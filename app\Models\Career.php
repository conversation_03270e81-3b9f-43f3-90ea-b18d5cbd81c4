<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Career extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'description',
        'requirements',
        'responsibilities',
        'benefits',
        'department',
        'location',
        'employment_type',
        'experience_level',
        'salary_min',
        'salary_max',
        'salary_currency',
        'is_active',
        'is_featured',
        'sort_order',
        'application_deadline',
        'required_skills',
        'preferred_skills',
        'contact_email',
        'contact_phone',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
        'application_deadline' => 'date',
        'required_skills' => 'array',
        'preferred_skills' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($career) {
            if (empty($career->slug)) {
                $career->slug = Str::slug($career->title);
            }
        });

        static::updating(function ($career) {
            if ($career->isDirty('title') && empty($career->slug)) {
                $career->slug = Str::slug($career->title);
            }
        });
    }

    public function jobApplications(): HasMany
    {
        return $this->hasMany(JobApplication::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByDepartment($query, $department)
    {
        return $query->where('department', $department);
    }

    public function getFormattedSalaryAttribute()
    {
        if (!$this->salary_min && !$this->salary_max) {
            return 'Competitive';
        }

        // Use CurrencyHelper for proper formatting
        if ($this->salary_min && $this->salary_max) {
            return \App\Helpers\CurrencyHelper::format($this->salary_min) . " - " . \App\Helpers\CurrencyHelper::format($this->salary_max);
        }

        if ($this->salary_min) {
            return \App\Helpers\CurrencyHelper::format($this->salary_min) . "+";
        }

        return \App\Helpers\CurrencyHelper::format($this->salary_max);
    }

    public function getApplicationsCountAttribute()
    {
        return $this->jobApplications()->count();
    }

    public function isApplicationDeadlinePassed()
    {
        return $this->application_deadline && $this->application_deadline->isPast();
    }

    /**
     * Get contact email with fallback to site settings
     */
    public function getContactEmailAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        return \App\Models\SiteSetting::getValue('careers_contact_email',
            \App\Models\SiteSetting::getValue('contact_email', '<EMAIL>')
        );
    }

    /**
     * Get contact phone with fallback to site settings
     */
    public function getContactPhoneAttribute($value): string
    {
        if ($value) {
            return $value;
        }

        return \App\Models\SiteSetting::getValue('careers_contact_phone',
            \App\Models\SiteSetting::getValue('contact_phone', '+****************')
        );
    }
}
