<?php

namespace App\Http\Middleware;

use App\Services\SeoLocalizationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LocaleMiddleware
{
    protected $seoService;

    public function __construct(SeoLocalizationService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip locale detection for admin routes
        if ($request->is('admin/*') || $request->is('api/*')) {
            return $next($request);
        }

        // Get the current locale
        $locale = $this->seoService->getCurrentLocale();
        
        // Set the application locale
        $language = explode('-', $locale)[0];
        App::setLocale($language);
        
        // Store locale in session for future requests
        Session::put('locale', $locale);
        
        // Share locale data with views
        view()->share('currentLocale', $locale);
        view()->share('currentLanguage', $language);

        return $next($request);
    }
}
