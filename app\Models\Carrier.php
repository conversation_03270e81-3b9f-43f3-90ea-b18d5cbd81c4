<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Carrier extends Model
{
    protected $fillable = [
        'name',
        'code',
        'type',
        'description',
        'logo',
        'website',
        'contact_email',
        'contact_phone',
        'api_credentials',
        'service_types',
        'base_rate',
        'per_kg_rate',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'api_credentials' => 'array',
        'service_types' => 'array',
        'base_rate' => 'decimal:2',
        'per_kg_rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get all parcels for this carrier
     */
    public function parcels(): HasMany
    {
        return $this->hasMany(Parcel::class);
    }

    /**
     * Scope to get only active carriers
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Check if carrier is a third-party service
     */
    public function isThirdParty(): bool
    {
        return in_array($this->type, ['dhl', 'post_office', 'fedex', 'ups']);
    }

    /**
     * Get available service types for this carrier
     */
    public function getAvailableServiceTypes(): array
    {
        return $this->service_types ?? ['standard', 'express'];
    }
}
