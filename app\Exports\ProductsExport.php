<?php

namespace App\Exports;

use App\Models\Product;
use App\Helpers\CurrencyHelper;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Http\Request;

class ProductsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Product::with(['category']);

        // Apply filters if request is provided
        if ($this->request) {
            if ($this->request->filled('category_id')) {
                $query->where('category_id', $this->request->category_id);
            }

            if ($this->request->filled('status')) {
                $query->where('is_active', $this->request->status === 'active');
            }

            if ($this->request->filled('stock_status')) {
                switch ($this->request->stock_status) {
                    case 'in_stock':
                        $query->where('manage_stock', true)
                              ->where('stock_quantity', '>', 0)
                              ->whereColumn('stock_quantity', '>', 'min_stock_level');
                        break;
                    case 'low_stock':
                        $query->where('manage_stock', true)
                              ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                              ->where('stock_quantity', '>', 0);
                        break;
                    case 'out_of_stock':
                        $query->where('manage_stock', true)
                              ->where('stock_quantity', 0);
                        break;
                }
            }

            if ($this->request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $this->request->date_from);
            }

            if ($this->request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $this->request->date_to);
            }
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Product Name',
            'SKU',
            'Category',
            'Price (' . CurrencyHelper::getCode() . ')',
            'Cost Price (' . CurrencyHelper::getCode() . ')',
            'Stock Quantity',
            'Min Stock Level',
            'Stock Status',
            'Status',
            'Featured',
            'Manage Stock',
            'Weight (kg)',
            'Dimensions',
            'Total Sales',
            'Created Date',
            'Updated Date',
        ];
    }

    /**
     * @param mixed $product
     * @return array
     */
    public function map($product): array
    {
        // Determine stock status
        $stockStatus = 'N/A';
        if ($product->manage_stock) {
            if ($product->stock_quantity == 0) {
                $stockStatus = 'Out of Stock';
            } elseif ($product->stock_quantity <= $product->min_stock_level) {
                $stockStatus = 'Low Stock';
            } else {
                $stockStatus = 'In Stock';
            }
        }

        // Format dimensions
        $dimensions = '';
        if ($product->length || $product->width || $product->height) {
            $dimensions = "{$product->length} x {$product->width} x {$product->height}";
        }

        return [
            $product->name,
            $product->sku,
            $product->category->name ?? 'Uncategorized',
            $product->price,
            $product->cost_price,
            $product->manage_stock ? $product->stock_quantity : 'N/A',
            $product->manage_stock ? $product->min_stock_level : 'N/A',
            $stockStatus,
            $product->is_active ? 'Active' : 'Inactive',
            $product->is_featured ? 'Yes' : 'No',
            $product->manage_stock ? 'Yes' : 'No',
            $product->weight,
            $dimensions,
            $product->total_sales ?? 0,
            $product->created_at->format('Y-m-d H:i:s'),
            $product->updated_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
            // Add borders to all cells
            'A1:P1000' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
            ],
        ];
    }
}
