<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class ContactController extends Controller
{
    /**
     * Display a listing of contact submissions
     */
    public function index(Request $request): View
    {
        $query = Contact::with('repliedBy')->latest();

        // Apply filters
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $contacts = $query->paginate(20);

        // Get statistics
        $stats = [
            'total' => Contact::count(),
            'new' => Contact::byStatus('new')->count(),
            'read' => Contact::byStatus('read')->count(),
            'replied' => Contact::byStatus('replied')->count(),
            'closed' => Contact::byStatus('closed')->count(),
        ];

        return view('admin.communications.contacts.index', compact('contacts', 'stats'));
    }

    /**
     * Display the specified contact
     */
    public function show(Contact $contact): View
    {
        // Mark as read when viewed
        $contact->markAsRead();

        return view('admin.communications.contacts.show', compact('contact'));
    }

    /**
     * Update contact status and notes
     */
    public function update(Request $request, Contact $contact): RedirectResponse
    {
        $validated = $request->validate([
            'status' => 'required|in:new,read,replied,closed',
            'admin_notes' => 'nullable|string|max:2000',
        ]);

        $contact->update($validated);

        if ($validated['status'] === 'replied') {
            $contact->markAsReplied(auth()->id());
        }

        return redirect()->back()->with('success', 'Contact updated successfully.');
    }

    /**
     * Delete a contact submission
     */
    public function destroy(Contact $contact): RedirectResponse
    {
        $contact->delete();

        return redirect()->route('admin.communications.contacts.index')
                        ->with('success', 'Contact deleted successfully.');
    }

    /**
     * Mark contact as read (AJAX)
     */
    public function markAsRead(Contact $contact): JsonResponse
    {
        $contact->markAsRead();

        return response()->json(['success' => true]);
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:mark_read,mark_replied,mark_closed,delete',
            'contacts' => 'required|array',
            'contacts.*' => 'exists:contacts,id',
        ]);

        $contacts = Contact::whereIn('id', $validated['contacts']);
        $count = $contacts->count();

        switch ($validated['action']) {
            case 'mark_read':
                $contacts->update(['status' => 'read', 'read_at' => now()]);
                $message = "{$count} contacts marked as read.";
                break;
            case 'mark_replied':
                $contacts->update([
                    'status' => 'replied',
                    'replied_at' => now(),
                    'replied_by' => auth()->id()
                ]);
                $message = "{$count} contacts marked as replied.";
                break;
            case 'mark_closed':
                $contacts->update(['status' => 'closed']);
                $message = "{$count} contacts marked as closed.";
                break;
            case 'delete':
                $contacts->delete();
                $message = "{$count} contacts deleted.";
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
