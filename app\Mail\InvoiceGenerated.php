<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\SiteSetting;
use App\Services\DocumentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class InvoiceGenerated extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected Order $order;
    protected array $siteSettings;
    protected ?string $invoicePdfPath = null;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, bool $attachPdf = true)
    {
        $this->order = $order;
        $this->siteSettings = cache()->remember('site_settings', 3600, function () {
            return SiteSetting::pluck('value', 'key_name')->toArray();
        });

        // Generate PDF if requested
        if ($attachPdf) {
            try {
                $documentService = app(DocumentGenerationService::class);
                $this->invoicePdfPath = $documentService->generateInvoice($this->order);
            } catch (\Exception $e) {
                // Log error but don't fail email sending
                logger()->error('Failed to generate invoice PDF for email', [
                    'order_id' => $this->order->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $siteName = $this->siteSettings['site_name'] ?? config('app.name');
        
        return new Envelope(
            subject: "Invoice #{$this->order->order_number} - {$siteName}",
            from: $this->siteSettings['notification_email'] ?? config('mail.from.address'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.invoice-generated',
            with: [
                'order' => $this->order,
                'siteSettings' => $this->siteSettings,
                'orderUrl' => route('customer.orders.show', $this->order->id),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];

        if ($this->invoicePdfPath && Storage::disk('public')->exists($this->invoicePdfPath)) {
            $attachments[] = Attachment::fromStorageDisk('public', $this->invoicePdfPath)
                ->as("invoice_{$this->order->order_number}.pdf")
                ->withMime('application/pdf');
        }

        return $attachments;
    }
}
