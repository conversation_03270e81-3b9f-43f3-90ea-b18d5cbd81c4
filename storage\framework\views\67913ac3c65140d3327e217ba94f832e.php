<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => 'Page Title',
    'subtitle' => '',
    'description' => '',
    'breadcrumbs' => [],
    'gradient' => 'from-gray-900 via-gray-800 to-green-900',
    'showScrollIndicator' => true
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => 'Page Title',
    'subtitle' => '',
    'description' => '',
    'breadcrumbs' => [],
    'gradient' => 'from-gray-900 via-gray-800 to-green-900',
    'showScrollIndicator' => true
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!-- Hero Section with Animated Background -->
<section class="relative min-h-[60vh] flex items-center justify-center overflow-hidden bg-gradient-to-br <?php echo e($gradient); ?>">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0">
        <!-- Floating Particles -->
        <div class="absolute w-2 h-2 bg-green-400 rounded-full animate-float" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
        <div class="absolute w-3 h-3 bg-blue-400 rounded-full animate-float" style="top: 60%; left: 80%; animation-delay: 2s;"></div>
        <div class="absolute w-1 h-1 bg-white rounded-full animate-float" style="top: 40%; left: 20%; animation-delay: 1s;"></div>
        <div class="absolute w-2 h-2 bg-green-300 rounded-full animate-float" style="top: 80%; left: 60%; animation-delay: 3s;"></div>
        <div class="absolute w-1 h-1 bg-blue-300 rounded-full animate-float" style="top: 30%; left: 90%; animation-delay: 1.5s;"></div>
        
        <!-- Gradient Orbs -->
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-green-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>
    
    <!-- Content -->
    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <div class="animate-on-scroll">
                <h1 class="text-5xl lg:text-7xl font-bold font-heading text-white mb-6">
                    <?php echo $title; ?>

                </h1>
                
                <?php if($subtitle): ?>
                <p class="text-xl lg:text-2xl text-gray-300 mb-8">
                    <?php echo e($subtitle); ?>

                </p>
                <?php endif; ?>
                
                <?php if($description): ?>
                <p class="text-lg text-gray-400 mb-8 max-w-2xl mx-auto">
                    <?php echo e($description); ?>

                </p>
                <?php endif; ?>
                
                <!-- Breadcrumb -->
                <?php if(count($breadcrumbs) > 0): ?>
                <nav class="flex justify-center items-center space-x-2 text-gray-400">
                    <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($index > 0): ?>
                            <i class="fas fa-chevron-right text-xs"></i>
                        <?php endif; ?>
                        
                        <?php if(isset($breadcrumb['url']) && !$loop->last): ?>
                            <a href="<?php echo e($breadcrumb['url']); ?>" class="hover:text-green-400 transition-colors">
                                <?php echo e($breadcrumb['title']); ?>

                            </a>
                        <?php else: ?>
                            <span class="text-green-400"><?php echo e($breadcrumb['title']); ?></span>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Scroll Indicator -->
    <?php if($showScrollIndicator): ?>
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
    </div>
    <?php endif; ?>
</section>

<?php $__env->startPush('styles'); ?>
<style>
/* Custom animations for magical effects */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Gradient text animation */
@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Floating particles animation
function createFloatingParticle() {
    const heroSection = document.querySelector('.bg-gradient-to-br');
    if (!heroSection) return;
    
    const particle = document.createElement('div');
    particle.className = 'absolute w-1 h-1 bg-white rounded-full opacity-30';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = '100%';
    particle.style.animation = `float ${3 + Math.random() * 4}s linear infinite`;
    
    heroSection.appendChild(particle);
    
    setTimeout(() => {
        if (particle.parentNode) {
            particle.remove();
        }
    }, 7000);
}

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    initScrollAnimations();
    
    // Create particles periodically
    setInterval(createFloatingParticle, 2000);
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/components/page-hero.blade.php ENDPATH**/ ?>