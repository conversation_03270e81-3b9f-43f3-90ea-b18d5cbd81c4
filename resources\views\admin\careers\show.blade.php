@extends('layouts.admin')

@section('title', 'Career Position Details')
@section('page-title', $career->title)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.careers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Careers
        </a>
        <a href="{{ route('admin.cms.careers.edit', $career) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Position
        </a>
        <a href="{{ route('careers.show', $career->slug) }}" target="_blank" class="btn btn-outline-info">
            <i class="fas fa-external-link-alt me-1"></i> View on Website
        </a>
        <button type="button" class="btn btn-outline-warning" onclick="toggleStatus()">
            <i class="fas fa-{{ $career->is_active ? 'eye-slash' : 'eye' }} me-1"></i> 
            {{ $career->is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8 mb-4">
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Position Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <strong>Position Title:</strong>
                                <p class="mb-0">{{ $career->title }}</p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <strong>Department:</strong>
                                <p class="mb-0">{{ $career->department ?? 'Not specified' }}</p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <strong>Location:</strong>
                                <p class="mb-0">{{ $career->location }}</p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <strong>Employment Type:</strong>
                                <p class="mb-0">{{ ucfirst(str_replace('-', ' ', $career->employment_type)) }}</p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <strong>Experience Level:</strong>
                                <p class="mb-0">{{ ucfirst($career->experience_level) }}</p>
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>Job Description:</strong>
                            <div class="mt-2 p-3 bg-light rounded">
                                {!! nl2br(e($career->description)) !!}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Information -->
                @if($career->requirements || $career->responsibilities || $career->benefits)
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Detailed Information</h6>
                        </div>
                        <div class="card-body">
                            @if($career->requirements)
                                <div class="mb-4">
                                    <strong>Requirements:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        {!! nl2br(e($career->requirements)) !!}
                                    </div>
                                </div>
                            @endif

                            @if($career->responsibilities)
                                <div class="mb-4">
                                    <strong>Key Responsibilities:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        {!! nl2br(e($career->responsibilities)) !!}
                                    </div>
                                </div>
                            @endif

                            @if($career->benefits)
                                <div class="mb-0">
                                    <strong>Benefits & Perks:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        {!! nl2br(e($career->benefits)) !!}
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Skills -->
                @if($career->required_skills || $career->preferred_skills)
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Skills</h6>
                        </div>
                        <div class="card-body">
                            @if($career->required_skills && count($career->required_skills) > 0)
                                <div class="mb-3">
                                    <strong>Required Skills:</strong>
                                    <div class="mt-2">
                                        @foreach($career->required_skills as $skill)
                                            <span class="badge bg-primary me-1 mb-1">{{ $skill }}</span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            @if($career->preferred_skills && count($career->preferred_skills) > 0)
                                <div class="mb-0">
                                    <strong>Preferred Skills:</strong>
                                    <div class="mt-2">
                                        @foreach($career->preferred_skills as $skill)
                                            <span class="badge bg-success me-1 mb-1">{{ $skill }}</span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Recent Applications -->
                @if($career->jobApplications->count() > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Applications</h6>
                            <a href="{{ route('admin.cms.job-applications.index', ['career' => $career->id]) }}" class="btn btn-sm btn-outline-primary">
                                View All Applications
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Applicant</th>
                                            <th>Applied Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($career->jobApplications->take(5) as $application)
                                            <tr>
                                                <td>
                                                    <div class="font-weight-bold">{{ $application->full_name }}</div>
                                                    <small class="text-muted">{{ $application->email }}</small>
                                                </td>
                                                <td>{{ $application->created_at->format('M d, Y') }}</td>
                                                <td>
                                                    <span class="badge {{ $application->status_badge_class }}">
                                                        {{ $application->status_label }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="{{ route('admin.cms.job-applications.show', $application) }}" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Position Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Position Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Status:</strong>
                            <p class="mb-0">
                                <span class="badge bg-{{ $career->is_active ? 'success' : 'secondary' }}">
                                    {{ $career->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                @if($career->is_featured)
                                    <span class="badge bg-warning ms-1">Featured</span>
                                @endif
                            </p>
                        </div>

                        <div class="mb-3">
                            <strong>Created:</strong>
                            <p class="mb-0">{{ $career->created_at->format('M d, Y h:i A') }}</p>
                        </div>

                        <div class="mb-3">
                            <strong>Last Updated:</strong>
                            <p class="mb-0">{{ $career->updated_at->format('M d, Y h:i A') }}</p>
                        </div>

                        <div class="mb-3">
                            <strong>Salary Range:</strong>
                            <p class="mb-0">{{ $career->formatted_salary }}</p>
                        </div>

                        @if($career->application_deadline)
                            <div class="mb-3">
                                <strong>Application Deadline:</strong>
                                <p class="mb-0 {{ $career->isApplicationDeadlinePassed() ? 'text-danger' : 'text-success' }}">
                                    {{ $career->application_deadline->format('M d, Y') }}
                                    @if($career->isApplicationDeadlinePassed())
                                        <br><small class="text-danger">Deadline passed</small>
                                    @endif
                                </p>
                            </div>
                        @endif

                        <div class="mb-3">
                            <strong>Total Applications:</strong>
                            <p class="mb-0">
                                <span class="badge bg-primary fs-6">{{ $career->jobApplications->count() }}</span>
                            </p>
                        </div>

                        <div class="mb-0">
                            <strong>Sort Order:</strong>
                            <p class="mb-0">{{ $career->sort_order ?? 0 }}</p>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                @if($career->contact_email || $career->contact_phone)
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                        </div>
                        <div class="card-body">
                            @if($career->contact_email)
                                <div class="mb-3">
                                    <strong>Contact Email:</strong>
                                    <p class="mb-0">
                                        <a href="mailto:{{ $career->contact_email }}">{{ $career->contact_email }}</a>
                                    </p>
                                </div>
                            @endif

                            @if($career->contact_phone)
                                <div class="mb-0">
                                    <strong>Contact Phone:</strong>
                                    <p class="mb-0">
                                        <a href="tel:{{ $career->contact_phone }}">{{ $career->contact_phone }}</a>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.cms.careers.edit', $career) }}" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i> Edit Position
                            </a>
                            <a href="{{ route('careers.show', $career->slug) }}" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-external-link-alt me-2"></i> View on Website
                            </a>
                            @if($career->jobApplications->count() > 0)
                                <a href="{{ route('admin.cms.job-applications.index', ['career' => $career->id]) }}" class="btn btn-outline-success">
                                    <i class="fas fa-users me-2"></i> View Applications
                                </a>
                            @endif
                            <button type="button" class="btn btn-outline-warning" onclick="toggleStatus()">
                                <i class="fas fa-{{ $career->is_active ? 'eye-slash' : 'eye' }} me-2"></i> 
                                {{ $career->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the career position <strong>{{ $career->title }}</strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone and will also delete all associated job applications.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.cms.careers.destroy', $career) }}" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Position</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function confirmDelete() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.cms.careers.toggle-status", $career) }}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
</script>
@endpush
