@extends('layouts.admin')

@section('title', 'Team Member Details')
@section('page-title', 'Team Member: ' . $teamMember->name)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.team.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Team
        </a>
        <a href="{{ route('admin.cms.team.edit', $teamMember) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Member
        </a>
        <button type="button" class="btn btn-outline-info" onclick="toggleStatus()">
            <i class="fas fa-{{ $teamMember->is_active ? 'eye-slash' : 'eye' }} me-1"></i> 
            {{ $teamMember->is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-3">
                        @if($teamMember->photo)
                            <img src="{{ Storage::url($teamMember->photo) }}" 
                                 alt="{{ $teamMember->name }}" 
                                 class="rounded-circle img-thumbnail" 
                                 style="width: 200px; height: 200px; object-fit: cover;">
                        @else
                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto" 
                                 style="width: 200px; height: 200px;">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                        @endif
                    </div>
                    
                    <h4 class="card-title mb-1">{{ $teamMember->name }}</h4>
                    <p class="text-muted mb-2">{{ $teamMember->position }}</p>
                    @if($teamMember->department)
                        <p class="text-muted small mb-3">{{ $teamMember->department }}</p>
                    @endif
                    
                    <div class="mb-3">
                        <span class="badge bg-{{ $teamMember->is_active ? 'success' : 'secondary' }} fs-6">
                            {{ $teamMember->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    <!-- Contact Information -->
                    @if($teamMember->email || $teamMember->phone)
                        <div class="d-flex justify-content-center gap-2 mb-3">
                            @if($teamMember->email)
                                <a href="mailto:{{ $teamMember->email }}" class="btn btn-outline-primary btn-sm" title="Email">
                                    <i class="fas fa-envelope"></i>
                                </a>
                            @endif
                            @if($teamMember->phone)
                                <a href="tel:{{ $teamMember->phone }}" class="btn btn-outline-success btn-sm" title="Phone">
                                    <i class="fas fa-phone"></i>
                                </a>
                            @endif
                        </div>
                    @endif
                    
                    <!-- Social Media Links -->
                    @if($teamMember->social_links && count($teamMember->social_links) > 0)
                        <div class="d-flex justify-content-center gap-2">
                            @foreach($teamMember->social_links as $platform => $url)
                                @if($url)
                                    <a href="{{ $url }}" target="_blank" class="btn btn-outline-info btn-sm" title="{{ ucfirst($platform) }}">
                                        <i class="fab fa-{{ $platform }}"></i>
                                    </a>
                                @endif
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.cms.team.edit', $teamMember) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i> Edit Member
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="toggleStatus()">
                            <i class="fas fa-{{ $teamMember->is_active ? 'eye-slash' : 'eye' }} me-2"></i> 
                            {{ $teamMember->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                        @if($teamMember->email)
                            <a href="mailto:{{ $teamMember->email }}" class="btn btn-outline-success">
                                <i class="fas fa-envelope me-2"></i> Send Email
                            </a>
                        @endif
                        <hr>
                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash me-2"></i> Delete Member
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- Member Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Member Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Full Name:</strong>
                            <p class="mb-0">{{ $teamMember->name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>Position:</strong>
                            <p class="mb-0">{{ $teamMember->position }}</p>
                        </div>
                    </div>
                    
                    @if($teamMember->department)
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>Department:</strong>
                                <p class="mb-0">{{ $teamMember->department }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>Display Order:</strong>
                                <p class="mb-0">{{ $teamMember->sort_order }}</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($teamMember->bio)
                        <div class="mb-3">
                            <strong>Biography:</strong>
                            <p class="mb-0">{{ $teamMember->bio }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Contact Information -->
            @if($teamMember->email || $teamMember->phone)
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @if($teamMember->email)
                                <div class="col-md-6 mb-3">
                                    <strong>Email Address:</strong>
                                    <p class="mb-0">
                                        <a href="mailto:{{ $teamMember->email }}" class="text-decoration-none">
                                            {{ $teamMember->email }}
                                        </a>
                                    </p>
                                </div>
                            @endif
                            @if($teamMember->phone)
                                <div class="col-md-6 mb-3">
                                    <strong>Phone Number:</strong>
                                    <p class="mb-0">
                                        <a href="tel:{{ $teamMember->phone }}" class="text-decoration-none">
                                            {{ $teamMember->phone }}
                                        </a>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Social Media -->
            @if($teamMember->social_links && count($teamMember->social_links) > 0)
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Social Media Profiles</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($teamMember->social_links as $platform => $url)
                                @if($url)
                                    <div class="col-md-6 mb-3">
                                        <strong>{{ ucfirst($platform) }}:</strong>
                                        <p class="mb-0">
                                            <a href="{{ $url }}" target="_blank" class="text-decoration-none">
                                                <i class="fab fa-{{ $platform }} me-1"></i>
                                                {{ $url }}
                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                            </a>
                                        </p>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- System Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Status:</strong>
                            <p class="mb-0">
                                <span class="badge bg-{{ $teamMember->is_active ? 'success' : 'secondary' }}">
                                    {{ $teamMember->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>Display Order:</strong>
                            <p class="mb-0">{{ $teamMember->sort_order }}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Added:</strong>
                            <p class="mb-0">{{ $teamMember->created_at->format('M d, Y h:i A') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>Last Updated:</strong>
                            <p class="mb-0">{{ $teamMember->updated_at->format('M d, Y h:i A') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete team member <strong>"{{ $teamMember->name }}"</strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.cms.team.destroy', $teamMember) }}" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Team Member</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function confirmDelete() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus() {
        if (confirm('Are you sure you want to {{ $teamMember->is_active ? 'deactivate' : 'activate' }} {{ $teamMember->name }}?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/cms/team/{{ $teamMember->id }}/toggle-status';
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endpush
