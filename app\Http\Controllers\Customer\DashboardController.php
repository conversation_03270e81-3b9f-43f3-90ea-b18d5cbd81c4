<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\Order;
use App\Models\Product;
use App\Models\Cart;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    /**
     * Show customer dashboard
     */
    public function index(): View
    {
        $user = Auth::user();

        // Get customer statistics
        $stats = [
            // Parcel statistics
            'total_parcels' => $user->parcels()->count(),
            'pending_parcels' => $user->parcels()->where('status', 'pending')->count(),
            'in_transit_parcels' => $user->parcels()->whereIn('status', ['picked_up', 'in_transit', 'out_for_delivery'])->count(),
            'delivered_parcels' => $user->parcels()->where('status', 'delivered')->count(),
            'total_spent_parcels' => $user->parcels()->where('is_paid', true)->sum('total_cost'),
            'unpaid_amount' => $user->parcels()->where('is_paid', false)->sum('total_cost'),

            // Order statistics
            'total_orders' => $user->orders()->count(),
            'pending_orders' => $user->orders()->where('status', 'pending')->count(),
            'processing_orders' => $user->orders()->whereIn('status', ['confirmed', 'processing'])->count(),
            'shipped_orders' => $user->orders()->where('status', 'shipped')->count(),
            'delivered_orders' => $user->orders()->where('status', 'delivered')->count(),
            'total_spent_orders' => $user->orders()->where('payment_status', 'paid')->sum('total_amount'),
            'pending_payments' => $user->orders()->where('payment_status', 'pending')->sum('total_amount'),
            'average_order_value' => $user->orders()->where('payment_status', 'paid')->avg('total_amount') ?? 0,
        ];

        // Get recent parcels
        $recentParcels = $user->parcels()
                             ->with(['carrier', 'trackingEvents' => function($query) {
                                 $query->latest()->limit(1);
                             }])
                             ->latest()
                             ->limit(5)
                             ->get();

        // Get parcels by status for chart
        $parcelsByStatus = $user->parcels()
                               ->selectRaw('status, COUNT(*) as count')
                               ->groupBy('status')
                               ->pluck('count', 'status')
                               ->toArray();

        // Get monthly spending for chart (last 6 months)
        $monthlySpending = $user->parcels()
                               ->where('is_paid', true)
                               ->where('created_at', '>=', now()->subMonths(6))
                               ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(total_cost) as total')
                               ->groupBy('year', 'month')
                               ->orderBy('year')
                               ->orderBy('month')
                               ->get()
                               ->map(function($item) {
                                   return [
                                       'period' => date('M Y', mktime(0, 0, 0, $item->month, 1, $item->year)),
                                       'total' => $item->total
                                   ];
                               });

        // Get user's cart
        $cart = Cart::getCurrent();
        $cart->load(['items.product']);

        return view('customer.dashboard', compact('stats', 'recentParcels', 'parcelsByStatus', 'monthlySpending', 'cart'));
    }

    /**
     * Show customer parcels
     */
    public function parcels(Request $request): View
    {
        $user = Auth::user();

        $query = $user->parcels()->with(['carrier', 'trackingEvents' => function($q) {
            $q->latest()->limit(1);
        }]);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('carrier')) {
            $query->where('carrier_id', $request->carrier);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('tracking_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('recipient_name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $parcels = $query->latest()->paginate(15);

        // Get filter options
        $carriers = \App\Models\Carrier::orderBy('name')->get();
        $statuses = [
            'pending' => 'Pending',
            'picked_up' => 'Picked Up',
            'in_transit' => 'In Transit',
            'out_for_delivery' => 'Out for Delivery',
            'delivered' => 'Delivered',
            'exception' => 'Exception',
            'returned' => 'Returned',
        ];

        return view('customer.parcels', compact('parcels', 'carriers', 'statuses'));
    }

    /**
     * Show single parcel details
     */
    public function parcelDetails(Parcel $parcel): View
    {
        // Ensure the parcel belongs to the authenticated customer
        if ($parcel->user_id !== Auth::id()) {
            abort(404);
        }

        $parcel->load(['carrier', 'trackingEvents' => function($query) {
            $query->orderBy('event_date', 'desc');
        }]);

        return view('customer.parcel-details', compact('parcel'));
    }

    /**
     * Track parcel by tracking number
     */
    public function trackParcel(Request $request)
    {
        $trackingNumber = $request->get('tracking_number');
        $parcel = null;

        if ($trackingNumber) {
            $parcel = Parcel::where('tracking_number', $trackingNumber)
                           ->with(['carrier', 'trackingEvents' => function($query) {
                               $query->orderBy('event_date', 'desc');
                           }])
                           ->first();

            // Add computed fields for the frontend
            if ($parcel) {
                $parcel->origin_city = $parcel->sender_city;
                $parcel->origin_country = $parcel->sender_country;
                $parcel->destination_city = $parcel->recipient_city;
                $parcel->destination_country = $parcel->recipient_country;
            }
        }

        // Handle AJAX requests
        if ($request->ajax()) {
            if ($parcel) {
                return response()->json([
                    'success' => true,
                    'parcel' => $parcel,
                    'message' => 'Package found successfully.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'parcel' => null,
                    'message' => 'Package not found. Please check your tracking number and try again.'
                ]);
            }
        }

        // Handle regular requests
        return view('customer.track', compact('parcel', 'trackingNumber'));
    }

    /**
     * Show customer orders
     */
    public function orders(Request $request): View
    {
        $user = Auth::user();

        $query = $user->orders()->with(['items.product']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('items', function($itemQuery) use ($search) {
                      $itemQuery->where('product_name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $orders = $query->latest()->paginate(15);

        // Get filter options
        $statuses = [
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',
            'processing' => 'Processing',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
        ];

        $paymentStatuses = [
            'pending' => 'Pending',
            'paid' => 'Paid',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
        ];

        return view('customer.orders', compact('orders', 'statuses', 'paymentStatuses'));
    }

    /**
     * Show single order details
     */
    public function orderDetails(Order $order): View
    {
        // Ensure the order belongs to the authenticated customer
        if ($order->customer_id !== Auth::id()) {
            abort(404);
        }

        $order->load(['items.product']);

        return view('customer.order-details', compact('order'));
    }

    /**
     * Cancel an order
     */
    public function cancelOrder(Order $order): RedirectResponse
    {
        // Ensure the order belongs to the authenticated customer
        if ($order->customer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        // Check if order can be cancelled
        if (!$order->canBeCancelled()) {
            return redirect()->route('customer.orders.show', $order)
                           ->with('error', 'This order cannot be cancelled at this time.');
        }

        try {
            DB::beginTransaction();

            // Update order status
            $order->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancellation_reason' => 'Cancelled by customer',
            ]);

            // If order was paid, create a refund request
            if ($order->isPaid()) {
                // Create a refund record or notification for admin
                // This would typically integrate with payment gateway refund APIs

                // For now, we'll just update the payment status
                $order->update([
                    'payment_status' => 'refund_pending',
                ]);

                // You could also create a refund record here
                // Refund::create([...]);
            }

            // Restore inventory if needed
            foreach ($order->items as $item) {
                if ($item->product && $item->product->manage_stock) {
                    $item->product->increment('stock_quantity', $item->quantity);
                }
            }

            DB::commit();

            return redirect()->route('customer.orders.show', $order)
                           ->with('success', 'Order has been cancelled successfully. ' .
                                  ($order->isPaid() ? 'A refund will be processed within 3-5 business days.' : ''));

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order cancellation failed', [
                'order_id' => $order->id,
                'customer_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('customer.orders.show', $order)
                           ->with('error', 'Failed to cancel order. Please contact support.');
        }
    }

    /**
     * Reorder items from an existing order
     */
    public function reorderItems(Order $order): RedirectResponse
    {
        // Ensure the order belongs to the authenticated customer
        if ($order->customer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        // Check if order can be reordered
        if (!$order->canBeReordered()) {
            return redirect()->route('customer.orders.show', $order)
                           ->with('error', 'This order cannot be reordered at this time.');
        }

        try {
            $addedItems = 0;
            $unavailableItems = [];

            foreach ($order->items as $item) {
                if ($item->product && $item->product->is_active) {
                    // Check stock availability
                    if ($item->product->manage_stock && $item->product->stock_quantity < $item->quantity) {
                        $unavailableItems[] = $item->product_name . ' (insufficient stock)';
                        continue;
                    }

                    // Add to cart (you'll need to implement cart functionality)
                    // For now, we'll just count the items that would be added
                    $addedItems++;
                } else {
                    $unavailableItems[] = $item->product_name . ' (no longer available)';
                }
            }

            if ($addedItems > 0) {
                $message = "Successfully added {$addedItems} item(s) to your cart.";
                if (count($unavailableItems) > 0) {
                    $message .= ' Some items were not available: ' . implode(', ', $unavailableItems);
                }

                return redirect()->route('customer.cart')
                               ->with('success', $message);
            } else {
                return redirect()->route('customer.orders.show', $order)
                               ->with('warning', 'No items could be added to cart. Items may no longer be available.');
            }

        } catch (\Exception $e) {
            Log::error('Order reorder failed', [
                'order_id' => $order->id,
                'customer_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->route('customer.orders.show', $order)
                           ->with('error', 'Failed to reorder items. Please try again.');
        }
    }
}
