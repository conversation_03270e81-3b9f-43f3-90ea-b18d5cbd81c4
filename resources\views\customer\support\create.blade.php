@extends('layouts.customer')

@section('title', 'Create Support Ticket')
@section('page-title', 'Create Support Ticket')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.support.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Tickets
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        New Support Ticket
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('customer.support.store') }}">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="subject" class="form-label">Subject *</label>
                                <input type="text" class="form-control @error('subject') is-invalid @enderror" 
                                       id="subject" name="subject" value="{{ old('subject') }}" 
                                       placeholder="Brief description of your issue" required>
                                @error('subject')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="priority" class="form-label">Priority *</label>
                                <select class="form-select @error('priority') is-invalid @enderror" 
                                        id="priority" name="priority" required>
                                    <option value="">Select Priority</option>
                                    @foreach($priorities as $value => $label)
                                        <option value="{{ $value }}" {{ old('priority') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('priority')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select @error('category') is-invalid @enderror" 
                                        id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    @foreach($categories as $value => $label)
                                        <option value="{{ $value }}" {{ old('category') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="order_number" class="form-label">Related Order (Optional)</label>
                                <select class="form-select @error('order_number') is-invalid @enderror" 
                                        id="order_number" name="order_number">
                                    <option value="">Select an order (if applicable)</option>
                                    @foreach($recentOrders as $order)
                                        <option value="{{ $order->order_number }}" 
                                                {{ old('order_number') === $order->order_number || request('order_number') === $order->order_number ? 'selected' : '' }}>
                                            Order #{{ $order->order_number }} - {{ $order->created_at->format('M d, Y') }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('order_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="parcel_tracking" class="form-label">Related Parcel Tracking (Optional)</label>
                            <select class="form-select @error('parcel_tracking') is-invalid @enderror" 
                                    id="parcel_tracking" name="parcel_tracking">
                                <option value="">Select a parcel (if applicable)</option>
                                @foreach($recentParcels as $parcel)
                                    <option value="{{ $parcel->tracking_number }}" 
                                            {{ old('parcel_tracking') === $parcel->tracking_number ? 'selected' : '' }}>
                                        {{ $parcel->tracking_number }} - {{ $parcel->created_at->format('M d, Y') }}
                                    </option>
                                @endforeach
                            </select>
                            @error('parcel_tracking')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="description" class="form-label">Description *</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="6" 
                                      placeholder="Please provide detailed information about your issue or question..." required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                Please be as specific as possible. Include any error messages, steps you've taken, 
                                and what you expected to happen.
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('customer.support.index') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                Submit Ticket
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Help & Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips for Better Support
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">Be Specific</h6>
                        <p class="small text-muted">
                            Provide detailed information about your issue, including any error messages you've seen.
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Include Context</h6>
                        <p class="small text-muted">
                            Tell us what you were trying to do when the problem occurred and what you expected to happen.
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-primary">Choose the Right Priority</h6>
                        <p class="small text-muted">
                            <strong>Urgent:</strong> Service is down or critical business impact<br>
                            <strong>High:</strong> Important feature not working<br>
                            <strong>Medium:</strong> General questions or minor issues<br>
                            <strong>Low:</strong> Feature requests or suggestions
                        </p>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-phone me-2"></i>
                        Other Ways to Reach Us
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Phone Support</h6>
                        <p class="mb-1"><strong>{{ \App\Models\SiteSetting::getValue('support_phone', '+****************') }}</strong></p>
                        <small class="text-muted">{{ \App\Models\SiteSetting::getValue('support_hours', 'Mon-Fri: 8AM-6PM EST') }}</small>
                    </div>

                    <div class="mb-3">
                        <h6>Email Support</h6>
                        <p class="mb-1"><strong>{{ \App\Models\SiteSetting::getValue('support_email', '<EMAIL>') }}</strong></p>
                        <small class="text-muted">{{ \App\Models\SiteSetting::getValue('support_response_time', 'Response within 24 hours') }}</small>
                    </div>

                    @if(\App\Models\SiteSetting::getValue('live_chat_enabled', true))
                    <div class="mb-3">
                        <h6>Live Chat</h6>
                        <p class="mb-1">Available on our website</p>
                        <small class="text-muted">{{ \App\Models\SiteSetting::getValue('live_chat_hours', 'Mon-Fri: 9AM-5PM EST') }}</small>
                    </div>
                    @endif
                </div>
            </div>

            <!-- FAQ -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        Frequently Asked Questions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How do I track my package?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <small>You can track your package using the tracking number provided in your order confirmation email. Visit our tracking page or use the "Track Package" feature in your dashboard.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    What are your shipping rates?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <small>Shipping rates depend on package size, weight, destination, and service level. Use our quote calculator for accurate pricing or contact us for bulk shipping discounts.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    How do I change my delivery address?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    <small>Contact us immediately if you need to change a delivery address. Changes may be possible if the package hasn't been dispatched yet. Additional fees may apply.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Auto-fill description based on category selection
    document.getElementById('category').addEventListener('change', function() {
        const category = this.value;
        const descriptionField = document.getElementById('description');
        
        if (category && !descriptionField.value) {
            let template = '';
            
            switch(category) {
                case 'technical':
                    template = 'I am experiencing a technical issue with:\n\nSteps to reproduce:\n1. \n2. \n3. \n\nExpected result:\n\nActual result:\n\nBrowser/Device information:';
                    break;
                case 'billing':
                    template = 'I have a question about my billing:\n\nOrder/Invoice number:\n\nIssue description:\n\nAmount in question:';
                    break;
                case 'shipping':
                    template = 'I have an issue with my shipment:\n\nTracking number:\n\nExpected delivery date:\n\nIssue description:';
                    break;
                case 'product':
                    template = 'I have a question about a product:\n\nProduct name/SKU:\n\nQuestion/Issue:';
                    break;
                default:
                    template = 'Please describe your issue or question in detail:';
            }
            
            descriptionField.value = template;
        }
    });

    // Character counter for description
    const descriptionField = document.getElementById('description');
    const maxLength = 5000;
    
    // Create character counter
    const counter = document.createElement('div');
    counter.className = 'form-text text-end';
    counter.id = 'char-counter';
    descriptionField.parentNode.appendChild(counter);
    
    function updateCounter() {
        const remaining = maxLength - descriptionField.value.length;
        counter.textContent = `${descriptionField.value.length}/${maxLength} characters`;
        counter.className = remaining < 100 ? 'form-text text-end text-warning' : 'form-text text-end text-muted';
    }
    
    descriptionField.addEventListener('input', updateCounter);
    updateCounter(); // Initial count
</script>
@endpush
