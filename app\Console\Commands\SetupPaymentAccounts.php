<?php

namespace App\Console\Commands;

use App\Models\SiteSetting;
use Illuminate\Console\Command;

class SetupPaymentAccounts extends Command
{
    protected $signature = 'payment:setup-accounts {--demo : Setup demo account details}';
    protected $description = 'Setup bank and PayPal account details for manual payments';

    public function handle()
    {
        if ($this->option('demo')) {
            $this->setupDemoAccounts();
        } else {
            $this->setupInteractiveAccounts();
        }

        return 0;
    }

    private function setupDemoAccounts()
    {
        $this->info('Setting up demo payment account details...');

        // Bank Account Demo Data
        $bankSettings = [
            'bank_name' => 'First National Bank',
            'bank_account_name' => 'Atrix Logistics LLC',
            'bank_account_number' => '**********',
            'bank_routing_number' => '*********',
            'bank_swift_code' => 'FNBAUS33',
            'bank_iban' => '***************************',
            'bank_branch_address' => '123 Banking Street, Financial District, New York, NY 10005',
            'bank_instructions' => 'Please include your tracking number in the transfer reference. Transfers typically take 1-3 business days to process.',
        ];

        // PayPal Account Demo Data
        $paypalSettings = [
            'paypal_email' => '<EMAIL>',
            'paypal_account_name' => 'Atrix Logistics',
            'paypal_account_type' => 'Business',
            'paypal_phone' => '+****************',
            'paypal_instructions' => 'Send payment as Friends & Family to avoid fees. Include your tracking number in the payment note.',
        ];

        // Update bank settings
        foreach ($bankSettings as $key => $value) {
            SiteSetting::updateOrCreate(
                ['key_name' => $key],
                ['value' => $value]
            );
        }

        // Update PayPal settings
        foreach ($paypalSettings as $key => $value) {
            SiteSetting::updateOrCreate(
                ['key_name' => $key],
                ['value' => $value]
            );
        }

        $this->info('✅ Demo payment account details have been set up successfully!');
        $this->info('');
        $this->info('Bank Account Details:');
        $this->info("- Bank: {$bankSettings['bank_name']}");
        $this->info("- Account: {$bankSettings['bank_account_number']}");
        $this->info("- Routing: {$bankSettings['bank_routing_number']}");
        $this->info('');
        $this->info('PayPal Account Details:');
        $this->info("- Email: {$paypalSettings['paypal_email']}");
        $this->info("- Type: {$paypalSettings['paypal_account_type']}");
        $this->info('');
        $this->info('You can view and modify these settings in the admin dashboard under CMS > Settings');
    }

    private function setupInteractiveAccounts()
    {
        $this->info('Setting up payment account details interactively...');
        $this->info('Leave fields blank to skip them.');
        $this->info('');

        // Bank Account Setup
        if ($this->confirm('Do you want to set up bank account details?')) {
            $this->info('Setting up bank account details:');
            
            $bankSettings = [
                'bank_name' => $this->ask('Bank Name'),
                'bank_account_name' => $this->ask('Account Holder Name'),
                'bank_account_number' => $this->ask('Account Number'),
                'bank_routing_number' => $this->ask('Routing Number (optional)'),
                'bank_swift_code' => $this->ask('SWIFT/BIC Code (optional)'),
                'bank_iban' => $this->ask('IBAN (optional)'),
                'bank_branch_address' => $this->ask('Bank Branch Address (optional)'),
                'bank_instructions' => $this->ask('Special Instructions (optional)'),
            ];

            foreach ($bankSettings as $key => $value) {
                if (!empty($value)) {
                    SiteSetting::updateOrCreate(
                        ['key_name' => $key],
                        ['value' => $value]
                    );
                }
            }

            $this->info('✅ Bank account details saved!');
        }

        // PayPal Account Setup
        if ($this->confirm('Do you want to set up PayPal account details?')) {
            $this->info('Setting up PayPal account details:');
            
            $paypalSettings = [
                'paypal_email' => $this->ask('PayPal Email Address'),
                'paypal_account_name' => $this->ask('PayPal Account Name'),
                'paypal_account_type' => $this->choice('Account Type', ['Personal', 'Business'], 1),
                'paypal_phone' => $this->ask('Phone Number (optional)'),
                'paypal_instructions' => $this->ask('Special Instructions (optional)'),
            ];

            foreach ($paypalSettings as $key => $value) {
                if (!empty($value)) {
                    SiteSetting::updateOrCreate(
                        ['key_name' => $key],
                        ['value' => $value]
                    );
                }
            }

            $this->info('✅ PayPal account details saved!');
        }

        $this->info('');
        $this->info('Payment account setup completed!');
        $this->info('You can view and modify these settings in the admin dashboard under CMS > Settings');
    }
}
