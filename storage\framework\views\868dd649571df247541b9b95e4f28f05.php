<?php $__env->startSection('title', 'Career Position Details'); ?>
<?php $__env->startSection('page-title', $career->title); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.cms.careers.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Careers
        </a>
        <a href="<?php echo e(route('admin.cms.careers.edit', $career)); ?>" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Position
        </a>
        <a href="<?php echo e(route('careers.show', $career->slug)); ?>" target="_blank" class="btn btn-outline-info">
            <i class="fas fa-external-link-alt me-1"></i> View on Website
        </a>
        <button type="button" class="btn btn-outline-warning" onclick="toggleStatus()">
            <i class="fas fa-<?php echo e($career->is_active ? 'eye-slash' : 'eye'); ?> me-1"></i> 
            <?php echo e($career->is_active ? 'Deactivate' : 'Activate'); ?>

        </button>
        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8 mb-4">
                <!-- Basic Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Position Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <strong>Position Title:</strong>
                                <p class="mb-0"><?php echo e($career->title); ?></p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <strong>Department:</strong>
                                <p class="mb-0"><?php echo e($career->department ?? 'Not specified'); ?></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <strong>Location:</strong>
                                <p class="mb-0"><?php echo e($career->location); ?></p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <strong>Employment Type:</strong>
                                <p class="mb-0"><?php echo e(ucfirst(str_replace('-', ' ', $career->employment_type))); ?></p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <strong>Experience Level:</strong>
                                <p class="mb-0"><?php echo e(ucfirst($career->experience_level)); ?></p>
                            </div>
                        </div>

                        <div class="mb-3">
                            <strong>Job Description:</strong>
                            <div class="mt-2 p-3 bg-light rounded">
                                <?php echo nl2br(e($career->description)); ?>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Information -->
                <?php if($career->requirements || $career->responsibilities || $career->benefits): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Detailed Information</h6>
                        </div>
                        <div class="card-body">
                            <?php if($career->requirements): ?>
                                <div class="mb-4">
                                    <strong>Requirements:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        <?php echo nl2br(e($career->requirements)); ?>

                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($career->responsibilities): ?>
                                <div class="mb-4">
                                    <strong>Key Responsibilities:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        <?php echo nl2br(e($career->responsibilities)); ?>

                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($career->benefits): ?>
                                <div class="mb-0">
                                    <strong>Benefits & Perks:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        <?php echo nl2br(e($career->benefits)); ?>

                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Skills -->
                <?php if($career->required_skills || $career->preferred_skills): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Skills</h6>
                        </div>
                        <div class="card-body">
                            <?php if($career->required_skills && count($career->required_skills) > 0): ?>
                                <div class="mb-3">
                                    <strong>Required Skills:</strong>
                                    <div class="mt-2">
                                        <?php $__currentLoopData = $career->required_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge bg-primary me-1 mb-1"><?php echo e($skill); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if($career->preferred_skills && count($career->preferred_skills) > 0): ?>
                                <div class="mb-0">
                                    <strong>Preferred Skills:</strong>
                                    <div class="mt-2">
                                        <?php $__currentLoopData = $career->preferred_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge bg-success me-1 mb-1"><?php echo e($skill); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Recent Applications -->
                <?php if($career->jobApplications->count() > 0): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">Recent Applications</h6>
                            <a href="<?php echo e(route('admin.cms.job-applications.index', ['career' => $career->id])); ?>" class="btn btn-sm btn-outline-primary">
                                View All Applications
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Applicant</th>
                                            <th>Applied Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $career->jobApplications->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $application): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <div class="font-weight-bold"><?php echo e($application->full_name); ?></div>
                                                    <small class="text-muted"><?php echo e($application->email); ?></small>
                                                </td>
                                                <td><?php echo e($application->created_at->format('M d, Y')); ?></td>
                                                <td>
                                                    <span class="badge <?php echo e($application->status_badge_class); ?>">
                                                        <?php echo e($application->status_label); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="<?php echo e(route('admin.cms.job-applications.show', $application)); ?>" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Position Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Position Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Status:</strong>
                            <p class="mb-0">
                                <span class="badge bg-<?php echo e($career->is_active ? 'success' : 'secondary'); ?>">
                                    <?php echo e($career->is_active ? 'Active' : 'Inactive'); ?>

                                </span>
                                <?php if($career->is_featured): ?>
                                    <span class="badge bg-warning ms-1">Featured</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <div class="mb-3">
                            <strong>Created:</strong>
                            <p class="mb-0"><?php echo e($career->created_at->format('M d, Y h:i A')); ?></p>
                        </div>

                        <div class="mb-3">
                            <strong>Last Updated:</strong>
                            <p class="mb-0"><?php echo e($career->updated_at->format('M d, Y h:i A')); ?></p>
                        </div>

                        <div class="mb-3">
                            <strong>Salary Range:</strong>
                            <p class="mb-0"><?php echo e($career->formatted_salary); ?></p>
                        </div>

                        <?php if($career->application_deadline): ?>
                            <div class="mb-3">
                                <strong>Application Deadline:</strong>
                                <p class="mb-0 <?php echo e($career->isApplicationDeadlinePassed() ? 'text-danger' : 'text-success'); ?>">
                                    <?php echo e($career->application_deadline->format('M d, Y')); ?>

                                    <?php if($career->isApplicationDeadlinePassed()): ?>
                                        <br><small class="text-danger">Deadline passed</small>
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <strong>Total Applications:</strong>
                            <p class="mb-0">
                                <span class="badge bg-primary fs-6"><?php echo e($career->jobApplications->count()); ?></span>
                            </p>
                        </div>

                        <div class="mb-0">
                            <strong>Sort Order:</strong>
                            <p class="mb-0"><?php echo e($career->sort_order ?? 0); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <?php if($career->contact_email || $career->contact_phone): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                        </div>
                        <div class="card-body">
                            <?php if($career->contact_email): ?>
                                <div class="mb-3">
                                    <strong>Contact Email:</strong>
                                    <p class="mb-0">
                                        <a href="mailto:<?php echo e($career->contact_email); ?>"><?php echo e($career->contact_email); ?></a>
                                    </p>
                                </div>
                            <?php endif; ?>

                            <?php if($career->contact_phone): ?>
                                <div class="mb-0">
                                    <strong>Contact Phone:</strong>
                                    <p class="mb-0">
                                        <a href="tel:<?php echo e($career->contact_phone); ?>"><?php echo e($career->contact_phone); ?></a>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Quick Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('admin.cms.careers.edit', $career)); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i> Edit Position
                            </a>
                            <a href="<?php echo e(route('careers.show', $career->slug)); ?>" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-external-link-alt me-2"></i> View on Website
                            </a>
                            <?php if($career->jobApplications->count() > 0): ?>
                                <a href="<?php echo e(route('admin.cms.job-applications.index', ['career' => $career->id])); ?>" class="btn btn-outline-success">
                                    <i class="fas fa-users me-2"></i> View Applications
                                </a>
                            <?php endif; ?>
                            <button type="button" class="btn btn-outline-warning" onclick="toggleStatus()">
                                <i class="fas fa-<?php echo e($career->is_active ? 'eye-slash' : 'eye'); ?> me-2"></i> 
                                <?php echo e($career->is_active ? 'Deactivate' : 'Activate'); ?>

                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the career position <strong><?php echo e($career->title); ?></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone and will also delete all associated job applications.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="<?php echo e(route('admin.cms.careers.destroy', $career)); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">Delete Position</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function confirmDelete() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("admin.cms.careers.toggle-status", $career)); ?>';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/careers/show.blade.php ENDPATH**/ ?>