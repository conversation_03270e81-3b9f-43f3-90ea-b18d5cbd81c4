@extends('layouts.admin')

@section('title', 'Product Analytics - ' . $product->name)
@section('page-title', 'Product Analytics')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.show', $product) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Product
        </a>
        <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-outline-primary">
            <i class="fas fa-edit me-1"></i> Edit Product
        </a>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Product Info Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            @if($product->featured_image)
                                <img src="{{ asset('storage/' . $product->featured_image) }}" 
                                     alt="{{ $product->name }}" 
                                     class="img-thumbnail" style="max-width: 100px;">
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 100px; height: 100px; border-radius: 8px;">
                                    <i class="fas fa-image text-muted fa-2x"></i>
                                </div>
                            @endif
                        </div>
                        <div class="col-md-10">
                            <h4 class="mb-1">{{ $product->name }}</h4>
                            <p class="text-muted mb-1">SKU: {{ $product->sku }}</p>
                            <p class="text-muted mb-0">Category: {{ $product->category->name ?? 'Uncategorized' }}</p>
                            <div class="mt-2">
                                <span class="badge bg-{{ $product->is_active ? 'success' : 'danger' }}">
                                    {{ $product->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                @if($product->is_featured)
                                    <span class="badge bg-warning">Featured</span>
                                @endif
                                @if($analytics['low_stock_alert'])
                                    <span class="badge bg-danger">Low Stock</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Sales
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($analytics['sales']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                ${{ number_format($analytics['revenue'], 2) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Profit Margin
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($analytics['profit_margin'], 1) }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Stock Level
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $analytics['stock_level'] ?? 'N/A' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Sales Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Sales Overview</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Details -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Product Details</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Current Price:</strong>
                        <span class="float-end">${{ number_format($product->getCurrentPrice(), 2) }}</span>
                    </div>
                    @if($product->sale_price && $product->isOnSale())
                        <div class="mb-3">
                            <strong>Regular Price:</strong>
                            <span class="float-end text-muted">
                                <del>${{ number_format($product->price, 2) }}</del>
                            </span>
                        </div>
                        <div class="mb-3">
                            <strong>Discount:</strong>
                            <span class="float-end text-success">
                                {{ $product->discount_percentage }}% OFF
                            </span>
                        </div>
                    @endif
                    @if($product->cost_price)
                        <div class="mb-3">
                            <strong>Cost Price:</strong>
                            <span class="float-end">${{ number_format($product->cost_price, 2) }}</span>
                        </div>
                        <div class="mb-3">
                            <strong>Profit per Sale:</strong>
                            <span class="float-end text-success">
                                ${{ number_format($product->getCurrentPrice() - $product->cost_price, 2) }}
                            </span>
                        </div>
                    @endif
                    @if($product->manage_stock)
                        <div class="mb-3">
                            <strong>Stock Quantity:</strong>
                            <span class="float-end {{ $analytics['low_stock_alert'] ? 'text-danger' : '' }}">
                                {{ $product->stock_quantity }}
                            </span>
                        </div>
                        <div class="mb-3">
                            <strong>Low Stock Alert:</strong>
                            <span class="float-end">{{ $product->min_stock_level }}</span>
                        </div>
                    @endif
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <span class="float-end">
                            <span class="badge bg-{{ $product->is_active ? 'success' : 'danger' }}">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </span>
                    </div>
                    <div class="mb-3">
                        <strong>Created:</strong>
                        <span class="float-end">{{ $product->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong>
                        <span class="float-end">{{ $product->updated_at->format('M d, Y') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Performance Insights</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-success">Strengths</h6>
                            <ul class="list-unstyled">
                                @if($analytics['profit_margin'] > 20)
                                    <li><i class="fas fa-check text-success me-2"></i>Good profit margin ({{ number_format($analytics['profit_margin'], 1) }}%)</li>
                                @endif
                                @if($product->is_featured)
                                    <li><i class="fas fa-check text-success me-2"></i>Featured product</li>
                                @endif
                                @if($product->reviews_allowed)
                                    <li><i class="fas fa-check text-success me-2"></i>Reviews enabled</li>
                                @endif
                                @if($product->meta_title && $product->meta_description)
                                    <li><i class="fas fa-check text-success me-2"></i>SEO optimized</li>
                                @endif
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">Areas for Improvement</h6>
                            <ul class="list-unstyled">
                                @if($analytics['sales'] == 0)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>No sales recorded yet</li>
                                @endif
                                @if($analytics['low_stock_alert'])
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Low stock level</li>
                                @endif
                                @if(!$product->featured_image)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Missing featured image</li>
                                @endif
                                @if(!$product->meta_title || !$product->meta_description)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>SEO could be improved</li>
                                @endif
                                @if($analytics['profit_margin'] < 10)
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Low profit margin</li>
                                @endif
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sales Chart
    const ctx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: @json($salesData['labels']),
            datasets: [{
                label: 'Sales',
                data: @json($salesData['data']),
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            elements: {
                point: {
                    radius: 4,
                    hoverRadius: 6
                }
            }
        }
    });
});
</script>
@endpush
