@extends('layouts.customer')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.parcels') }}" class="btn btn-primary">
            <i class="fas fa-box me-1"></i> View All Parcels
        </a>
        <a href="{{ route('customer.track') }}" class="btn btn-outline-primary">
            <i class="fas fa-search-location me-1"></i> Track Package
        </a>
    </div>
@endsection

@section('content')
    <!-- Welcome Message -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 bg-gradient-primary text-dark bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2">Welcome back, {{ Auth::user()->name }}!</h4>
                            <p class="mb-0">Here's an overview of your shipping activity with Atrix Logistics.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-shipping-fast fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $stats['total_parcels'] }}</h3>
                        <p class="mb-0">Total Parcels</p>
                    </div>
                    <div>
                        <i class="fas fa-boxes fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $stats['delivered_parcels'] }}</h3>
                        <p class="mb-0">Delivered</p>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $stats['in_transit_parcels'] }}</h3>
                        <p class="mb-0">In Transit</p>
                    </div>
                    <div>
                        <i class="fas fa-truck fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">@currency($stats['total_spent_parcels'])</h3>
                        <p class="mb-0">Spent on Shipping</p>
                    </div>
                    <div>
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- E-commerce Statistics -->
    @if($stats['total_orders'] > 0)
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 bg-gradient-success text-white">
                    <div class="card-header border-0">
                        <h5 class="mb-0 text-white">
                            <i class="fas fa-shopping-cart me-2"></i>
                            E-commerce Activity
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-1 text-white">{{ $stats['total_orders'] }}</h4>
                                        <p class="mb-0 text-white-50">Total Orders</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-receipt fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-1 text-white">{{ $stats['delivered_orders'] }}</h4>
                                        <p class="mb-0 text-white-50">Delivered Orders</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-1 text-white">@currency($stats['total_spent_orders'])</h4>
                                        <p class="mb-0 text-white-50">Total Spent</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h4 class="mb-1 text-white">@currency($stats['average_order_value'])</h4>
                                        <p class="mb-0 text-white-50">Avg Order Value</p>
                                    </div>
                                    <div>
                                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <a href="{{ route('customer.orders') }}" class="btn btn-light btn-sm">
                                        <i class="fas fa-list me-1"></i> View All Orders
                                    </a>
                                    <a href="{{ route('customer.wishlist.index') }}" class="btn btn-outline-light btn-sm">
                                        <i class="fas fa-heart me-1"></i> My Wishlist
                                    </a>
                                    <a href="{{ route('customer.support.index') }}" class="btn btn-outline-light btn-sm">
                                        <i class="fas fa-headset me-1"></i> Support
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <div class="row">
        <!-- Recent Parcels -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            Recent Parcels
                        </h5>
                        <a href="{{ route('customer.parcels') }}" class="btn btn-sm btn-outline-light">
                            View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($recentParcels->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tracking #</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentParcels as $parcel)
                                        <tr>
                                            <td>
                                                <strong>{{ $parcel->tracking_number }}</strong>
                                            </td>
                                            <td>
                                                {{ Str::limit($parcel->description, 30) }}
                                                @if($parcel->recipient_name)
                                                    <br><small class="text-muted">To: {{ $parcel->recipient_name }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $statusColors = [
                                                        'pending' => 'secondary',
                                                        'picked_up' => 'info',
                                                        'in_transit' => 'warning',
                                                        'out_for_delivery' => 'primary',
                                                        'delivered' => 'success',
                                                        'exception' => 'danger',
                                                        'returned' => 'dark'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusColors[$parcel->status] ?? 'secondary' }}">
                                                    {{ ucwords(str_replace('_', ' ', $parcel->status)) }}
                                                </span>
                                            </td>
                                            <td>
                                                {{ $parcel->created_at->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $parcel->created_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                <a href="{{ route('customer.parcels.show', $parcel) }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No parcels yet</h5>
                            <p class="text-muted">Your shipping history will appear here once you start using our services.</p>
                            <a href="#" class="btn btn-primary" onclick="alert('Coming soon!')">
                                <i class="fas fa-plus me-2"></i>
                                Create Shipment
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions & Charts -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('customer.track') }}" class="btn btn-outline-primary">
                            <i class="fas fa-search-location me-2"></i>
                            Track Package
                        </a>
                        <a href="{{ route('customer.orders') }}" class="btn btn-outline-success">
                            <i class="fas fa-receipt me-2"></i>
                            My Orders
                        </a>
                        <a href="{{ route('customer.wishlist.index') }}" class="btn btn-outline-info">
                            <i class="fas fa-heart me-2"></i>
                            My Wishlist
                        </a>
                        <a href="{{ route('customer.support.create') }}" class="btn btn-outline-warning">
                            <i class="fas fa-headset me-2"></i>
                            Get Support
                        </a>
                        <a href="{{ route('customer.profile.show') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-user me-2"></i>
                            My Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Parcel Status Chart -->
            @if($stats['total_parcels'] > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            Parcel Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" width="400" height="400"></canvas>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Monthly Spending Chart -->
    @if($monthlySpending->count() > 0)
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Monthly Spending (Last 6 Months)
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="spendingChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if(!$cart->isEmpty())
        <!-- Shopping Cart Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>
                                My Shopping Cart ({{ $cart->item_count }} items)
                            </h5>
                            <div class="d-flex gap-2">
                                <a href="{{ route('cart.index') }}" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-edit me-1"></i>Manage Cart
                                </a>
                                <a href="{{ route('checkout.index') }}" class="btn btn-sm btn-success">
                                    <i class="fas fa-credit-card me-1"></i>Checkout
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Cart Items -->
                            <div class="col-lg-8">
                                <div class="row">
                                    @foreach($cart->items->take(3) as $item)
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-center">
                                                    @if($item->product->featured_image_url)
                                                        <img src="{{ $item->product->featured_image_url }}"
                                                             alt="{{ $item->product->name }}"
                                                             class="rounded me-3"
                                                             style="width: 50px; height: 50px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                             style="width: 50px; height: 50px;">
                                                            <i class="fas fa-box text-muted"></i>
                                                        </div>
                                                    @endif
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1 text-truncate">{{ $item->product->name }}</h6>
                                                        <small class="text-muted">Qty: {{ $item->quantity }}</small>
                                                        <div class="fw-bold text-success">${{ $item->formatted_total_price }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach

                                    @if($cart->items->count() > 3)
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-dashed">
                                            <div class="card-body p-3 d-flex align-items-center justify-content-center">
                                                <div class="text-center">
                                                    <i class="fas fa-plus-circle text-muted mb-2" style="font-size: 2rem;"></i>
                                                    <p class="mb-0 text-muted">{{ $cart->items->count() - 3 }} more items</p>
                                                    <a href="{{ route('cart.index') }}" class="btn btn-sm btn-outline-primary mt-2">
                                                        View All
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Cart Summary -->
                            <div class="col-lg-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Order Summary</h6>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Subtotal:</span>
                                            <span>${{ number_format($cart->subtotal, 2) }}</span>
                                        </div>
                                        @if($cart->tax_amount > 0)
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Tax:</span>
                                            <span>${{ number_format($cart->tax_amount, 2) }}</span>
                                        </div>
                                        @endif
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Shipping:</span>
                                            <span class="text-success">Free</span>
                                        </div>
                                        @if($cart->discount_amount > 0)
                                        <div class="d-flex justify-content-between mb-2 text-success">
                                            <span>Discount:</span>
                                            <span>-${{ number_format($cart->discount_amount, 2) }}</span>
                                        </div>
                                        @endif
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>Total:</span>
                                            <span class="text-success">${{ number_format($cart->total_amount, 2) }}</span>
                                        </div>
                                        <div class="d-grid gap-2 mt-3">
                                            <a href="{{ route('checkout.index') }}" class="btn btn-success">
                                                <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                            </a>
                                            <a href="{{ route('cart.index') }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-shopping-cart me-2"></i>View Full Cart
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('styles')
<style>
.border-dashed {
    border: 2px dashed #dee2e6 !important;
}
</style>
@endpush

@push('scripts')
<script>
    // Parcel Status Chart
    @if($stats['total_parcels'] > 0)
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    @foreach($parcelsByStatus as $status => $count)
                        '{{ ucwords(str_replace("_", " ", $status)) }}',
                    @endforeach
                ],
                datasets: [{
                    data: [
                        @foreach($parcelsByStatus as $status => $count)
                            {{ $count }},
                        @endforeach
                    ],
                    backgroundColor: [
                        '#6c757d', // pending
                        '#0dcaf0', // picked_up
                        '#ffc107', // in_transit
                        '#0d6efd', // out_for_delivery
                        '#198754', // delivered
                        '#dc3545', // exception
                        '#212529'  // returned
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    @endif

    // Monthly Spending Chart
    @if($monthlySpending->count() > 0)
        const spendingCtx = document.getElementById('spendingChart').getContext('2d');
        const spendingChart = new Chart(spendingCtx, {
            type: 'line',
            data: {
                labels: [
                    @foreach($monthlySpending as $month)
                        '{{ $month["period"] }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'Spending ($)',
                    data: [
                        @foreach($monthlySpending as $month)
                            {{ $month["total"] }},
                        @endforeach
                    ],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toFixed(2);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    @endif
</script>
@endpush
