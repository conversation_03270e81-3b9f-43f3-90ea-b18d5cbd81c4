<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // Branding
            ['key_name' => 'site_name', 'value' => 'Atrix Logistics', 'type' => 'string', 'group_name' => 'branding', 'label' => 'Site Name', 'description' => 'Website name displayed in header and title', 'is_public' => true],
            ['key_name' => 'site_title', 'value' => 'Atrix Logistics - Professional Shipping Solutions', 'type' => 'string', 'group_name' => 'branding', 'label' => 'Site Title', 'description' => 'Default page title for SEO', 'is_public' => true],
            ['key_name' => 'site_tagline', 'value' => 'We ship anything, anywhere, anytime', 'type' => 'string', 'group_name' => 'branding', 'label' => 'Site Tagline', 'description' => 'Company tagline/slogan', 'is_public' => true],
            ['key_name' => 'company_description', 'value' => 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.', 'type' => 'text', 'group_name' => 'branding', 'label' => 'Company Description', 'description' => 'Company description for footer and about sections', 'is_public' => true],
            ['key_name' => 'site_logo', 'value' => '', 'type' => 'image', 'group_name' => 'branding', 'label' => 'Site Logo', 'description' => 'Main website logo', 'is_public' => true],
            ['key_name' => 'site_favicon', 'value' => '', 'type' => 'image', 'group_name' => 'branding', 'label' => 'Favicon', 'description' => 'Website favicon', 'is_public' => true],

            // Contact Information
            ['key_name' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Contact Email', 'description' => 'Main contact email address', 'is_public' => true],
            ['key_name' => 'email', 'value' => '<EMAIL>', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Email (Alternative)', 'description' => 'Alternative email field name', 'is_public' => true],
            ['key_name' => 'notification_email', 'value' => '<EMAIL>', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Notification Email', 'description' => 'Email address to receive contact form notifications', 'is_public' => false],
            ['key_name' => 'contact_phone', 'value' => '+****************', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Contact Phone', 'description' => 'Main contact phone number', 'is_public' => true],
            ['key_name' => 'phone', 'value' => '+****************', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Phone (Alternative)', 'description' => 'Alternative phone field name', 'is_public' => true],
            ['key_name' => 'phone_2', 'value' => '+****************', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Secondary Phone', 'description' => 'Secondary contact phone number', 'is_public' => true],
            ['key_name' => 'contact_fax', 'value' => '+****************', 'type' => 'string', 'group_name' => 'contact', 'label' => 'Fax Number', 'description' => 'Company fax number', 'is_public' => true],
            ['key_name' => 'contact_address', 'value' => '123 Logistics Ave, Transport City, TC 12345', 'type' => 'text', 'group_name' => 'contact', 'label' => 'Company Address', 'description' => 'Main company address', 'is_public' => true],
            ['key_name' => 'address', 'value' => '123 Logistics Ave, Transport City, TC 12345', 'type' => 'text', 'group_name' => 'contact', 'label' => 'Address (Alternative)', 'description' => 'Alternative address field name', 'is_public' => true],
            ['key_name' => 'business_hours', 'value' => "Monday - Friday: 8:00 AM - 6:00 PM\nSaturday: 9:00 AM - 4:00 PM\nSunday: Closed", 'type' => 'text', 'group_name' => 'contact', 'label' => 'Business Hours', 'description' => 'Operating hours', 'is_public' => true],

            // Support Information
            ['key_name' => 'support_phone', 'value' => '+****************', 'type' => 'string', 'group_name' => 'support', 'label' => 'Support Phone', 'description' => 'Customer support phone number', 'is_public' => true],
            ['key_name' => 'support_email', 'value' => '<EMAIL>', 'type' => 'string', 'group_name' => 'support', 'label' => 'Support Email', 'description' => 'Customer support email address', 'is_public' => true],
            ['key_name' => 'support_hours', 'value' => 'Mon-Fri: 8AM-6PM EST', 'type' => 'string', 'group_name' => 'support', 'label' => 'Support Hours', 'description' => 'Customer support operating hours', 'is_public' => true],
            ['key_name' => 'support_response_time', 'value' => 'Response within 24 hours', 'type' => 'string', 'group_name' => 'support', 'label' => 'Support Response Time', 'description' => 'Expected support response time', 'is_public' => true],
            ['key_name' => 'live_chat_hours', 'value' => 'Mon-Fri: 9AM-5PM EST', 'type' => 'string', 'group_name' => 'support', 'label' => 'Live Chat Hours', 'description' => 'Live chat availability hours', 'is_public' => true],
            ['key_name' => 'live_chat_enabled', 'value' => '1', 'type' => 'boolean', 'group_name' => 'support', 'label' => 'Enable Live Chat', 'description' => 'Enable/disable live chat feature', 'is_public' => true],

            // Currency Settings
            ['key_name' => 'base_currency', 'value' => 'USD', 'type' => 'string', 'group_name' => 'currency', 'label' => 'Base Currency', 'description' => 'Primary currency for the platform', 'is_public' => true],
            ['key_name' => 'currency_symbol', 'value' => '$', 'type' => 'string', 'group_name' => 'currency', 'label' => 'Currency Symbol', 'description' => 'Symbol for the base currency', 'is_public' => true],
            ['key_name' => 'currency_position', 'value' => 'before', 'type' => 'string', 'group_name' => 'currency', 'label' => 'Currency Position', 'description' => 'Position of currency symbol (before/after)', 'is_public' => true],
            ['key_name' => 'decimal_places', 'value' => '2', 'type' => 'integer', 'group_name' => 'currency', 'label' => 'Decimal Places', 'description' => 'Number of decimal places for currency display', 'is_public' => true],
            ['key_name' => 'thousands_separator', 'value' => ',', 'type' => 'string', 'group_name' => 'currency', 'label' => 'Thousands Separator', 'description' => 'Character used to separate thousands', 'is_public' => true],
            ['key_name' => 'decimal_separator', 'value' => '.', 'type' => 'string', 'group_name' => 'currency', 'label' => 'Decimal Separator', 'description' => 'Character used to separate decimals', 'is_public' => true],

            // Bank Account Settings
            ['key_name' => 'bank_name', 'value' => '', 'type' => 'string', 'group_name' => 'bank_account', 'label' => 'Bank Name', 'description' => 'Name of the bank for manual transfers', 'is_public' => false],
            ['key_name' => 'bank_account_name', 'value' => '', 'type' => 'string', 'group_name' => 'bank_account', 'label' => 'Account Holder Name', 'description' => 'Name on the bank account', 'is_public' => false],
            ['key_name' => 'bank_account_number', 'value' => '', 'type' => 'string', 'group_name' => 'bank_account', 'label' => 'Account Number', 'description' => 'Bank account number', 'is_public' => false],
            ['key_name' => 'bank_routing_number', 'value' => '', 'type' => 'string', 'group_name' => 'bank_account', 'label' => 'Routing Number', 'description' => 'Bank routing number (US) or sort code', 'is_public' => false],
            ['key_name' => 'bank_swift_code', 'value' => '', 'type' => 'string', 'group_name' => 'bank_account', 'label' => 'SWIFT/BIC Code', 'description' => 'International bank identifier', 'is_public' => false],
            ['key_name' => 'bank_iban', 'value' => '', 'type' => 'string', 'group_name' => 'bank_account', 'label' => 'IBAN', 'description' => 'International Bank Account Number', 'is_public' => false],
            ['key_name' => 'bank_branch_address', 'value' => '', 'type' => 'text', 'group_name' => 'bank_account', 'label' => 'Bank Branch Address', 'description' => 'Physical address of the bank branch', 'is_public' => false],
            ['key_name' => 'bank_instructions', 'value' => '', 'type' => 'text', 'group_name' => 'bank_account', 'label' => 'Transfer Instructions', 'description' => 'Additional instructions for bank transfers', 'is_public' => false],

            // PayPal Account Settings
            ['key_name' => 'paypal_email', 'value' => '', 'type' => 'string', 'group_name' => 'paypal_account', 'label' => 'PayPal Email', 'description' => 'PayPal account email for manual transfers', 'is_public' => false],
            ['key_name' => 'paypal_account_name', 'value' => '', 'type' => 'string', 'group_name' => 'paypal_account', 'label' => 'PayPal Account Name', 'description' => 'Name associated with PayPal account', 'is_public' => false],
            ['key_name' => 'paypal_account_type', 'value' => 'Personal', 'type' => 'string', 'group_name' => 'paypal_account', 'label' => 'Account Type', 'description' => 'PayPal account type (Personal/Business)', 'is_public' => false],
            ['key_name' => 'paypal_phone', 'value' => '', 'type' => 'string', 'group_name' => 'paypal_account', 'label' => 'PayPal Phone', 'description' => 'Phone number associated with PayPal account', 'is_public' => false],
            ['key_name' => 'paypal_instructions', 'value' => '', 'type' => 'text', 'group_name' => 'paypal_account', 'label' => 'PayPal Instructions', 'description' => 'Instructions for PayPal manual transfers', 'is_public' => false],

            // Social Media
            ['key_name' => 'social_facebook', 'value' => 'https://facebook.com/atrixlogistics', 'type' => 'string', 'group_name' => 'social', 'label' => 'Facebook URL', 'description' => 'Facebook page URL', 'is_public' => true],
            ['key_name' => 'social_twitter', 'value' => 'https://twitter.com/atrixlogistics', 'type' => 'string', 'group_name' => 'social', 'label' => 'Twitter URL', 'description' => 'Twitter profile URL', 'is_public' => true],
            ['key_name' => 'social_linkedin', 'value' => 'https://linkedin.com/company/atrixlogistics', 'type' => 'string', 'group_name' => 'social', 'label' => 'LinkedIn URL', 'description' => 'LinkedIn company page URL', 'is_public' => true],
            ['key_name' => 'social_instagram', 'value' => 'https://instagram.com/atrixlogistics', 'type' => 'string', 'group_name' => 'social', 'label' => 'Instagram URL', 'description' => 'Instagram profile URL', 'is_public' => true],

            // SEO Settings
            ['key_name' => 'meta_description', 'value' => 'Professional logistics and shipping services. We handle truck, air, and ocean freight with reliable tracking and competitive rates.', 'type' => 'text', 'group_name' => 'seo', 'label' => 'Meta Description', 'description' => 'Default meta description for SEO', 'is_public' => false],
            ['key_name' => 'meta_keywords', 'value' => 'logistics, shipping, freight, transportation, delivery, tracking', 'type' => 'text', 'group_name' => 'seo', 'label' => 'Meta Keywords', 'description' => 'Default meta keywords', 'is_public' => false],

            // System Settings
            ['key_name' => 'tracking_prefix', 'value' => 'ATX', 'type' => 'string', 'group_name' => 'system', 'label' => 'Tracking Prefix', 'description' => 'Tracking number prefix', 'is_public' => false],
            ['key_name' => 'default_carrier_id', 'value' => '3', 'type' => 'integer', 'group_name' => 'system', 'label' => 'Default Carrier', 'description' => 'Default carrier for new parcels', 'is_public' => false],
            ['key_name' => 'quote_validity_days', 'value' => '30', 'type' => 'integer', 'group_name' => 'system', 'label' => 'Quote Validity', 'description' => 'Number of days quotes remain valid', 'is_public' => false],

            // Shipping Settings
            ['key_name' => 'shipping_base_rate_express', 'value' => '15.50', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Express Base Rate (per kg)', 'description' => 'Base rate per kilogram for express shipping', 'is_public' => false],
            ['key_name' => 'shipping_base_rate_standard', 'value' => '8.75', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Standard Base Rate (per kg)', 'description' => 'Base rate per kilogram for standard shipping', 'is_public' => false],
            ['key_name' => 'shipping_base_rate_economy', 'value' => '5.25', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Economy Base Rate (per kg)', 'description' => 'Base rate per kilogram for economy shipping', 'is_public' => false],
            ['key_name' => 'shipping_base_rate_freight', 'value' => '3.80', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Freight Base Rate (per kg)', 'description' => 'Base rate per kilogram for freight shipping', 'is_public' => false],
            ['key_name' => 'shipping_service_multiplier_express', 'value' => '1.2', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Express Service Multiplier', 'description' => 'Multiplier for express service type', 'is_public' => false],
            ['key_name' => 'shipping_service_multiplier_overnight', 'value' => '1.5', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Overnight Service Multiplier', 'description' => 'Multiplier for overnight service type', 'is_public' => false],
            ['key_name' => 'shipping_service_multiplier_same_day', 'value' => '2.0', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Same Day Service Multiplier', 'description' => 'Multiplier for same day service type', 'is_public' => false],
            ['key_name' => 'shipping_international_multiplier', 'value' => '2.5', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'International Shipping Multiplier', 'description' => 'Multiplier for international shipments', 'is_public' => false],
            ['key_name' => 'shipping_insurance_rate', 'value' => '0.02', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Insurance Rate (%)', 'description' => 'Insurance rate as percentage of declared value (e.g., 0.02 = 2%)', 'is_public' => false],
            ['key_name' => 'shipping_signature_fee', 'value' => '5.00', 'type' => 'string', 'group_name' => 'shipping', 'label' => 'Signature Required Fee', 'description' => 'Flat fee for signature required service', 'is_public' => false],

            // WhatsApp & Live Chat Settings
            ['key_name' => 'whatsapp_number', 'value' => '', 'type' => 'string', 'group_name' => 'contact', 'label' => 'WhatsApp Number', 'description' => 'WhatsApp number with country code (e.g., +1234567890)', 'is_public' => true],
            ['key_name' => 'whatsapp_enabled', 'value' => '0', 'type' => 'boolean', 'group_name' => 'contact', 'label' => 'Enable WhatsApp Button', 'description' => 'Show floating WhatsApp button on frontend', 'is_public' => true],
            ['key_name' => 'live_chat_enabled', 'value' => '1', 'type' => 'boolean', 'group_name' => 'contact', 'label' => 'Enable Live Chat', 'description' => 'Enable live chat widget on frontend', 'is_public' => true],
            ['key_name' => 'enable_product_inquiries', 'value' => '1', 'type' => 'boolean', 'group_name' => 'system', 'label' => 'Enable Product Inquiries', 'description' => 'Allow product inquiry modal', 'is_public' => false],

            // Social Media Links
            ['key_name' => 'social_facebook', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'Facebook URL', 'description' => 'Facebook page URL', 'is_public' => true],
            ['key_name' => 'facebook_url', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'Facebook URL (Alt)', 'description' => 'Alternative Facebook URL field', 'is_public' => true],
            ['key_name' => 'social_twitter', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'Twitter URL', 'description' => 'Twitter profile URL', 'is_public' => true],
            ['key_name' => 'twitter_url', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'Twitter URL (Alt)', 'description' => 'Alternative Twitter URL field', 'is_public' => true],
            ['key_name' => 'social_linkedin', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'LinkedIn URL', 'description' => 'LinkedIn company page URL', 'is_public' => true],
            ['key_name' => 'linkedin_url', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'LinkedIn URL (Alt)', 'description' => 'Alternative LinkedIn URL field', 'is_public' => true],
            ['key_name' => 'social_instagram', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'Instagram URL', 'description' => 'Instagram profile URL', 'is_public' => true],
            ['key_name' => 'instagram_url', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'Instagram URL (Alt)', 'description' => 'Alternative Instagram URL field', 'is_public' => true],
            ['key_name' => 'social_youtube', 'value' => '', 'type' => 'string', 'group_name' => 'social', 'label' => 'YouTube URL', 'description' => 'YouTube channel URL', 'is_public' => true],

            // Homepage Content
            ['key_name' => 'hero_title', 'value' => 'Professional Logistics & Freight Solutions', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Hero Title', 'description' => 'Main hero section title', 'is_public' => true],
            ['key_name' => 'hero_subtitle', 'value' => 'Global Shipping • Supply Chain Management • Warehousing Solutions', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Hero Subtitle', 'description' => 'Hero section subtitle', 'is_public' => true],
            ['key_name' => 'hero_description', 'value' => 'Reliable worldwide logistics and freight solutions for businesses of all sizes. From automotive parts to steel products, we deliver your cargo safely and on time.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Hero Description', 'description' => 'Hero section description text', 'is_public' => true],
            ['key_name' => 'hero_image', 'value' => '', 'type' => 'image', 'group_name' => 'homepage', 'label' => 'Hero Image', 'description' => 'Hero section background/featured image', 'is_public' => true],

            // Services Section
            ['key_name' => 'services_title', 'value' => 'Our Logistics Services', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Services Section Title', 'description' => 'Services section main title', 'is_public' => true],
            ['key_name' => 'services_description', 'value' => 'Comprehensive logistics and freight solutions designed to streamline your supply chain and deliver results.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Services Section Description', 'description' => 'Services section description', 'is_public' => true],

            // Products Section
            ['key_name' => 'products_title', 'value' => 'Featured Products', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Products Section Title', 'description' => 'Products section main title', 'is_public' => true],
            ['key_name' => 'products_description', 'value' => 'Quality products we specialize in shipping and handling with expertise.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Products Section Description', 'description' => 'Products section description', 'is_public' => true],

            // Tracking Section
            ['key_name' => 'tracking_title', 'value' => 'Track Your Shipment', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Tracking Section Title', 'description' => 'Tracking section main title', 'is_public' => true],
            ['key_name' => 'tracking_description', 'value' => 'Get real-time updates on your cargo location and delivery status', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Tracking Section Description', 'description' => 'Tracking section description', 'is_public' => true],

            // Statistics
            ['key_name' => 'stat_shipments', 'value' => '10K+', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Successful Shipments', 'description' => 'Number of successful shipments', 'is_public' => true],
            ['key_name' => 'stat_countries', 'value' => '50+', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Countries Served', 'description' => 'Number of countries served', 'is_public' => true],
            ['key_name' => 'stat_clients', 'value' => '500+', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Happy Clients', 'description' => 'Number of satisfied clients', 'is_public' => true],
            ['key_name' => 'stat_experience', 'value' => '15+', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Years Experience', 'description' => 'Years of experience', 'is_public' => true],

            // Why Choose Us Section
            ['key_name' => 'why_choose_title', 'value' => 'Why Choose Our Logistics?', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Why Choose Us Title', 'description' => 'Why choose us section title', 'is_public' => true],
            ['key_name' => 'why_choose_description', 'value' => 'We combine industry expertise with cutting-edge technology to deliver exceptional logistics solutions that drive your business forward.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Why Choose Us Description', 'description' => 'Why choose us section description', 'is_public' => true],
            ['key_name' => 'why_choose_image', 'value' => '', 'type' => 'image', 'group_name' => 'homepage', 'label' => 'Why Choose Us Image', 'description' => 'Why choose us section image', 'is_public' => true],

            // Testimonials Section
            ['key_name' => 'testimonials_title', 'value' => 'What Our Clients Say', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Testimonials Title', 'description' => 'Testimonials section title', 'is_public' => true],
            ['key_name' => 'testimonials_description', 'value' => 'Trusted by businesses worldwide for reliable logistics solutions.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Testimonials Description', 'description' => 'Testimonials section description', 'is_public' => true],

            // Testimonial 1
            ['key_name' => 'testimonial_1_text', 'value' => 'Outstanding logistics service! Their team handled our complex supply chain requirements with professionalism and delivered exceptional results.', 'type' => 'text', 'group_name' => 'testimonials', 'label' => 'Testimonial 1 Text', 'description' => 'First testimonial text', 'is_public' => true],
            ['key_name' => 'testimonial_1_name', 'value' => 'Sarah Johnson', 'type' => 'string', 'group_name' => 'testimonials', 'label' => 'Testimonial 1 Name', 'description' => 'First testimonial author name', 'is_public' => true],
            ['key_name' => 'testimonial_1_company', 'value' => 'Manufacturing Corp', 'type' => 'string', 'group_name' => 'testimonials', 'label' => 'Testimonial 1 Company', 'description' => 'First testimonial author company', 'is_public' => true],

            // Testimonial 2
            ['key_name' => 'testimonial_2_text', 'value' => 'Outstanding logistics support for our steel products. Professional team and competitive pricing.', 'type' => 'text', 'group_name' => 'testimonials', 'label' => 'Testimonial 2 Text', 'description' => 'Second testimonial text', 'is_public' => true],
            ['key_name' => 'testimonial_2_name', 'value' => 'Michael Chen', 'type' => 'string', 'group_name' => 'testimonials', 'label' => 'Testimonial 2 Name', 'description' => 'Second testimonial author name', 'is_public' => true],
            ['key_name' => 'testimonial_2_company', 'value' => 'Steel Works Inc.', 'type' => 'string', 'group_name' => 'testimonials', 'label' => 'Testimonial 2 Company', 'description' => 'Second testimonial author company', 'is_public' => true],

            // Testimonial 3
            ['key_name' => 'testimonial_3_text', 'value' => 'Reliable container shipping solutions that have helped us expand our business globally.', 'type' => 'text', 'group_name' => 'testimonials', 'label' => 'Testimonial 3 Text', 'description' => 'Third testimonial text', 'is_public' => true],
            ['key_name' => 'testimonial_3_name', 'value' => 'Mike Chen', 'type' => 'string', 'group_name' => 'testimonials', 'label' => 'Testimonial 3 Name', 'description' => 'Third testimonial author name', 'is_public' => true],
            ['key_name' => 'testimonial_3_company', 'value' => 'Global Trade Co.', 'type' => 'string', 'group_name' => 'testimonials', 'label' => 'Testimonial 3 Company', 'description' => 'Third testimonial author company', 'is_public' => true],

            // CTA Section
            ['key_name' => 'cta_title', 'value' => 'Ready to Optimize Your Logistics?', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'CTA Section Title', 'description' => 'Call-to-action section title', 'is_public' => true],
            ['key_name' => 'cta_description', 'value' => 'Get a free quote today and experience the difference of professional logistics services.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'CTA Section Description', 'description' => 'Call-to-action section description', 'is_public' => true],

            // Blog Section
            ['key_name' => 'show_blog_section', 'value' => '0', 'type' => 'boolean', 'group_name' => 'homepage', 'label' => 'Show Blog Section', 'description' => 'Display blog section on homepage', 'is_public' => true],
            ['key_name' => 'blog_section_title', 'value' => 'Latest Industry Insights', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'Blog Section Title', 'description' => 'Blog section title', 'is_public' => true],
            ['key_name' => 'blog_section_description', 'value' => 'Stay updated with the latest trends and news in logistics and shipping.', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'Blog Section Description', 'description' => 'Blog section description', 'is_public' => true],

            // FAQ Section
            ['key_name' => 'faq_section_title', 'value' => 'Frequently Asked Questions', 'type' => 'string', 'group_name' => 'homepage', 'label' => 'FAQ Section Title', 'description' => 'FAQ section title', 'is_public' => true],
            ['key_name' => 'faq_section_description', 'value' => 'Get answers to common questions about our logistics services', 'type' => 'text', 'group_name' => 'homepage', 'label' => 'FAQ Section Description', 'description' => 'FAQ section description', 'is_public' => true],

            // Additional SEO Settings
            ['key_name' => 'site_description', 'value' => 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.', 'type' => 'text', 'group_name' => 'seo', 'label' => 'Site Description', 'description' => 'Default site description for meta tags', 'is_public' => true],
            ['key_name' => 'site_keywords', 'value' => 'logistics, freight, shipping, warehousing, supply chain, automotive parts, steel products, containers', 'type' => 'text', 'group_name' => 'seo', 'label' => 'Site Keywords', 'description' => 'Default site keywords for meta tags', 'is_public' => true],

            // About Page Content
            ['key_name' => 'about_description', 'value' => 'Learn about our company, mission, and commitment to providing exceptional logistics solutions worldwide.', 'type' => 'text', 'group_name' => 'about', 'label' => 'About Page Description', 'description' => 'About page meta description', 'is_public' => true],
            ['key_name' => 'about_hero_title', 'value' => 'About Our Company', 'type' => 'string', 'group_name' => 'about', 'label' => 'About Hero Title', 'description' => 'About page hero section title', 'is_public' => true],
            ['key_name' => 'about_hero_subtitle', 'value' => 'Leading the way in logistics and shipping solutions since 2008', 'type' => 'text', 'group_name' => 'about', 'label' => 'About Hero Subtitle', 'description' => 'About page hero section subtitle', 'is_public' => true],

            // Company Story Section
            ['key_name' => 'company_story_title', 'value' => 'Our Story', 'type' => 'string', 'group_name' => 'about', 'label' => 'Company Story Title', 'description' => 'Company story section title', 'is_public' => true],
            ['key_name' => 'company_story_subtitle', 'value' => 'Building trust through reliable logistics solutions', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Story Subtitle', 'description' => 'Company story section subtitle', 'is_public' => true],
            ['key_name' => 'company_story_p1', 'value' => 'Founded with a vision to revolutionize the logistics industry, we have grown from a small local operation to a global network of trusted partners. Our commitment to excellence and customer satisfaction has been the driving force behind our success.', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Story Paragraph 1', 'description' => 'First paragraph of company story', 'is_public' => true],
            ['key_name' => 'company_story_p2', 'value' => 'We specialize in automotive parts, shipping containers, and steel products, providing comprehensive logistics solutions that help businesses thrive in today\'s competitive marketplace. Our team of experienced professionals works tirelessly to ensure your cargo reaches its destination safely and on time.', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Story Paragraph 2', 'description' => 'Second paragraph of company story', 'is_public' => true],
            ['key_name' => 'company_story_p3', 'value' => 'With state-of-the-art technology and a customer-first approach, we continue to set new standards in the logistics industry while maintaining our core values of integrity, reliability, and innovation.', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Story Paragraph 3', 'description' => 'Third paragraph of company story', 'is_public' => true],
            ['key_name' => 'company_story_image', 'value' => '', 'type' => 'image', 'group_name' => 'about', 'label' => 'Company Story Image', 'description' => 'Company story section image', 'is_public' => true],

            // Mission, Vision, Values
            ['key_name' => 'mvv_section_title', 'value' => 'Our Mission, Vision & Values', 'type' => 'string', 'group_name' => 'about', 'label' => 'MVV Section Title', 'description' => 'Mission, Vision, Values section title', 'is_public' => true],
            ['key_name' => 'mvv_section_description', 'value' => 'The principles that guide everything we do', 'type' => 'text', 'group_name' => 'about', 'label' => 'MVV Section Description', 'description' => 'Mission, Vision, Values section description', 'is_public' => true],
            ['key_name' => 'company_mission', 'value' => 'To provide reliable, efficient, and cost-effective logistics solutions that enable our clients to focus on their core business while we handle their shipping needs with precision and care.', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Mission', 'description' => 'Company mission statement', 'is_public' => true],
            ['key_name' => 'company_vision', 'value' => 'To be the world\'s most trusted logistics partner, connecting businesses globally through innovative solutions and exceptional service that drives economic growth and prosperity.', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Vision', 'description' => 'Company vision statement', 'is_public' => true],
            ['key_name' => 'company_values', 'value' => 'Integrity, reliability, innovation, and customer satisfaction are at the core of everything we do. We believe in building long-term partnerships based on trust and mutual success.', 'type' => 'text', 'group_name' => 'about', 'label' => 'Company Values', 'description' => 'Company values statement', 'is_public' => true],

            // Team Section
            ['key_name' => 'team_section_title', 'value' => 'Meet Our Team', 'type' => 'string', 'group_name' => 'about', 'label' => 'Team Section Title', 'description' => 'Team section title', 'is_public' => true],
            ['key_name' => 'team_section_description', 'value' => 'The dedicated professionals behind our success', 'type' => 'text', 'group_name' => 'about', 'label' => 'Team Section Description', 'description' => 'Team section description', 'is_public' => true],

            // Stats Section
            ['key_name' => 'stats_section_title', 'value' => 'Our Achievements', 'type' => 'string', 'group_name' => 'about', 'label' => 'Stats Section Title', 'description' => 'Statistics section title', 'is_public' => true],
            ['key_name' => 'stats_section_description', 'value' => 'Numbers that speak for our commitment to excellence', 'type' => 'text', 'group_name' => 'about', 'label' => 'Stats Section Description', 'description' => 'Statistics section description', 'is_public' => true],

            // About CTA
            ['key_name' => 'about_cta_title', 'value' => 'Ready to Work with Us?', 'type' => 'string', 'group_name' => 'about', 'label' => 'About CTA Title', 'description' => 'About page call-to-action title', 'is_public' => true],
            ['key_name' => 'about_cta_description', 'value' => 'Join hundreds of satisfied clients who trust us with their logistics needs.', 'type' => 'text', 'group_name' => 'about', 'label' => 'About CTA Description', 'description' => 'About page call-to-action description', 'is_public' => true],

            // Trust Badges & Certifications
            ['key_name' => 'ssl_badge', 'value' => '1', 'type' => 'boolean', 'group_name' => 'trust', 'label' => 'Show SSL Badge', 'description' => 'Display SSL security badge in footer', 'is_public' => true],
            ['key_name' => 'iso_certified', 'value' => '1', 'type' => 'boolean', 'group_name' => 'trust', 'label' => 'ISO Certified', 'description' => 'Display ISO certification badge', 'is_public' => true],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key_name' => $setting['key_name']],
                $setting
            );
        }
    }
}
