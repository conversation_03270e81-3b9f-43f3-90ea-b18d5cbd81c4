<?php if($paginator->hasPages()): ?>
    <div class="d-flex justify-content-between align-items-center">
        
        <?php if($paginator->onFirstPage()): ?>
            <span class="btn btn-outline-secondary disabled">
                &laquo; Previous
            </span>
        <?php else: ?>
            <a class="btn btn-outline-primary" href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev">
                &laquo; Previous
            </a>
        <?php endif; ?>

        
        <div class="d-flex align-items-center">
            <span class="me-3">Showing <?php echo e($paginator->firstItem()); ?> to <?php echo e($paginator->lastItem()); ?> of <?php echo e($paginator->total()); ?> results</span>
            
            <nav aria-label="Page navigation">
                <ul class="pagination mb-0">
                    
                    <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        
                        <?php if(is_string($element)): ?>
                            <li class="page-item disabled"><span class="page-link"><?php echo e($element); ?></span></li>
                        <?php endif; ?>

                        
                        <?php if(is_array($element)): ?>
                            <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($page == $paginator->currentPage()): ?>
                                    <li class="page-item active"><span class="page-link"><?php echo e($page); ?></span></li>
                                <?php else: ?>
                                    <li class="page-item"><a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a></li>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </nav>
        </div>

        
        <?php if($paginator->hasMorePages()): ?>
            <a class="btn btn-outline-primary" href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next">
                Next &raquo;
            </a>
        <?php else: ?>
            <span class="btn btn-outline-secondary disabled">
                Next &raquo;
            </span>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/pagination/admin.blade.php ENDPATH**/ ?>