<!-- Language Dropdown (Mockup) -->
<div class="relative group">
    <button class="flex items-center space-x-2 text-gray-700 hover:text-green-600 transition-colors" 
            onclick="toggleLanguageDropdown()" 
            id="language-dropdown-btn">
        <i class="fas fa-globe text-sm"></i>
        <span class="text-sm font-medium">EN</span>
        <i class="fas fa-chevron-down text-xs transition-transform" id="language-chevron"></i>
    </button>
    
    <div id="language-dropdown" class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible transition-all duration-300 z-50">
        <div class="py-2">
            <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                Select Language
            </div>
            
            <!-- English -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors language-option active" data-lang="en" data-flag="🇺🇸">
                <span class="text-lg mr-3">🇺🇸</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">English</div>
                    <div class="text-xs text-gray-500">United States</div>
                </div>
                <i class="fas fa-check text-green-600 ml-auto opacity-100"></i>
            </a>
            
            <!-- Spanish -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors language-option" data-lang="es" data-flag="🇪🇸">
                <span class="text-lg mr-3">🇪🇸</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Español</div>
                    <div class="text-xs text-gray-500">España</div>
                </div>
                <i class="fas fa-check text-green-600 ml-auto opacity-0"></i>
            </a>
            
            <!-- French -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors language-option" data-lang="fr" data-flag="🇫🇷">
                <span class="text-lg mr-3">🇫🇷</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Français</div>
                    <div class="text-xs text-gray-500">France</div>
                </div>
                <i class="fas fa-check text-green-600 ml-auto opacity-0"></i>
            </a>
            
            <!-- German -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors language-option" data-lang="de" data-flag="🇩🇪">
                <span class="text-lg mr-3">🇩🇪</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Deutsch</div>
                    <div class="text-xs text-gray-500">Deutschland</div>
                </div>
                <i class="fas fa-check text-green-600 ml-auto opacity-0"></i>
            </a>
            
            <!-- Portuguese -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors language-option" data-lang="pt" data-flag="🇵🇹">
                <span class="text-lg mr-3">🇵🇹</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Português</div>
                    <div class="text-xs text-gray-500">Portugal</div>
                </div>
                <i class="fas fa-check text-green-600 ml-auto opacity-0"></i>
            </a>
            
            <!-- Chinese -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors language-option" data-lang="zh" data-flag="🇨🇳">
                <span class="text-lg mr-3">🇨🇳</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">中文</div>
                    <div class="text-xs text-gray-500">中国</div>
                </div>
                <i class="fas fa-check text-green-600 ml-auto opacity-0"></i>
            </a>
        </div>
        
        <div class="border-t border-gray-100 px-4 py-2">
            <div class="text-xs text-gray-500 text-center">
                <i class="fas fa-info-circle mr-1"></i>
                More languages coming soon
            </div>
        </div>
    </div>
</div>

<script>
function toggleLanguageDropdown() {
    const dropdown = document.getElementById('language-dropdown');
    const chevron = document.getElementById('language-chevron');
    
    if (dropdown.classList.contains('opacity-0')) {
        dropdown.classList.remove('opacity-0', 'invisible');
        dropdown.classList.add('opacity-100', 'visible');
        chevron.classList.add('rotate-180');
    } else {
        dropdown.classList.add('opacity-0', 'invisible');
        dropdown.classList.remove('opacity-100', 'visible');
        chevron.classList.remove('rotate-180');
    }
}

// Handle language selection (mockup functionality)
document.addEventListener('DOMContentLoaded', function() {
    const languageOptions = document.querySelectorAll('.language-option');
    const languageBtn = document.getElementById('language-dropdown-btn');
    
    languageOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active state from all options
            languageOptions.forEach(opt => {
                opt.classList.remove('active');
                opt.querySelector('.fas.fa-check').classList.add('opacity-0');
            });
            
            // Add active state to selected option
            this.classList.add('active');
            this.querySelector('.fas.fa-check').classList.remove('opacity-0');
            
            // Update button text
            const langCode = this.dataset.lang.toUpperCase();
            const flag = this.dataset.flag;
            languageBtn.querySelector('span').textContent = langCode;
            
            // Close dropdown
            toggleLanguageDropdown();
            
            // Show notification (mockup)
            showNotification(`Language changed to ${this.querySelector('.text-sm.font-medium').textContent}`, 'info');
        });
    });
});

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    const dropdown = document.getElementById('language-dropdown');
    const button = document.getElementById('language-dropdown-btn');
    
    if (!dropdown.contains(e.target) && !button.contains(e.target)) {
        dropdown.classList.add('opacity-0', 'invisible');
        dropdown.classList.remove('opacity-100', 'visible');
        document.getElementById('language-chevron').classList.remove('rotate-180');
    }
});
</script>

<style>
.language-option.active {
    background-color: #f0fdf4;
}

.language-option:hover .fas.fa-check {
    opacity: 0.3 !important;
}

.language-option.active:hover .fas.fa-check {
    opacity: 1 !important;
}
</style>
