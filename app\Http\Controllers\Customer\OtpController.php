<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;

class OtpController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Send OTP to customer phone number
     */
    public function sendOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|min:10|max:15',
        ]);

        $phone = $request->phone;
        $rateLimitKey = 'customer-otp-send:' . $request->ip() . ':' . $phone;

        // Rate limiting: 3 attempts per 5 minutes
        if (RateLimiter::tooManyAttempts($rateLimitKey, 3)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => "Too many attempts. Please try again in {$seconds} seconds.",
                'retry_after' => $seconds
            ], 429);
        }

        // Check if phone number can receive OTP
        if (!$this->smsService->canSendOtp($phone)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many OTP requests for this phone number. Please try again later.',
            ], 429);
        }

        // Check if customer exists with this phone number
        $customer = User::where('phone', $phone)
                       ->where('role', 'customer')
                       ->first();
        
        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'No customer account found with this phone number.',
            ], 404);
        }

        // Send OTP
        $otp = $this->smsService->sendOtp($phone);
        
        if ($otp) {
            RateLimiter::hit($rateLimitKey, 300); // 5 minutes
            
            return response()->json([
                'success' => true,
                'message' => 'OTP sent successfully to your phone number.',
                'remaining_attempts' => $this->smsService->getRemainingAttempts($phone),
                'expires_in' => 300, // 5 minutes
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to send OTP. Please try again.',
        ], 500);
    }

    /**
     * Verify OTP and login customer
     */
    public function verifyOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'otp' => 'required|string|size:6',
        ]);

        $phone = $request->phone;
        $otp = $request->otp;
        $rateLimitKey = 'customer-otp-verify:' . $request->ip() . ':' . $phone;

        // Rate limiting: 5 attempts per 5 minutes
        if (RateLimiter::tooManyAttempts($rateLimitKey, 5)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => "Too many verification attempts. Please try again in {$seconds} seconds.",
                'retry_after' => $seconds
            ], 429);
        }

        // Find customer
        $customer = User::where('phone', $phone)
                       ->where('role', 'customer')
                       ->first();
        
        if (!$customer) {
            RateLimiter::hit($rateLimitKey, 300);
            return response()->json([
                'success' => false,
                'message' => 'Invalid phone number.',
            ], 404);
        }

        // Verify OTP
        if (!$this->smsService->verifyOtp($phone, $otp)) {
            RateLimiter::hit($rateLimitKey, 300);
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired OTP.',
            ], 400);
        }

        // Check if account is active
        if (!$customer->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Your account has been deactivated. Please contact support.',
            ], 403);
        }

        // Login the customer
        Auth::login($customer, true); // Remember the customer

        // Update last login
        $customer->update([
            'last_login_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Login successful!',
            'redirect_url' => route('customer.dashboard'),
        ]);
    }
}
