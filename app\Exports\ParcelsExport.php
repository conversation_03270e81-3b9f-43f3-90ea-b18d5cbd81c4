<?php

namespace App\Exports;

use App\Models\Parcel;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Http\Request;

class ParcelsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Parcel::with(['carrier', 'user']);

        // Apply filters if request is provided
        if ($this->request) {
            if ($this->request->filled('status')) {
                $query->where('status', $this->request->status);
            }

            if ($this->request->filled('carrier_id')) {
                $query->where('carrier_id', $this->request->carrier_id);
            }

            if ($this->request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $this->request->date_from);
            }

            if ($this->request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $this->request->date_to);
            }
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Tracking Number',
            'Status',
            'Carrier',
            'Sender Name',
            'Sender Email',
            'Recipient Name',
            'Recipient Email',
            'Description',
            'Weight (kg)',
            'Declared Value',
            'Shipping Cost',
            'Total Cost',
            'Is Paid',
            'Created Date',
            'Delivered Date',
        ];
    }

    /**
     * @param mixed $parcel
     * @return array
     */
    public function map($parcel): array
    {
        return [
            $parcel->tracking_number,
            $parcel->getFormattedStatus(),
            $parcel->carrier->name ?? 'N/A',
            $parcel->sender_name,
            $parcel->sender_email,
            $parcel->recipient_name,
            $parcel->recipient_email,
            $parcel->description,
            $parcel->weight,
            $parcel->declared_value,
            $parcel->shipping_cost,
            $parcel->total_cost,
            $parcel->is_paid ? 'Yes' : 'No',
            $parcel->created_at->format('Y-m-d H:i:s'),
            $parcel->delivered_at ? $parcel->delivered_at->format('Y-m-d H:i:s') : '',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
        ];
    }
}
