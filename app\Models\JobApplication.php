<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class JobApplication extends Model
{
    protected $fillable = [
        'career_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'cover_letter',
        'resume_path',
        'additional_documents',
        'years_of_experience',
        'expected_salary',
        'availability',
        'willing_to_relocate',
        'skills',
        'why_interested',
        'status',
        'admin_notes',
        'reviewed_at',
        'reviewed_by',
        'interview_schedule',
        'referral_source',
        'consent_data_processing',
    ];

    protected $casts = [
        'additional_documents' => 'array',
        'skills' => 'array',
        'willing_to_relocate' => 'boolean',
        'consent_data_processing' => 'boolean',
        'expected_salary' => 'decimal:2',
        'reviewed_at' => 'datetime',
        'interview_schedule' => 'array',
    ];

    public function career(): BelongsTo
    {
        return $this->belongsTo(Career::class);
    }

    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getResumeUrlAttribute()
    {
        return $this->resume_path ? Storage::url($this->resume_path) : null;
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeReviewing($query)
    {
        return $query->where('status', 'reviewing');
    }

    public function scopeShortlisted($query)
    {
        return $query->where('status', 'shortlisted');
    }

    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'bg-warning',
            'reviewing' => 'bg-info',
            'shortlisted' => 'bg-primary',
            'interviewed' => 'bg-secondary',
            'rejected' => 'bg-danger',
            'hired' => 'bg-success',
            default => 'bg-secondary'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'pending' => 'Pending Review',
            'reviewing' => 'Under Review',
            'shortlisted' => 'Shortlisted',
            'interviewed' => 'Interviewed',
            'rejected' => 'Rejected',
            'hired' => 'Hired',
            default => ucfirst($this->status)
        };
    }
}
