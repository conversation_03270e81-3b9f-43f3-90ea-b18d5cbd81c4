<?php

namespace Tests\Feature;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Category;
use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CartTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Product $product;
    protected Category $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'customer',
            'email_verified_at' => now(),
        ]);

        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Product',
            'price' => 99.99,
            'sale_price' => null, // Ensure no sale price
            'stock_quantity' => 10,
            'manage_stock' => true,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function user_can_add_product_to_cart()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/cart/add', [
            'product_id' => $this->product->id,
            'quantity' => 2,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product added to cart successfully.',
            ]);

        // Verify cart was created and item added
        $cart = Cart::where('user_id', $this->user->id)->first();
        $this->assertNotNull($cart);
        $this->assertEquals(2, $cart->item_count);
        $this->assertEquals(199.98, $cart->total_amount);

        // Verify cart item
        $cartItem = $cart->items()->first();
        $this->assertEquals($this->product->id, $cartItem->product_id);
        $this->assertEquals(2, $cartItem->quantity);
        $this->assertEquals(99.99, $cartItem->unit_price);
        $this->assertEquals(199.98, $cartItem->total_price);
    }

    /** @test */
    public function user_cannot_add_product_without_authentication()
    {
        $response = $this->postJson('/cart/add', [
            'product_id' => $this->product->id,
            'quantity' => 1,
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Please log in to add items to cart.',
            ]);
    }

    /** @test */
    public function user_cannot_add_more_than_available_stock()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/cart/add', [
            'product_id' => $this->product->id,
            'quantity' => 15, // More than available stock (10)
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Insufficient stock. Only 10 items available.',
            ]);
    }

    /** @test */
    public function user_can_update_cart_item_quantity()
    {
        $this->actingAs($this->user);

        // First add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        // Update quantity
        $response = $this->postJson('/cart/update', [
            'product_id' => $this->product->id,
            'quantity' => 5,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Cart updated successfully.',
            ]);

        // Verify quantity was updated
        $cart->refresh();
        $cartItem = $cart->items()->first();
        $this->assertEquals(5, $cartItem->quantity);
        $this->assertEquals(499.95, $cartItem->total_price);
    }

    /** @test */
    public function user_can_remove_item_by_setting_quantity_to_zero()
    {
        $this->actingAs($this->user);

        // First add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        // Remove by setting quantity to 0
        $response = $this->postJson('/cart/update', [
            'product_id' => $this->product->id,
            'quantity' => 0,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product removed from cart.',
            ]);

        // Verify item was removed
        $cart->refresh();
        $this->assertEquals(0, $cart->items()->count());
    }

    /** @test */
    public function user_can_remove_product_from_cart()
    {
        $this->actingAs($this->user);

        // First add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        $response = $this->postJson('/cart/remove', [
            'product_id' => $this->product->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product removed from cart.',
            ]);

        // Verify item was removed
        $cart->refresh();
        $this->assertEquals(0, $cart->items()->count());
    }

    /** @test */
    public function user_can_clear_entire_cart()
    {
        $this->actingAs($this->user);

        // Add multiple items to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);
        
        $product2 = Product::factory()->create([
            'category_id' => $this->category->id,
            'price' => 49.99,
        ]);
        $cart->addProduct($product2, 1);

        $response = $this->postJson('/cart/clear');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Cart cleared successfully.',
                'cart_count' => 0,
            ]);

        // Verify cart is empty
        $cart->refresh();
        $this->assertEquals(0, $cart->items()->count());
    }

    /** @test */
    public function user_can_get_cart_count()
    {
        $this->actingAs($this->user);

        // Add items to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 3);

        $response = $this->getJson('/cart/count');

        $response->assertStatus(200)
            ->assertJson([
                'count' => 3,
            ]);
    }

    /** @test */
    public function user_can_save_item_for_later()
    {
        $this->actingAs($this->user);

        // First add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        $response = $this->postJson('/cart/save-for-later', [
            'product_id' => $this->product->id,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Product saved to wishlist.',
            ]);

        // Verify item was removed from cart
        $cart->refresh();
        $this->assertEquals(0, $cart->items()->count());

        // Verify item was added to wishlist
        $this->assertTrue($this->user->wishlist()->where('product_id', $this->product->id)->exists());
    }

    /** @test */
    public function cart_totals_are_calculated_correctly()
    {
        $this->actingAs($this->user);

        $cart = Cart::getCurrent();
        
        // Add first product
        $cart->addProduct($this->product, 2); // 2 * 99.99 = 199.98
        
        // Add second product
        $product2 = Product::factory()->create([
            'category_id' => $this->category->id,
            'price' => 25.50,
        ]);
        $cart->addProduct($product2, 3); // 3 * 25.50 = 76.50

        $cart->refresh();
        
        // Total should be 199.98 + 76.50 = 276.48
        $this->assertEquals(276.48, $cart->subtotal);
        $this->assertEquals(276.48, $cart->total_amount); // No tax or shipping in this test
        $this->assertEquals(5, $cart->item_count); // 2 + 3 items
    }

    /** @test */
    public function cart_view_displays_correctly()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        $response = $this->get('/cart');

        $response->assertStatus(200)
            ->assertViewIs('frontend.cart.index')
            ->assertViewHas('cart')
            ->assertSee($this->product->name)
            ->assertSee('$199.98'); // Total price for 2 items
    }
}
