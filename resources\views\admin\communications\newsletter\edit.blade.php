@extends('layouts.admin')

@section('title', 'Edit Newsletter Subscriber')

@section('page-header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Edit Newsletter Subscriber</h1>
            <p class="text-muted">Update subscriber information for {{ $newsletter->email }}</p>
        </div>
        <div>
            <a href="{{ route('admin.communications.newsletter.show', $newsletter) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Subscriber
            </a>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-edit me-2"></i>
                        Edit Subscriber Information
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.communications.newsletter.update', $newsletter) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" 
                                           class="form-control @error('email') is-invalid @enderror" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email', $newsletter->email) }}" 
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">Name (Optional)</label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name', $newsletter->name) }}">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-control @error('status') is-invalid @enderror" 
                                            id="status" 
                                            name="status" 
                                            required>
                                        <option value="active" {{ old('status', $newsletter->status) === 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="unsubscribed" {{ old('status', $newsletter->status) === 'unsubscribed' ? 'selected' : '' }}>Unsubscribed</option>
                                        <option value="bounced" {{ old('status', $newsletter->status) === 'bounced' ? 'selected' : '' }}>Bounced</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Current Information Display -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Current Information</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Subscribed:</strong> {{ $newsletter->subscribed_at->format('M j, Y g:i A') }}
                                    <br><strong>Source:</strong> {{ ucfirst(str_replace('_', ' ', $newsletter->subscription_source)) }}
                                </div>
                                <div class="col-md-6">
                                    @if($newsletter->unsubscribed_at)
                                        <strong>Unsubscribed:</strong> {{ $newsletter->unsubscribed_at->format('M j, Y g:i A') }}
                                    @endif
                                    @if($newsletter->ip_address)
                                        <br><strong>IP:</strong> {{ $newsletter->ip_address }}
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Status Change Warning -->
                        <div id="status-warning" class="alert alert-warning" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Status Change Notice:</strong>
                            <span id="status-warning-text"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.communications.newsletter.show', $newsletter) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Subscriber
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Danger Zone
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Delete Subscriber</h6>
                            <p class="text-muted mb-0">
                                Permanently delete this subscriber. This action cannot be undone.
                                The subscriber will be completely removed from your newsletter list.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <form method="POST" action="{{ route('admin.communications.newsletter.destroy', $newsletter) }}" 
                                  onsubmit="return confirmDelete()">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>
                                    Delete Subscriber
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const statusWarning = document.getElementById('status-warning');
    const statusWarningText = document.getElementById('status-warning-text');
    const originalStatus = '{{ $newsletter->status }}';

    statusSelect.addEventListener('change', function() {
        const newStatus = this.value;
        
        if (newStatus !== originalStatus) {
            let warningMessage = '';
            
            switch (newStatus) {
                case 'active':
                    if (originalStatus === 'unsubscribed') {
                        warningMessage = 'This will resubscribe the user to your newsletter. They will start receiving emails again.';
                    } else if (originalStatus === 'bounced') {
                        warningMessage = 'This will mark the email as deliverable again. Make sure the email issue has been resolved.';
                    }
                    break;
                case 'unsubscribed':
                    if (originalStatus === 'active') {
                        warningMessage = 'This will unsubscribe the user from your newsletter. They will stop receiving emails.';
                    }
                    break;
                case 'bounced':
                    warningMessage = 'This will mark the email as bounced. Use this status for emails that consistently fail to deliver.';
                    break;
            }
            
            if (warningMessage) {
                statusWarningText.textContent = warningMessage;
                statusWarning.style.display = 'block';
            } else {
                statusWarning.style.display = 'none';
            }
        } else {
            statusWarning.style.display = 'none';
        }
    });
});

function confirmDelete() {
    return confirm('Are you sure you want to delete this subscriber?\n\nThis action cannot be undone. The subscriber will be permanently removed from your newsletter list.\n\nEmail: {{ $newsletter->email }}');
}
</script>
@endpush
@endsection
