<?php

namespace Database\Seeders;

use App\Models\Parcel;
use App\Models\TrackingEvent;
use App\Models\Carrier;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SampleParcelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $carriers = Carrier::all();
        $customer = User::where('role', 'customer')->first();

        // Sample parcel 1 - In Transit
        $parcel1 = Parcel::create([
            'tracking_number' => 'ATX-2024-12345678',
            'carrier_id' => $carriers->where('code', 'ATX')->first()->id,
            'user_id' => $customer->id,
            'sender_name' => 'Atrix Logistics Warehouse',
            'sender_email' => '<EMAIL>',
            'sender_phone' => '+****************',
            'sender_address' => '123 Logistics Ave',
            'sender_city' => 'Transport City',
            'sender_state' => 'TC',
            'sender_postal_code' => '12345',
            'sender_country' => 'USA',
            'recipient_name' => '<PERSON>',
            'recipient_email' => '<EMAIL>',
            'recipient_phone' => '+****************',
            'recipient_address' => '456 Customer St',
            'recipient_city' => 'Customer City',
            'recipient_state' => 'CC',
            'recipient_postal_code' => '54321',
            'recipient_country' => 'USA',
            'description' => 'Electronics Package - Laptop Computer',
            'weight' => 2.5,
            'dimensions' => '40x30x10 cm',
            'declared_value' => 1200.00,
            'service_type' => 'express',
            'status' => 'in_transit',
            'shipping_cost' => 25.50,
            'total_cost' => 25.50,
            'is_paid' => true,
            'shipped_at' => now()->subDays(2),
            'estimated_delivery_date' => now()->addDay(),
        ]);

        // Add tracking events for parcel 1
        TrackingEvent::create([
            'parcel_id' => $parcel1->id,
            'status' => 'pending',
            'location' => 'Transport City, TC',
            'description' => 'Package received at origin facility',
            'event_date' => now()->subDays(2)->subHours(8),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel1->id,
            'status' => 'picked_up',
            'location' => 'Transport City, TC',
            'description' => 'Package picked up by carrier',
            'event_date' => now()->subDays(2)->subHours(6),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel1->id,
            'status' => 'in_transit',
            'location' => 'Chicago, IL',
            'description' => 'Package in transit to destination',
            'event_date' => now()->subDay()->subHours(4),
            'is_public' => true,
        ]);

        // Sample parcel 2 - Delivered
        $parcel2 = Parcel::create([
            'tracking_number' => 'ATX-2024-87654321',
            'carrier_id' => $carriers->where('code', 'DHL')->first()->id,
            'user_id' => $customer->id,
            'sender_name' => 'Online Store Inc',
            'sender_email' => '<EMAIL>',
            'sender_phone' => '+****************',
            'sender_address' => '789 Commerce Blvd',
            'sender_city' => 'Commerce City',
            'sender_state' => 'CO',
            'sender_postal_code' => '80022',
            'sender_country' => 'USA',
            'recipient_name' => 'Jane Smith',
            'recipient_email' => '<EMAIL>',
            'recipient_phone' => '+****************',
            'recipient_address' => '321 Residential Ave',
            'recipient_city' => 'Hometown',
            'recipient_state' => 'HT',
            'recipient_postal_code' => '98765',
            'recipient_country' => 'USA',
            'description' => 'Clothing Package - Winter Jacket',
            'weight' => 1.2,
            'dimensions' => '35x25x8 cm',
            'declared_value' => 150.00,
            'service_type' => 'standard',
            'status' => 'delivered',
            'shipping_cost' => 15.75,
            'total_cost' => 15.75,
            'is_paid' => true,
            'shipped_at' => now()->subDays(5),
            'delivered_at' => now()->subDays(1),
            'estimated_delivery_date' => now()->subDays(1),
        ]);

        // Add tracking events for parcel 2
        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'status' => 'pending',
            'location' => 'Commerce City, CO',
            'description' => 'Package received at origin facility',
            'event_date' => now()->subDays(5)->subHours(10),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'status' => 'picked_up',
            'location' => 'Commerce City, CO',
            'description' => 'Package picked up by DHL',
            'event_date' => now()->subDays(5)->subHours(8),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'status' => 'in_transit',
            'location' => 'Denver, CO',
            'description' => 'Package departed origin facility',
            'event_date' => now()->subDays(4)->subHours(12),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'status' => 'in_transit',
            'location' => 'Kansas City, MO',
            'description' => 'Package in transit',
            'event_date' => now()->subDays(3)->subHours(8),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'status' => 'out_for_delivery',
            'location' => 'Hometown, HT',
            'description' => 'Package out for delivery',
            'event_date' => now()->subDays(1)->subHours(6),
            'is_public' => true,
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'status' => 'delivered',
            'location' => 'Hometown, HT',
            'description' => 'Package delivered successfully',
            'event_date' => now()->subDays(1)->subHours(2),
            'is_public' => true,
        ]);

        // Sample parcel 3 - Pending
        $parcel3 = Parcel::create([
            'tracking_number' => 'ATX-2024-11223344',
            'carrier_id' => $carriers->where('code', 'USPS')->first()->id,
            'user_id' => null, // No user associated
            'sender_name' => 'Small Business LLC',
            'sender_email' => '<EMAIL>',
            'sender_phone' => '+****************',
            'sender_address' => '555 Business Park Dr',
            'sender_city' => 'Business City',
            'sender_state' => 'BC',
            'sender_postal_code' => '11111',
            'sender_country' => 'USA',
            'recipient_name' => 'Bob Johnson',
            'recipient_email' => '<EMAIL>',
            'recipient_phone' => '+****************',
            'recipient_address' => '999 Home Street',
            'recipient_city' => 'Home City',
            'recipient_state' => 'HC',
            'recipient_postal_code' => '22222',
            'recipient_country' => 'USA',
            'description' => 'Books Package - Educational Materials',
            'weight' => 3.8,
            'dimensions' => '30x20x15 cm',
            'declared_value' => 75.00,
            'service_type' => 'standard',
            'status' => 'pending',
            'shipping_cost' => 12.25,
            'total_cost' => 12.25,
            'is_paid' => true,
            'estimated_delivery_date' => now()->addDays(3),
        ]);

        // Add tracking event for parcel 3
        TrackingEvent::create([
            'parcel_id' => $parcel3->id,
            'status' => 'pending',
            'location' => 'Business City, BC',
            'description' => 'Package received and processing',
            'event_date' => now()->subHours(2),
            'is_public' => true,
        ]);
    }
}
