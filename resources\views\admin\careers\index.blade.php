@extends('layouts.admin')

@section('title', 'Career Management')
@section('page-title', 'Career Positions')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.careers.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add New Position
        </a>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Total Positions
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $careers->total() }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-briefcase fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Active Positions
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $careers->where('is_active', true)->count() }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Featured Positions
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $careers->where('is_featured', true)->count() }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-star fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Total Applications
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $careers->sum('applications_count') }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Careers Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Career Positions</h6>
            </div>
            <div class="card-body">
                @if($careers->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Location</th>
                                    <th>Type</th>
                                    <th>Applications</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($careers as $career)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($career->is_featured)
                                                    <i class="fas fa-star text-warning me-2" title="Featured"></i>
                                                @endif
                                                <div>
                                                    <div class="font-weight-bold">{{ $career->title }}</div>
                                                    <div class="text-muted small">{{ $career->experience_level }} level</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $career->department ?? 'N/A' }}</td>
                                        <td>{{ $career->location }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ ucfirst($career->employment_type) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $career->applications_count }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $career->is_active ? 'success' : 'secondary' }}">
                                                {{ $career->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.cms.careers.show', $career) }}" 
                                                   class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.cms.careers.edit', $career) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        onclick="toggleStatus({{ $career->id }})" title="Toggle Status">
                                                    <i class="fas fa-{{ $career->is_active ? 'eye-slash' : 'eye' }}"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete({{ $career->id }})" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $careers->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-briefcase fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-gray-600">No Career Positions Found</h5>
                        <p class="text-gray-500">Start by creating your first career position.</p>
                        <a href="{{ route('admin.cms.careers.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add New Position
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this career position?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone and will also delete all associated job applications.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Position</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function confirmDelete(careerId) {
        const form = document.getElementById('deleteForm');
        form.action = `/admin/cms/careers/${careerId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus(careerId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/cms/careers/${careerId}/toggle-status`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        form.appendChild(csrfToken);
        document.body.appendChild(form);
        form.submit();
    }
</script>
@endpush
