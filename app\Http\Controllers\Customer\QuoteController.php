<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Quote;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class QuoteController extends Controller
{
    /**
     * Display customer's quotes
     */
    public function index(Request $request): View
    {
        $user = Auth::user();

        $query = $user->quotes()->with(['assignedTo']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('service_type')) {
            $query->where('service_type', $request->service_type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('quote_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $quotes = $query->latest()->paginate(15);

        // Get filter options
        $statuses = [
            'pending' => 'Pending',
            'reviewing' => 'Reviewing',
            'quoted' => 'Quoted',
            'accepted' => 'Accepted',
            'rejected' => 'Rejected',
            'expired' => 'Expired',
        ];

        $serviceTypes = [
            'domestic_shipping' => 'Domestic Shipping',
            'international_shipping' => 'International Shipping',
            'express_delivery' => 'Express Delivery',
            'freight_shipping' => 'Freight Shipping',
            'warehousing' => 'Warehousing',
            'custom_logistics' => 'Custom Logistics',
            'bulk_shipping' => 'Bulk Shipping',
            'specialized_transport' => 'Specialized Transport',
        ];

        return view('customer.quotes.index', compact('quotes', 'statuses', 'serviceTypes'));
    }

    /**
     * Show the form for creating a new quote request
     */
    public function create(): View
    {
        $serviceTypes = [
            'domestic_shipping' => 'Domestic Shipping',
            'international_shipping' => 'International Shipping',
            'express_delivery' => 'Express Delivery',
            'freight_shipping' => 'Freight Shipping',
            'warehousing' => 'Warehousing',
            'custom_logistics' => 'Custom Logistics',
            'bulk_shipping' => 'Bulk Shipping',
            'specialized_transport' => 'Specialized Transport',
        ];

        $priorities = [
            'standard' => 'Standard',
            'urgent' => 'Urgent',
            'express' => 'Express',
        ];

        return view('customer.quotes.create', compact('serviceTypes', 'priorities'));
    }

    /**
     * Store a newly created quote request
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        // First validate the quote type
        $request->validate([
            'quote_type' => 'required|in:shipping,product',
        ]);

        $quoteType = $request->input('quote_type', 'shipping');

        // Base validation rules
        $rules = [
            'quote_type' => 'required|in:shipping,product',
            'priority' => 'required|in:standard,urgent,express',
        ];

        // Add shipping-specific rules
        if ($quoteType === 'shipping') {
            $rules = array_merge($rules, [
                'service_type' => 'required|in:domestic_shipping,international_shipping,express_delivery,freight_shipping,warehousing,custom_logistics,bulk_shipping,specialized_transport',
                'description' => 'required|string|max:5000',
                'origin_address' => 'required|string|max:500',
                'origin_city' => 'required|string|max:100',
                'origin_state' => 'nullable|string|max:100',
                'origin_postal_code' => 'nullable|string|max:20',
                'origin_country' => 'required|string|max:100',
                'destination_address' => 'required|string|max:500',
                'destination_city' => 'required|string|max:100',
                'destination_state' => 'nullable|string|max:100',
                'destination_postal_code' => 'nullable|string|max:20',
                'destination_country' => 'required|string|max:100',
                'package_count' => 'required|integer|min:1',
                'total_weight' => 'nullable|numeric|min:0',
                'weight_unit' => 'required|in:kg,lbs',
                'package_type' => 'nullable|string|max:100',
                'package_description' => 'nullable|string|max:1000',
                'declared_value' => 'nullable|numeric|min:0',
                'preferred_pickup_date' => 'nullable|date|after_or_equal:today',
                'required_delivery_date' => 'nullable|date|after_or_equal:preferred_pickup_date',
                'delivery_speed' => 'required|in:standard,express,overnight,same_day',
                'fragile' => 'nullable|in:0,1',
                'hazardous' => 'nullable|in:0,1',
                'insurance_required' => 'nullable|in:0,1',
                'signature_required' => 'nullable|in:0,1',
                'customer_notes' => 'nullable|string|max:2000',
            ]);
        } else {
            // Product quote rules
            $rules = array_merge($rules, [
                'description' => 'nullable|string|max:5000',
                'products' => 'required|json',
                'products_total' => 'required|numeric|min:0',
                'product_requirements' => 'nullable|string|max:2000',
            ]);
        }

        try {
            $validated = $request->validate($rules);
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => array_values($e->validator->errors()->all())
                ], 422);
            }
            throw $e;
        }

        // Add customer information
        $validated['user_id'] = $user->id;
        $validated['customer_name'] = $user->name;
        $validated['customer_email'] = $user->email;
        $validated['customer_phone'] = $user->phone;
        $validated['company_name'] = $user->company_name;

        // Handle product quote specific data
        if ($quoteType === 'product') {
            $products = json_decode($validated['products'], true);

            // Validate products exist and update with current data
            $validatedProducts = [];
            $totalValue = 0;

            foreach ($products as $productData) {
                $product = \App\Models\Product::find($productData['product_id']);
                if ($product && $product->is_active) {
                    $quantity = max(1, intval($productData['quantity']));
                    $priceAtTime = $product->isOnSale() ? $product->sale_price : $product->price;

                    $validatedProducts[] = [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'product_sku' => $product->sku,
                        'quantity' => $quantity,
                        'price_at_time' => $priceAtTime,
                        'total' => $priceAtTime * $quantity,
                        'notes' => $productData['notes'] ?? null,
                    ];

                    $totalValue += $priceAtTime * $quantity;
                }
            }

            if (empty($validatedProducts)) {
                if ($request->wantsJson() || $request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['No valid products found in your quote request.']
                    ], 422);
                }
                return back()->withErrors(['products' => 'No valid products found in your quote request.']);
            }

            $validated['products'] = $validatedProducts;
            $validated['products_total'] = $totalValue;
            $validated['service_type'] = 'product_inquiry';
        } else {
            // Handle shipping quote specific data

            // Handle dimensions
            if ($request->filled(['length', 'width', 'height'])) {
                $validated['dimensions'] = [
                    'length' => $request->length,
                    'width' => $request->width,
                    'height' => $request->height,
                    'unit' => $request->dimension_unit ?? 'cm',
                ];
            }

            // Handle additional services
            if ($request->filled('additional_services')) {
                $validated['additional_services'] = $request->additional_services;
            }

            // Convert string values to boolean for shipping quotes
            $validated['fragile'] = (bool) ($validated['fragile'] ?? 0);
            $validated['hazardous'] = (bool) ($validated['hazardous'] ?? 0);
            $validated['insurance_required'] = (bool) ($validated['insurance_required'] ?? 0);
            $validated['signature_required'] = (bool) ($validated['signature_required'] ?? 0);
        }

        $validated['status'] = 'pending';

        $quote = Quote::create($validated);

        $message = "Quote request submitted successfully! Quote #{$quote->quote_number}";

        if ($quoteType === 'product') {
            $productCount = count($validatedProducts);
            $message .= " for {$productCount} product(s).";
        }

        $message .= ' We will review and provide pricing within 24 hours.';

        // Handle AJAX requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'redirect' => route('customer.quotes.show', $quote),
                'quote_number' => $quote->quote_number
            ]);
        }

        return redirect()->route('customer.quotes.show', $quote)
                        ->with('success', $message);
    }

    /**
     * Show quote lookup form
     */
    public function lookup(): View
    {
        return view('customer.quotes.lookup');
    }

    /**
     * Lookup quote by number and email
     */
    public function lookupQuote(Request $request): RedirectResponse
    {
        $request->validate([
            'quote_number' => 'required|string',
            'email' => 'required|email',
        ]);

        $quote = Quote::where('quote_number', $request->quote_number)
                    ->where('customer_email', $request->email)
                    ->first();

        if (!$quote) {
            return back()->withErrors(['quote_number' => 'Quote not found or email does not match.']);
        }

        // Check if user has access to this quote
        $user = Auth::user();
        if ($user->role === 'customer' && $quote->user_id !== $user->id) {
            return back()->withErrors(['quote_number' => 'You do not have access to this quote.']);
        }

        return redirect()->route('customer.quotes.show', $quote);
    }

    /**
     * Display the specified quote
     */
    public function show(Quote $quote): View
    {
        $user = Auth::user();

        // Check access permissions
        if ($user->role === 'customer' && $quote->user_id !== $user->id) {
            abort(404);
        }

        $quote->load(['assignedTo']);

        return view('customer.quotes.show', compact('quote'));
    }

    /**
     * Accept a quote
     */
    public function accept(Quote $quote): RedirectResponse
    {
        // Ensure the quote belongs to the authenticated customer
        if ($quote->user_id !== Auth::id()) {
            abort(404);
        }

        // Only allow accepting if quote is quoted and not expired
        if (!$quote->canBeAccepted()) {
            return redirect()->route('customer.quotes.show', $quote)
                           ->with('error', 'This quote cannot be accepted.');
        }

        $quote->update([
            'status' => 'accepted',
            'accepted_at' => now(),
        ]);

        return redirect()->route('customer.quotes.show', $quote)
                        ->with('success', 'Quote accepted successfully!');
    }

    /**
     * Reject a quote
     */
    public function reject(Quote $quote): RedirectResponse
    {
        // Ensure the quote belongs to the authenticated customer
        if ($quote->user_id !== Auth::id()) {
            abort(404);
        }

        // Only allow rejecting if quote can be rejected
        if (!$quote->canBeRejected()) {
            return redirect()->route('customer.quotes.show', $quote)
                           ->with('error', 'This quote cannot be rejected.');
        }

        $quote->update([
            'status' => 'rejected',
            'rejected_at' => now(),
        ]);

        return redirect()->route('customer.quotes.show', $quote)
                        ->with('success', 'Quote rejected.');
    }

    /**
     * Add customer notes to a quote
     */
    public function addNotes(Request $request, Quote $quote): RedirectResponse
    {
        // Ensure the quote belongs to the authenticated customer
        if ($quote->user_id !== Auth::id()) {
            abort(404);
        }

        $request->validate([
            'customer_notes' => 'required|string|max:2000',
        ]);

        $quote->update(['customer_notes' => $request->customer_notes]);

        return redirect()->route('customer.quotes.show', $quote)
                        ->with('success', 'Notes added successfully.');
    }
}
