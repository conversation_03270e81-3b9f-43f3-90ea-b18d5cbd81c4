<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Contact extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'status',
        'admin_notes',
        'read_at',
        'replied_at',
        'replied_by',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'read_at' => 'datetime',
        'replied_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when contact status changes
        static::saved(function ($contact) {
            cache()->forget('admin_new_contacts_count');
        });

        static::deleted(function ($contact) {
            cache()->forget('admin_new_contacts_count');
        });
    }

    /**
     * Get the user who replied to this contact
     */
    public function repliedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'replied_by');
    }

    /**
     * Mark contact as read
     */
    public function markAsRead(): void
    {
        if (!$this->read_at) {
            $this->update([
                'read_at' => now(),
                'status' => 'read'
            ]);
            // Clear the admin contacts count cache
            cache()->forget('admin_new_contacts_count');
        }
    }

    /**
     * Mark contact as replied
     */
    public function markAsReplied(int $userId): void
    {
        $this->update([
            'replied_at' => now(),
            'replied_by' => $userId,
            'status' => 'replied'
        ]);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for unread contacts
     */
    public function scopeUnread($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeAttribute(): string
    {
        return match($this->status) {
            'new' => 'badge-primary',
            'read' => 'badge-info',
            'replied' => 'badge-success',
            'closed' => 'badge-secondary',
            default => 'badge-light'
        };
    }
}
