# Image Processing Service - Global Usage Guide

## Overview

The `ImageProcessingService` is now globally available throughout the application and provides automatic image conversion, optimization, and sanitization for all file uploads.

## Features

- **Automatic WebP Conversion**: All images (except favicons) are converted to WebP format
- **Favicon ICO Conversion**: Favicons are resized and converted to ICO format
- **Image Optimization**: Large images are automatically resized while maintaining aspect ratio
- **Security Validation**: File size, MIME type, and content validation
- **Global Availability**: Accessible from any controller or service

## Usage Methods

### 1. Service Container (Recommended)

```php
// In any controller constructor
public function __construct(ImageProcessingService $imageService)
{
    $this->imageService = $imageService;
}

// Or resolve from container
$imageService = app(ImageProcessingService::class);
```

### 2. Helper Class (Quick Access)

```php
use App\Helpers\ImageHelper;

// Process specific image types
$path = ImageHelper::processProduct($uploadedFile);
$path = ImageHelper::processCategory($uploadedFile);
$path = ImageHelper::processSlider($uploadedFile);
$path = ImageHelper::processTeamMember($uploadedFile);

// Process with custom options
$path = ImageHelper::processWithOptions($uploadedFile, [
    'directory' => 'uploads/custom',
    'format' => 'webp',
    'quality' => 90,
    'max_width' => 1200,
    'max_height' => 800
]);

// Delete images
ImageHelper::deleteImage($imagePath);

// Process multiple images
$paths = ImageHelper::processMultiple($uploadedFiles, 'product');
```

### 3. Direct Service Methods

```php
// Basic processing (site settings)
$path = $imageService->processImage($file, 'uploads/settings', 'site_logo');

// Advanced processing with options
$path = $imageService->processImageWithOptions($file, [
    'directory' => 'uploads/products',
    'prefix' => 'product',
    'format' => 'webp',
    'quality' => 85,
    'max_width' => 1200,
    'max_height' => 1200
]);

// Predefined methods
$path = $imageService->processProductImage($file);
$path = $imageService->processSliderImage($file);
$path = $imageService->processCategoryImage($file);
$path = $imageService->processTeamMemberPhoto($file);
```

## Configuration Options

| Option | Default | Description |
|--------|---------|-------------|
| `directory` | `uploads/images` | Storage directory |
| `prefix` | `img` | Filename prefix |
| `format` | `webp` | Output format (webp, ico, original) |
| `quality` | `85` | Image quality (1-100) |
| `max_width` | `1920` | Maximum width in pixels |
| `max_height` | `1080` | Maximum height in pixels |

## Predefined Configurations

### Product Images
- Directory: `uploads/products`
- Format: WebP
- Quality: 85%
- Max Size: 1200x1200

### Slider Images
- Directory: `uploads/sliders`
- Format: WebP
- Quality: 90%
- Max Size: 1920x1080

### Category Images
- Directory: `uploads/categories`
- Format: WebP
- Quality: 85%
- Max Size: 800x600

### Team Member Photos
- Directory: `uploads/team`
- Format: WebP
- Quality: 85%
- Max Size: 600x600

### Site Settings (Logo/Favicon)
- Directory: `uploads/settings`
- Logo: WebP format
- Favicon: ICO format (32x32)

## Error Handling

The service includes comprehensive error handling with fallbacks:

```php
try {
    $path = ImageHelper::processProduct($file);
    \Log::info('Image processed successfully', ['path' => $path]);
} catch (\Exception $e) {
    \Log::error('Image processing failed', ['error' => $e->getMessage()]);
    // Fallback to original storage
    $path = $file->store('uploads/products', 'public');
}
```

## Controllers Updated

The following controllers have been updated to use the ImageProcessingService:

- ✅ **SiteSettingController** - Logo and favicon processing
- ✅ **ProductController** - Featured and gallery images
- ✅ **CategoryController** - Category images
- 🔄 **SliderController** - Slider images (pending)
- 🔄 **TeamMemberController** - Team photos (pending)

## Benefits

1. **Performance**: WebP images are 25-35% smaller than JPEG/PNG
2. **Security**: Comprehensive validation and sanitization
3. **Consistency**: Standardized image processing across the application
4. **Maintainability**: Centralized image processing logic
5. **Flexibility**: Easy to customize for different use cases

## Testing

Run the image processing tests:

```bash
php artisan test tests/Unit/ImageProcessingServiceTest.php
```

## Troubleshooting

### Common Issues

1. **Service not found**: Clear cache with `php artisan cache:clear`
2. **Permission errors**: Check storage directory permissions
3. **Memory issues**: Increase PHP memory limit for large images
4. **Extension missing**: Ensure GD extension is installed

### Debug Logging

The service logs all processing activities. Check `storage/logs/laravel.log` for:
- File upload attempts
- Processing success/failure
- Error details
- Performance metrics
