@extends('layouts.admin')

@section('title', 'Quote Management')
@section('page-title', 'Quote Management')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.quotes.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> New Quote
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="exportQuotes()">
            <i class="fas fa-download me-1"></i> Export
        </button>
        <button type="button" class="btn btn-outline-info" onclick="refreshQuotes()">
            <i class="fas fa-sync me-1"></i> Refresh
        </button>
    </div>
@endsection

@section('content')
    <!-- Quote Statistics -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['total_quotes']) }}</h4>
                            <p class="mb-0">Total Quotes</p>
                        </div>
                        <div>
                            <i class="fas fa-quote-left fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['pending_quotes']) }}</h4>
                            <p class="mb-0">Pending</p>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['active_quotes']) }}</h4>
                            <p class="mb-0">Active</p>
                        </div>
                        <div>
                            <i class="fas fa-tasks fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['accepted_quotes']) }}</h4>
                            <p class="mb-0">Accepted</p>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">${{ number_format($stats['total_value'], 0) }}</h4>
                            <p class="mb-0">Total Value</p>
                        </div>
                        <div>
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">${{ number_format($stats['avg_quote_value'], 0) }}</h4>
                            <p class="mb-0">Avg Value</p>
                        </div>
                        <div>
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.quotes.index') }}" class="row g-3">
                <div class="col-md-2">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Quote #, customer, company...">
                </div>

                <div class="col-md-2">
                    <label for="quote_type" class="form-label">Quote Type</label>
                    <select class="form-select" id="quote_type" name="quote_type">
                        <option value="">All Types</option>
                        <option value="shipping" {{ request('quote_type') === 'shipping' ? 'selected' : '' }}>Shipping</option>
                        <option value="product" {{ request('quote_type') === 'product' ? 'selected' : '' }}>Product</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ request('status') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="service_type" class="form-label">Service</label>
                    <select class="form-select" id="service_type" name="service_type">
                        <option value="">All Services</option>
                        @foreach($serviceTypes as $value => $label)
                            <option value="{{ $value }}" {{ request('service_type') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                        <option value="product_inquiry" {{ request('service_type') === 'product_inquiry' ? 'selected' : '' }}>
                            Product Inquiry
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">All Priorities</option>
                        @foreach($priorities as $value => $label)
                            <option value="{{ $value }}" {{ request('priority') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="assigned_to" class="form-label">Assigned To</label>
                    <select class="form-select" id="assigned_to" name="assigned_to">
                        <option value="">All Admins</option>
                        @foreach($admins as $admin)
                            <option value="{{ $admin->id }}" {{ request('assigned_to') == $admin->id ? 'selected' : '' }}>
                                {{ $admin->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quotes Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-quote-left me-2"></i>
                Quotes ({{ $quotes->total() }})
            </h5>
            
            @if($quotes->count() > 0)
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            data-bs-toggle="dropdown">
                        Bulk Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="showBulkAssignModal()">
                            <i class="fas fa-user-plus me-2"></i> Assign Selected
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showBulkStatusModal()">
                            <i class="fas fa-edit me-2"></i> Update Status
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                            <i class="fas fa-trash me-2"></i> Delete Selected
                        </a></li>
                    </ul>
                </div>
            @endif
        </div>
        
        <div class="card-body">
            @if($quotes->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Quote #</th>
                                <th>Customer</th>
                                <th>Type & Service</th>
                                <th>Details</th>
                                <th>Value</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Assigned</th>
                                <th>Created</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($quotes as $quote)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input quote-checkbox" 
                                               value="{{ $quote->id }}">
                                    </td>
                                    <td>
                                        <strong>{{ $quote->quote_number }}</strong>
                                        @if($quote->isExpired())
                                            <br><small class="text-danger">Expired</small>
                                        @elseif($quote->expires_at)
                                            <br><small class="text-muted">Expires {{ $quote->expires_at->diffForHumans() }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $quote->customer_name }}</strong>
                                            @if($quote->company_name)
                                                <br><small class="text-muted">{{ $quote->company_name }}</small>
                                            @endif
                                            <br><a href="mailto:{{ $quote->customer_email }}">{{ $quote->customer_email }}</a>
                                            @if($quote->customer_phone)
                                                <br><small class="text-muted">{{ $quote->customer_phone }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="badge bg-{{ $quote->quote_type === 'product' ? 'success' : 'primary' }}">
                                                {{ ucwords($quote->quote_type) }}
                                            </span>
                                            @if($quote->quote_source)
                                                <span class="badge bg-secondary">{{ ucwords(str_replace('_', ' ', $quote->quote_source)) }}</span>
                                            @endif
                                            <br>
                                            <span class="badge bg-info">{{ $quote->formatted_service_type }}</span>
                                            @if($quote->isShippingQuote() && $quote->package_count > 1)
                                                <br><small class="text-muted">{{ $quote->package_count }} packages</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($quote->isShippingQuote())
                                            <div>
                                                <strong>From:</strong> {{ $quote->origin_city ?? 'N/A' }}, {{ $quote->origin_country ?? 'N/A' }}
                                                <br><strong>To:</strong> {{ $quote->destination_city ?? 'N/A' }}, {{ $quote->destination_country ?? 'N/A' }}
                                                @if($quote->total_weight)
                                                    <br><small class="text-muted">Weight: {{ $quote->total_weight }} {{ $quote->weight_unit }}</small>
                                                @endif
                                            </div>
                                        @elseif($quote->isProductQuote())
                                            <div>
                                                @if($quote->products)
                                                    <strong>{{ $quote->getProductsCount() }} Product(s)</strong>
                                                    <br><small class="text-muted">Total Value: ${{ number_format($quote->products_total, 2) }}</small>
                                                    @if($quote->getProductsWithDetails()->count() > 0)
                                                        @php $firstProduct = $quote->getProductsWithDetails()->first(); @endphp
                                                        <br><small class="text-muted">{{ $firstProduct['product']->name ?? 'Product' }}
                                                        @if($quote->getProductsCount() > 1)
                                                            + {{ $quote->getProductsCount() - 1 }} more
                                                        @endif
                                                        </small>
                                                    @endif
                                                @else
                                                    <span class="text-muted">Product inquiry</span>
                                                @endif
                                            </div>
                                        @else
                                            <span class="text-muted">Details not available</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($quote->final_price)
                                            <strong>${{ number_format($quote->final_price, 2) }}</strong>
                                            @if($quote->discount_amount > 0)
                                                <br><small class="text-success">-${{ number_format($quote->discount_amount, 2) }} discount</small>
                                            @endif
                                        @elseif($quote->quoted_price)
                                            <strong>${{ number_format($quote->quoted_price, 2) }}</strong>
                                        @else
                                            <span class="text-muted">Not quoted</span>
                                            @if($quote->isProductQuote() && $quote->products_total)
                                                <br><small class="text-info">Products: ${{ number_format($quote->products_total, 2) }}</small>
                                            @endif
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $quote->priority_badge_color }}">
                                            {{ $quote->formatted_priority }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $quote->status_badge_color }}">
                                            {{ $quote->formatted_status }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($quote->assignedTo)
                                            <span class="badge bg-secondary">{{ $quote->assignedTo->name }}</span>
                                        @else
                                            <span class="text-muted">Unassigned</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $quote->created_at->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $quote->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.quotes.show', $quote) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($quote->canBeQuoted())
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="showQuoteModal({{ $quote->id }})" title="Provide Quote">
                                                    <i class="fas fa-dollar-sign"></i>
                                                </button>
                                            @endif
                                            <a href="{{ route('admin.quotes.edit', $quote) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-3">
                    {{ $quotes->links('pagination.admin') }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-quote-left fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No quotes found</h5>
                    <p class="text-muted">Start by creating your first quote or adjust your filters.</p>
                    <a href="{{ route('admin.quotes.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Create First Quote
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Provide Quote Modal -->
    <div class="modal fade" id="quoteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Provide Quote</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="quoteForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="quoted_price" class="form-label">Quoted Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="quoted_price" name="quoted_price" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="discount_amount" class="form-label">Discount Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="expires_at" class="form-label">Quote Expires At *</label>
                            <input type="datetime-local" class="form-control" id="expires_at" name="expires_at" required>
                        </div>

                        <div class="mb-3">
                            <label for="pricing_breakdown" class="form-label">Pricing Breakdown</label>
                            <textarea class="form-control" id="pricing_breakdown" name="pricing_breakdown" rows="4"
                                      placeholder="Base shipping: $X&#10;Fuel surcharge: $X&#10;Insurance: $X&#10;Total: $X"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Admin Notes</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                      placeholder="Internal notes about this quote..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitQuote()">Provide Quote</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    let currentQuoteId = null;

    // Select all functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.quote-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Show quote modal
    function showQuoteModal(quoteId) {
        currentQuoteId = quoteId;

        // Set default expiry date (7 days from now)
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 7);
        document.getElementById('expires_at').value = expiryDate.toISOString().slice(0, 16);

        new bootstrap.Modal(document.getElementById('quoteModal')).show();
    }

    // Submit quote
    function submitQuote() {
        const form = document.getElementById('quoteForm');
        const formData = new FormData(form);

        fetch(`/admin/quotes/${currentQuoteId}/provide-quote`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(Object.fromEntries(formData))
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('quoteModal')).hide();
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }

    // Bulk actions
    function showBulkAssignModal() {
        const selectedQuotes = getSelectedQuotes();
        if (selectedQuotes.length === 0) {
            alert('Please select at least one quote.');
            return;
        }

        const adminId = prompt('Enter admin ID to assign to:');
        if (adminId) {
            bulkAction('assign', { assigned_to: adminId });
        }
    }

    function showBulkStatusModal() {
        const selectedQuotes = getSelectedQuotes();
        if (selectedQuotes.length === 0) {
            alert('Please select at least one quote.');
            return;
        }

        const status = prompt('Enter new status (pending, reviewing, quoted, accepted, rejected, expired):');
        if (status) {
            bulkAction('update_status', { status: status });
        }
    }

    function bulkAction(action, additionalData = {}) {
        const selectedQuotes = getSelectedQuotes();

        if (selectedQuotes.length === 0) {
            alert('Please select at least one quote.');
            return;
        }

        const actionText = action === 'delete' ? 'delete' : action.replace('_', ' ');
        if (confirm(`Are you sure you want to ${actionText} ${selectedQuotes.length} quote(s)?`)) {
            fetch('{{ route("admin.quotes.bulk-action") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    quote_ids: selectedQuotes,
                    action: action,
                    ...additionalData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }

    function getSelectedQuotes() {
        return Array.from(document.querySelectorAll('.quote-checkbox:checked'))
                   .map(cb => cb.value);
    }

    // Utility functions
    function refreshQuotes() {
        location.reload();
    }

    function exportQuotes() {
        alert('Export functionality coming soon!');
    }

    // Auto-calculate final price
    document.getElementById('quoted_price').addEventListener('input', calculateFinalPrice);
    document.getElementById('discount_amount').addEventListener('input', calculateFinalPrice);

    function calculateFinalPrice() {
        const quotedPrice = parseFloat(document.getElementById('quoted_price').value) || 0;
        const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
        const finalPrice = quotedPrice - discountAmount;

        // You can display the final price somewhere if needed
        console.log('Final Price:', finalPrice);
    }
</script>
@endpush
