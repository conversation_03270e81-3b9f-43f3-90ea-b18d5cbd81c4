@extends('layouts.customer')

@section('title', 'My Orders')
@section('page-title', 'My Orders')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <a href="{{ route('customer.support.create') }}" class="btn btn-primary">
            <i class="fas fa-headset me-1"></i> Get Support
        </a>
    </div>
@endsection

@section('content')
    <!-- Order Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="d-flex justify-content-between text-dark align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $orders->total() }}</h3>
                        <p class="mb-0">Total Orders</p>
                    </div>
                    <div>
                        <i class="fas fa-receipt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $orders->where('status', 'pending')->count() }}</h3>
                        <p class="mb-0">Pending</p>
                    </div>
                    <div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $orders->whereIn('status', ['shipped', 'processing'])->count() }}</h3>
                        <p class="mb-0">In Progress</p>
                    </div>
                    <div>
                        <i class="fas fa-truck fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $orders->where('status', 'delivered')->count() }}</h3>
                        <p class="mb-0">Delivered</p>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('customer.orders') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Order number, product...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ request('status') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="payment_status" class="form-label">Payment</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="">All Payments</option>
                        @foreach($paymentStatuses as $value => $label)
                            <option value="{{ $value }}" {{ request('payment_status') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request('date_to') }}">
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-receipt me-2"></i>
                Your Orders ({{ $orders->total() }})
            </h5>
        </div>
        
        <div class="card-body">
            @if($orders->count() > 0)
                <div class="row">
                    @foreach($orders as $order)
                        <div class="col-12 mb-4">
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <strong>Order #{{ $order->order_number }}</strong>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-{{ $order->status_badge_color }}">
                                                {{ $order->formatted_status }}
                                            </span>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-{{ $order->payment_status_badge_color }}">
                                                {{ $order->formatted_payment_status }}
                                            </span>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <strong>@currency($order->total_amount)</strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="row">
                                                @foreach($order->items->take(3) as $item)
                                                    <div class="col-md-4 mb-3">
                                                        <div class="d-flex align-items-center">
                                                            @if($item->product && $item->product->featured_image)
                                                                <img src="{{ Storage::url($item->product->featured_image) }}" 
                                                                     alt="{{ $item->product_name }}" 
                                                                     class="me-3" 
                                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                                            @else
                                                                <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                                     style="width: 50px; height: 50px;">
                                                                    <i class="fas fa-image text-muted"></i>
                                                                </div>
                                                            @endif
                                                            <div>
                                                                <strong>{{ Str::limit($item->product_name, 20) }}</strong>
                                                                <br><small class="text-muted">Qty: {{ $item->quantity }}</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                                
                                                @if($order->items->count() > 3)
                                                    <div class="col-md-4 mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                                 style="width: 50px; height: 50px;">
                                                                <i class="fas fa-plus text-muted"></i>
                                                            </div>
                                                            <div>
                                                                <strong>+{{ $order->items->count() - 3 }} more</strong>
                                                                <br><small class="text-muted">items</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="text-end">
                                                <p class="mb-1">
                                                    <strong>Order Date:</strong><br>
                                                    {{ $order->created_at->format('M d, Y h:i A') }}
                                                </p>
                                                
                                                @if($order->tracking_number)
                                                    <p class="mb-1">
                                                        <strong>Tracking:</strong><br>
                                                        <code>{{ $order->tracking_number }}</code>
                                                    </p>
                                                @endif
                                                
                                                <div class="mt-3">
                                                    <a href="{{ route('customer.orders.show', $order) }}" 
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i> View Details
                                                    </a>
                                                    
                                                    @if($order->canBeTracked())
                                                        <a href="{{ route('customer.track') }}?tracking_number={{ $order->tracking_number }}" 
                                                           class="btn btn-outline-info btn-sm">
                                                            <i class="fas fa-map-marker-alt me-1"></i> Track
                                                        </a>
                                                    @endif
                                                    
                                                    @if($order->needsSupport())
                                                        <a href="{{ route('customer.support.create') }}?order_number={{ $order->order_number }}" 
                                                           class="btn btn-outline-warning btn-sm">
                                                            <i class="fas fa-headset me-1"></i> Support
                                                        </a>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing {{ $orders->firstItem() }} to {{ $orders->lastItem() }} of {{ $orders->total() }} orders
                    </div>
                    {{ $orders->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No orders found</h5>
                    <p class="text-muted">You haven't placed any orders yet. Start shopping to see your orders here.</p>
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-2"></i> Start Shopping
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('styles')
<style>
    .stats-card {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.success {
        border-left-color: #28a745;
    }
    
    .stats-card.warning {
        border-left-color: #ffc107;
    }
    
    .stats-card.info {
        border-left-color: #17a2b8;
    }
    
    .stats-card.danger {
        border-left-color: #dc3545;
    }
</style>
@endpush
