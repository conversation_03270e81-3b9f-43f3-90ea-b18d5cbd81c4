<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;

class CategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $categories = Category::with('parent', 'children')
                             ->withCount('products')
                             ->orderBy('sort_order')
                             ->orderBy('name')
                             ->paginate(20);

        return view('admin.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $parentCategories = Category::roots()
                                  ->active()
                                  ->with('children')
                                  ->get();

        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:categories,slug',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'hide_category_prices' => 'boolean',
            'footer_featured' => 'boolean',
            'footer_text' => 'nullable|string|max:255',
            'footer_icon' => 'nullable|string|max:100',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        $validated['is_active'] = $request->boolean('is_active');
        $validated['is_featured'] = $request->boolean('is_featured');
        $validated['hide_category_prices'] = $request->boolean('hide_category_prices');
        $validated['footer_featured'] = $request->boolean('footer_featured');
        $validated['footer_text'] = $request->input('footer_text');
        $validated['footer_icon'] = $request->input('footer_icon');

        // Handle image upload with processing
        if ($request->hasFile('image')) {
            try {
                $validated['image'] = \App\Helpers\ImageHelper::processCategory($request->file('image'));
            } catch (\Exception $e) {
                \Log::error('Category image processing failed', ['error' => $e->getMessage()]);
                // Fallback to original storage
                $validated['image'] = $request->file('image')->store('uploads/categories', 'public');
            }
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Category::generateSlug($validated['name']);
        }

        $category = Category::create($validated);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category): View
    {
        $category->load(['parent', 'children.children', 'products' => function($query) {
            $query->active()->limit(10);
        }]);

        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category): View
    {
        $parentCategories = Category::roots()
                                  ->active()
                                  ->where('id', '!=', $category->id)
                                  ->with('children')
                                  ->get();

        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:categories,slug,' . $category->id,
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'hide_category_prices' => 'boolean',
            'footer_featured' => 'boolean',
            'footer_text' => 'nullable|string|max:255',
            'footer_icon' => 'nullable|string|max:100',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
        ]);

        $validated['is_active'] = $request->boolean('is_active');
        $validated['is_featured'] = $request->boolean('is_featured');
        $validated['hide_category_prices'] = $request->boolean('hide_category_prices');
        $validated['footer_featured'] = $request->boolean('footer_featured');
        $validated['footer_text'] = $request->input('footer_text');
        $validated['footer_icon'] = $request->input('footer_icon');

        // Handle image upload with processing
        if ($request->hasFile('image')) {
            // Delete old image
            if ($category->image) {
                \App\Helpers\ImageHelper::deleteImage($category->image);
            }

            try {
                $validated['image'] = \App\Helpers\ImageHelper::processCategory($request->file('image'));
            } catch (\Exception $e) {
                \Log::error('Category image processing failed during update', ['error' => $e->getMessage()]);
                // Fallback to original storage
                $validated['image'] = $request->file('image')->store('uploads/categories', 'public');
            }
        }

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Category::generateSlug($validated['name'], $category->id);
        }

        $category->update($validated);

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category): RedirectResponse
    {
        // Check if category has products
        if ($category->products()->count() > 0) {
            return redirect()->route('admin.categories.index')
                           ->with('error', 'Cannot delete category with products. Please move or delete products first.');
        }

        // Check if category has children
        if ($category->children()->count() > 0) {
            return redirect()->route('admin.categories.index')
                           ->with('error', 'Cannot delete category with subcategories. Please delete subcategories first.');
        }

        // Delete image
        if ($category->image) {
            Storage::disk('public')->delete($category->image);
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
                        ->with('success', 'Category deleted successfully.');
    }

    /**
     * Toggle category status
     */
    public function toggleStatus(Category $category): RedirectResponse
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Category {$status} successfully.");
    }

    /**
     * Update category order
     */
    public function updateOrder(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'orders' => 'required|array',
            'orders.*' => 'integer|exists:categories,id',
        ]);

        foreach ($validated['orders'] as $order => $id) {
            Category::where('id', $id)->update(['sort_order' => $order + 1]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Get category tree for API
     */
    public function tree(): \Illuminate\Http\JsonResponse
    {
        $categories = Category::roots()
                            ->active()
                            ->with('descendants')
                            ->get();

        return response()->json($categories);
    }
}
