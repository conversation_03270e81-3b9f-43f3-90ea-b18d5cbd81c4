# 🔄 Development Workflow - Atrix Logistics

## 🎯 Overview
This document defines the complete development workflow for the Atrix Logistics project, ensuring consistent, high-quality development practices across all team members.

## 🌳 Git Branching Strategy

### GitHub Flow (Simplified Branching)
We use **GitHub Flow** for its simplicity and effectiveness in continuous deployment environments.

```
main (production-ready)
├── feature/tracking-system
├── feature/quote-modal
├── feature/cms-management
├── bugfix/header-responsive
└── hotfix/security-patch
```

### Branch Types & Naming Conventions

#### 1. **Main Branch**
```bash
main  # Production-ready code only
```
- **Purpose**: Always deployable, stable code
- **Protection**: Requires PR approval, status checks pass
- **Deployment**: Auto-deploys to production

#### 2. **Feature Branches**
```bash
feature/tracking-system
feature/quote-modal-implementation
feature/cms-team-management
```
- **Naming**: `feature/brief-description`
- **Purpose**: New features and enhancements
- **Lifetime**: Short-lived (1-5 days)
- **Source**: Created from `main`
- **Merge**: Via Pull Request to `main`

#### 3. **Bug Fix Branches**
```bash
bugfix/header-responsive-issue
bugfix/tracking-form-validation
```
- **Naming**: `bugfix/brief-description`
- **Purpose**: Non-critical bug fixes
- **Source**: Created from `main`
- **Merge**: Via Pull Request to `main`

#### 4. **Hotfix Branches**
```bash
hotfix/security-vulnerability
hotfix/critical-tracking-bug
```
- **Naming**: `hotfix/brief-description`
- **Purpose**: Critical production fixes
- **Source**: Created from `main`
- **Merge**: Direct to `main` (expedited review)

### Branch Creation Workflow

```bash
# 1. Start from main
git checkout main
git pull origin main

# 2. Create feature branch
git checkout -b feature/quote-modal-implementation

# 3. Work on feature
git add .
git commit -m "feat(quotes): add quote modal component"

# 4. Push branch
git push -u origin feature/quote-modal-implementation

# 5. Create Pull Request
# (Done via GitHub interface)

# 6. After merge, cleanup
git checkout main
git pull origin main
git branch -d feature/quote-modal-implementation
```

## 📝 Commit Message Standards

### Conventional Commits Format
```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### Commit Types
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

### Examples
```bash
feat(tracking): add real-time parcel status updates
fix(auth): resolve login redirect issue
docs(api): update tracking endpoint documentation
style(components): format quote modal component
refactor(services): extract parcel service logic
test(tracking): add unit tests for tracking service
chore(deps): update Laravel to 12.17.0
```

### Scope Guidelines
- **auth**: Authentication and authorization
- **tracking**: Parcel tracking functionality
- **quotes**: Quote and inquiry system
- **products**: Product catalog and e-commerce
- **admin**: Admin panel and CMS
- **api**: API endpoints and responses
- **ui**: User interface components
- **db**: Database migrations and models

## 🔍 Code Review Process

### Pull Request Requirements

#### PR Title Format
```
[TYPE] Brief description of changes

Examples:
[FEATURE] Implement quote modal system
[BUGFIX] Fix responsive header navigation
[HOTFIX] Resolve security vulnerability in auth
```

#### PR Description Template
```markdown
## 📋 Description
Brief description of what this PR does.

## 🎯 Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## 🧪 Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Browser testing completed (Chrome, Firefox, Safari, Edge)

## 📱 Screenshots/Videos
(If applicable, add screenshots or videos demonstrating the changes)

## ✅ Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is commented where necessary
- [ ] Documentation updated
- [ ] No new warnings or errors
- [ ] Tests added/updated for changes
- [ ] All tests pass
- [ ] Security considerations addressed

## 🔗 Related Issues
Closes #123
Related to #456
```

### Review Process Steps

#### 1. **Automated Checks**
```yaml
# GitHub Actions checks (must pass)
- Code style (PHP CS Fixer)
- Static analysis (PHPStan)
- Unit tests (PHPUnit)
- Security scan (Psalm)
- Dependency vulnerabilities
```

#### 2. **Manual Review Requirements**
- **Minimum 1 approval** for regular features
- **Minimum 2 approvals** for critical changes
- **Architecture review** for major changes
- **Security review** for auth/security changes

#### 3. **Review Checklist**
```markdown
## Code Quality
- [ ] Code is readable and well-structured
- [ ] Follows Laravel conventions
- [ ] No code duplication
- [ ] Proper error handling
- [ ] Security best practices followed

## Functionality
- [ ] Feature works as described
- [ ] Edge cases handled
- [ ] Performance considerations addressed
- [ ] Mobile responsiveness maintained

## Testing
- [ ] Adequate test coverage
- [ ] Tests are meaningful
- [ ] No failing tests
- [ ] Manual testing completed

## Documentation
- [ ] Code is self-documenting
- [ ] Complex logic explained
- [ ] API documentation updated
- [ ] User documentation updated
```

### Review Response Guidelines

#### For Authors
- **Respond promptly** to review comments
- **Address all feedback** before requesting re-review
- **Ask questions** if feedback is unclear
- **Update tests** when code changes
- **Rebase** if conflicts arise

#### For Reviewers
- **Review within 24 hours** of assignment
- **Be constructive** and specific in feedback
- **Explain reasoning** behind suggestions
- **Approve when satisfied** with changes
- **Request changes** if issues found

## 🚀 Merge & Deployment Procedures

### Merge Requirements
```yaml
Branch Protection Rules:
- Require pull request reviews (1 minimum)
- Require status checks to pass
- Require branches to be up to date
- Restrict pushes to main branch
- Require linear history
```

### Merge Process

#### 1. **Pre-Merge Checklist**
- [ ] All CI checks pass
- [ ] Required approvals received
- [ ] Branch is up-to-date with main
- [ ] No merge conflicts
- [ ] Documentation updated

#### 2. **Merge Strategy**
```bash
# Use "Squash and merge" for feature branches
# This creates a clean, linear history
```

#### 3. **Post-Merge Actions**
- [ ] Delete feature branch
- [ ] Update local main branch
- [ ] Verify deployment success
- [ ] Monitor for issues

### Deployment Pipeline

#### Staging Deployment
```yaml
# Automatic on merge to main
stages:
  - build
  - test
  - deploy-staging
  - smoke-tests
```

#### Production Deployment
```yaml
# Manual trigger after staging validation
stages:
  - deploy-production
  - health-checks
  - rollback-ready
```

### Rollback Procedures

#### Quick Rollback
```bash
# Revert last commit
git revert HEAD
git push origin main

# Deploy previous version
./deploy.sh --version=previous
```

#### Emergency Rollback
```bash
# Reset to last known good commit
git reset --hard <last-good-commit>
git push --force-with-lease origin main

# Immediate deployment
./deploy.sh --emergency
```

## 🔧 Development Environment

### Local Setup Standards
```bash
# Required tools
- PHP 8.1+
- Composer 2.0+
- Node.js 18+
- MySQL 8.0+
- Git 2.30+

# IDE Configuration
- EditorConfig support
- PHP CS Fixer integration
- PHPStan analysis
- Xdebug for debugging
```

### Pre-commit Hooks
```bash
#!/bin/sh
# .git/hooks/pre-commit

# Run PHP CS Fixer
./vendor/bin/php-cs-fixer fix --dry-run --diff

# Run PHPStan
./vendor/bin/phpstan analyse

# Run tests
php artisan test

# Check for debugging statements
if grep -r "dd\|dump\|var_dump" app/; then
    echo "Remove debugging statements before committing"
    exit 1
fi
```

### Code Formatting
```bash
# PHP CS Fixer configuration
./vendor/bin/php-cs-fixer fix

# Prettier for frontend assets
npx prettier --write resources/js/**/*.js
npx prettier --write resources/css/**/*.css
```

## 📊 Workflow Metrics

### Key Performance Indicators
- **Lead Time**: Time from feature start to production
- **Cycle Time**: Time from PR creation to merge
- **Deployment Frequency**: How often we deploy
- **Mean Time to Recovery**: Time to fix production issues

### Target Metrics
- **Lead Time**: < 5 days
- **Cycle Time**: < 24 hours
- **Deployment Frequency**: Daily
- **MTTR**: < 2 hours

### Monitoring Tools
- GitHub Insights for PR metrics
- Deployment pipeline metrics
- Production monitoring alerts
- Code quality trends

## 🆘 Troubleshooting

### Common Issues

#### Merge Conflicts
```bash
# Update branch with latest main
git checkout main
git pull origin main
git checkout feature/your-branch
git rebase main

# Resolve conflicts manually
git add .
git rebase --continue
git push --force-with-lease origin feature/your-branch
```

#### Failed CI Checks
```bash
# Run checks locally
composer install
php artisan test
./vendor/bin/php-cs-fixer fix
./vendor/bin/phpstan analyse

# Fix issues and push
git add .
git commit -m "fix: resolve CI check failures"
git push origin feature/your-branch
```

#### Deployment Issues
```bash
# Check deployment logs
kubectl logs -f deployment/atrix-logistics

# Rollback if necessary
./deploy.sh --rollback

# Monitor health
curl -f https://api.atrixlogistics.com/health
```

This development workflow ensures consistent, high-quality code delivery while maintaining team productivity and code stability.
