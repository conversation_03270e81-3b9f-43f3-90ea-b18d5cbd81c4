<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Career;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;

class CareerController extends Controller
{
    /**
     * Display a listing of careers
     */
    public function index(): View
    {
        $careers = Career::with('jobApplications')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.careers.index', compact('careers'));
    }

    /**
     * Show the form for creating a new career
     */
    public function create(): View
    {
        return view('admin.careers.create');
    }

    /**
     * Store a newly created career
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'nullable|string',
            'responsibilities' => 'nullable|string',
            'benefits' => 'nullable|string',
            'department' => 'nullable|string|max:255',
            'location' => 'required|string|max:255',
            'employment_type' => 'required|in:full-time,part-time,contract,internship,remote',
            'experience_level' => 'required|in:entry,mid,senior,executive',
            'salary_min' => 'nullable|numeric|min:0',
            'salary_max' => 'nullable|numeric|min:0|gte:salary_min',
            'salary_currency' => 'nullable|string|max:3',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'application_deadline' => 'nullable|date|after:today',
            'required_skills' => 'nullable|array',
            'preferred_skills' => 'nullable|array',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
        ]);

        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');
        $validated['slug'] = Str::slug($validated['title']);

        $career = Career::create($validated);

        return redirect()->route('admin.cms.careers.show', $career)
                        ->with('success', 'Career position created successfully.');
    }

    /**
     * Display the specified career
     */
    public function show(Career $career): View
    {
        $career->load(['jobApplications' => function($query) {
            $query->orderBy('created_at', 'desc');
        }]);

        return view('admin.careers.show', compact('career'));
    }

    /**
     * Show the form for editing the specified career
     */
    public function edit(Career $career): View
    {
        return view('admin.careers.edit', compact('career'));
    }

    /**
     * Update the specified career
     */
    public function update(Request $request, Career $career): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'requirements' => 'nullable|string',
            'responsibilities' => 'nullable|string',
            'benefits' => 'nullable|string',
            'department' => 'nullable|string|max:255',
            'location' => 'required|string|max:255',
            'employment_type' => 'required|in:full-time,part-time,contract,internship,remote',
            'experience_level' => 'required|in:entry,mid,senior,executive',
            'salary_min' => 'nullable|numeric|min:0',
            'salary_max' => 'nullable|numeric|min:0|gte:salary_min',
            'salary_currency' => 'nullable|string|max:3',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'application_deadline' => 'nullable|date',
            'required_skills' => 'nullable|array',
            'preferred_skills' => 'nullable|array',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
        ]);

        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        if ($career->title !== $validated['title']) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $career->update($validated);

        return redirect()->route('admin.cms.careers.show', $career)
                        ->with('success', 'Career position updated successfully.');
    }

    /**
     * Remove the specified career
     */
    public function destroy(Career $career): RedirectResponse
    {
        $career->delete();

        return redirect()->route('admin.cms.careers.index')
                        ->with('success', 'Career position deleted successfully.');
    }

    /**
     * Toggle career active status
     */
    public function toggleStatus(Career $career): RedirectResponse
    {
        $career->update(['is_active' => !$career->is_active]);

        $status = $career->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Career position {$status} successfully.");
    }
}
