<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'parent_id',
        'image',
        'icon',
        'sort_order',
        'is_active',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'is_featured',
        'hide_category_prices',
        'footer_featured',
        'footer_text',
        'footer_icon',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'hide_category_prices' => 'boolean',
        'footer_featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the parent category
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * Get the child categories
     */
    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get all descendants recursively
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get products in this category
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get all products including from child categories
     */
    public function allProducts()
    {
        $categoryIds = $this->getAllCategoryIds();
        return Product::whereIn('category_id', $categoryIds);
    }

    /**
     * Get all category IDs including children
     */
    public function getAllCategoryIds(): array
    {
        $ids = [$this->id];

        foreach ($this->children as $child) {
            $ids = array_merge($ids, $child->getAllCategoryIds());
        }

        return $ids;
    }

    /**
     * Get the category path (breadcrumb)
     */
    public function getPathAttribute(): array
    {
        $path = [];
        $category = $this;

        while ($category) {
            array_unshift($path, $category);
            $category = $category->parent;
        }

        return $path;
    }

    /**
     * Get the full category name with parent names
     */
    public function getFullNameAttribute(): string
    {
        $names = [];
        foreach ($this->path as $category) {
            $names[] = $category->name;
        }

        return implode(' > ', $names);
    }

    /**
     * Check if category has children
     */
    public function hasChildren(): bool
    {
        return $this->children()->count() > 0;
    }

    /**
     * Check if category is root (no parent)
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Get root categories
     */
    public static function roots()
    {
        return static::whereNull('parent_id')->orderBy('sort_order');
    }

    /**
     * Get active categories
     */
    public static function active()
    {
        return static::where('is_active', true);
    }

    /**
     * Get featured categories
     */
    public static function featured()
    {
        return static::where('is_featured', true)->where('is_active', true);
    }

    /**
     * Get footer featured categories
     */
    public static function footerFeatured()
    {
        return static::where('footer_featured', true)->where('is_active', true);
    }

    /**
     * Generate slug from name
     */
    public static function generateSlug(string $name, ?int $excludeId = null): string
    {
        $slug = \Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::where('slug', $slug)
                    ->when($excludeId, fn($query) => $query->where('id', '!=', $excludeId))
                    ->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = static::generateSlug($category->name);
            }

            if (is_null($category->sort_order)) {
                $category->sort_order = static::where('parent_id', $category->parent_id)->max('sort_order') + 1;
            }
        });

        static::updating(function ($category) {
            if ($category->isDirty('name') && empty($category->slug)) {
                $category->slug = static::generateSlug($category->name, $category->id);
            }
        });
    }





    /**
     * Scope a query to only include active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the category tree as a flat array
     */
    public static function getTree()
    {
        return static::with('children.children.children')
                    ->whereNull('parent_id')
                    ->orderBy('sort_order')
                    ->orderBy('name')
                    ->get();
    }
}
