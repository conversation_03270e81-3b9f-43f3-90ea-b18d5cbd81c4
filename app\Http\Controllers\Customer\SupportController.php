<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\Order;
use App\Models\Parcel;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class SupportController extends Controller
{
    /**
     * Display customer's support tickets
     */
    public function index(Request $request): View
    {
        $user = Auth::user();

        $query = $user->supportTickets()->with(['assignedTo']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('ticket_number', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $tickets = $query->latest()->paginate(15);

        // Get filter options
        $statuses = [
            'open' => 'Open',
            'in_progress' => 'In Progress',
            'waiting_customer' => 'Waiting for Customer',
            'waiting_admin' => 'Waiting for Admin',
            'resolved' => 'Resolved',
            'closed' => 'Closed',
        ];

        $priorities = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];

        $categories = [
            'general' => 'General',
            'technical' => 'Technical',
            'billing' => 'Billing',
            'shipping' => 'Shipping',
            'product' => 'Product',
            'account' => 'Account',
            'complaint' => 'Complaint',
            'suggestion' => 'Suggestion',
        ];

        return view('customer.support.index', compact('tickets', 'statuses', 'priorities', 'categories'));
    }

    /**
     * Show the form for creating a new support ticket
     */
    public function create(): View
    {
        $user = Auth::user();

        // Get user's recent orders and parcels for reference
        $recentOrders = $user->orders()->latest()->limit(10)->get(['id', 'order_number', 'created_at']);
        $recentParcels = $user->parcels()->latest()->limit(10)->get(['id', 'tracking_number', 'created_at']);

        $categories = [
            'general' => 'General Inquiry',
            'technical' => 'Technical Issue',
            'billing' => 'Billing Question',
            'shipping' => 'Shipping Issue',
            'product' => 'Product Question',
            'account' => 'Account Issue',
            'complaint' => 'Complaint',
            'suggestion' => 'Suggestion',
        ];

        $priorities = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];

        return view('customer.support.create', compact('categories', 'priorities', 'recentOrders', 'recentParcels'));
    }

    /**
     * Store a newly created support ticket
     */
    public function store(Request $request): RedirectResponse
    {
        $user = Auth::user();

        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'description' => 'required|string|max:5000',
            'category' => 'required|in:general,technical,billing,shipping,product,account,complaint,suggestion',
            'priority' => 'required|in:low,medium,high,urgent',
            'order_number' => 'nullable|string|max:255',
            'parcel_tracking' => 'nullable|string|max:255',
        ]);

        $ticket = SupportTicket::create(array_merge($validated, [
            'user_id' => $user->id,
            'customer_name' => $user->name,
            'customer_email' => $user->email,
            'customer_phone' => $user->phone,
            'status' => 'open',
        ]));

        return redirect()->route('customer.support.show', $ticket)
                        ->with('success', 'Support ticket created successfully. Ticket #' . $ticket->ticket_number);
    }

    /**
     * Display the specified support ticket
     */
    public function show(SupportTicket $ticket): View
    {
        // Ensure the ticket belongs to the authenticated customer
        if ($ticket->user_id !== Auth::id()) {
            abort(404);
        }

        $ticket->load(['assignedTo']);

        return view('customer.support.show', compact('ticket'));
    }

    /**
     * Show the form for editing the specified support ticket
     */
    public function edit(SupportTicket $ticket): View|RedirectResponse
    {
        // Ensure the ticket belongs to the authenticated customer
        if ($ticket->user_id !== Auth::id()) {
            abort(404);
        }

        // Only allow editing if ticket is open or waiting for customer
        if (!in_array($ticket->status, ['open', 'waiting_customer'])) {
            return redirect()->route('customer.support.show', $ticket)
                           ->with('error', 'This ticket cannot be edited in its current status.');
        }

        $categories = [
            'general' => 'General Inquiry',
            'technical' => 'Technical Issue',
            'billing' => 'Billing Question',
            'shipping' => 'Shipping Issue',
            'product' => 'Product Question',
            'account' => 'Account Issue',
            'complaint' => 'Complaint',
            'suggestion' => 'Suggestion',
        ];

        $priorities = [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
        ];

        return view('customer.support.edit', compact('ticket', 'categories', 'priorities'));
    }

    /**
     * Update the specified support ticket
     */
    public function update(Request $request, SupportTicket $ticket): RedirectResponse
    {
        // Ensure the ticket belongs to the authenticated customer
        if ($ticket->user_id !== Auth::id()) {
            abort(404);
        }

        // Only allow updating if ticket is open or waiting for customer
        if (!in_array($ticket->status, ['open', 'waiting_customer'])) {
            return redirect()->route('customer.support.show', $ticket)
                           ->with('error', 'This ticket cannot be updated in its current status.');
        }

        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'description' => 'required|string|max:5000',
            'category' => 'required|in:general,technical,billing,shipping,product,account,complaint,suggestion',
            'priority' => 'required|in:low,medium,high,urgent',
            'order_number' => 'nullable|string|max:255',
            'parcel_tracking' => 'nullable|string|max:255',
        ]);

        $ticket->update($validated);

        return redirect()->route('customer.support.show', $ticket)
                        ->with('success', 'Support ticket updated successfully.');
    }

    /**
     * Close the specified support ticket
     */
    public function close(SupportTicket $ticket): RedirectResponse
    {
        // Ensure the ticket belongs to the authenticated customer
        if ($ticket->user_id !== Auth::id()) {
            abort(404);
        }

        // Only allow closing if ticket is resolved
        if ($ticket->status !== 'resolved') {
            return redirect()->route('customer.support.show', $ticket)
                           ->with('error', 'Only resolved tickets can be closed.');
        }

        $ticket->update([
            'status' => 'closed',
            'closed_at' => now(),
        ]);

        return redirect()->route('customer.support.show', $ticket)
                        ->with('success', 'Support ticket closed successfully.');
    }

    /**
     * Reopen the specified support ticket
     */
    public function reopen(SupportTicket $ticket): RedirectResponse
    {
        // Ensure the ticket belongs to the authenticated customer
        if ($ticket->user_id !== Auth::id()) {
            abort(404);
        }

        // Only allow reopening if ticket is closed
        if ($ticket->status !== 'closed') {
            return redirect()->route('customer.support.show', $ticket)
                           ->with('error', 'Only closed tickets can be reopened.');
        }

        $ticket->update([
            'status' => 'open',
            'closed_at' => null,
        ]);

        return redirect()->route('customer.support.show', $ticket)
                        ->with('success', 'Support ticket reopened successfully.');
    }
}
