<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('site_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key_name')->unique();
            $table->longText('value')->nullable();
            $table->enum('type', ['string', 'text', 'integer', 'boolean', 'json', 'image'])->default('string');
            $table->string('group_name', 100)->default('general');
            $table->string('label')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Whether setting can be accessed publicly
            $table->timestamps();

            // Indexes
            $table->index('key_name');
            $table->index('group_name');
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('site_settings');
    }
};
