<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\Carrier;
use App\Models\TrackingEvent;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;

class ParcelAnalyticsController extends Controller
{
    /**
     * Display parcel analytics dashboard
     */
    public function index(Request $request): View
    {
        try {
            $period = $request->get('period', '30'); // days
            $startDate = Carbon::now()->subDays($period);

            // Overview Statistics
            $overviewStats = $this->getOverviewStats($startDate);

            // Performance Metrics
            $performanceMetrics = $this->getPerformanceMetrics($startDate);

            // Revenue Analytics
            $revenueAnalytics = $this->getRevenueAnalytics($startDate);

            // Carrier Performance
            $carrierPerformance = $this->getCarrierPerformance($startDate);

            // Geographic Distribution
            $geographicData = $this->getGeographicDistribution($startDate);

            // Service Type Analytics
            $serviceTypeAnalytics = $this->getServiceTypeAnalytics($startDate);

            return view('admin.analytics.parcels', compact(
                'overviewStats',
                'performanceMetrics',
                'revenueAnalytics',
                'carrierPerformance',
                'geographicData',
                'serviceTypeAnalytics',
                'period'
            ));
        } catch (\Exception $e) {
            // Log the error and return with default data
            \Log::error('Parcel Analytics Error: ' . $e->getMessage());

            // Return view with empty/default data
            return view('admin.analytics.parcels', [
                'overviewStats' => $this->getDefaultOverviewStats(),
                'performanceMetrics' => $this->getDefaultPerformanceMetrics(),
                'revenueAnalytics' => $this->getDefaultRevenueAnalytics(),
                'carrierPerformance' => [],
                'geographicData' => $this->getDefaultGeographicData(),
                'serviceTypeAnalytics' => [],
                'period' => $request->get('period', '30')
            ])->with('error', 'Some analytics data could not be loaded. Please try again later.');
        }
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(Carbon $startDate): array
    {
        $totalParcels = Parcel::where('created_at', '>=', $startDate)->count();
        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays(now()));
        $previousParcels = Parcel::whereBetween('created_at', [$previousPeriodStart, $startDate])->count();
        
        $deliveredParcels = Parcel::where('created_at', '>=', $startDate)
                                 ->where('status', 'delivered')
                                 ->count();
        
        $inTransitParcels = Parcel::where('created_at', '>=', $startDate)
                                 ->whereIn('status', ['picked_up', 'in_transit', 'out_for_delivery'])
                                 ->count();
        
        $exceptionParcels = Parcel::where('created_at', '>=', $startDate)
                                 ->where('status', 'exception')
                                 ->count();

        $totalRevenue = Parcel::where('created_at', '>=', $startDate)
                             ->where('is_paid', true)
                             ->sum('total_cost');

        $previousRevenue = Parcel::whereBetween('created_at', [$previousPeriodStart, $startDate])
                                ->where('is_paid', true)
                                ->sum('total_cost');

        return [
            'total_parcels' => $totalParcels,
            'parcels_growth' => $previousParcels > 0 ? (($totalParcels - $previousParcels) / $previousParcels) * 100 : 0,
            'delivered_parcels' => $deliveredParcels,
            'delivery_rate' => $totalParcels > 0 ? ($deliveredParcels / $totalParcels) * 100 : 0,
            'in_transit_parcels' => $inTransitParcels,
            'exception_parcels' => $exceptionParcels,
            'exception_rate' => $totalParcels > 0 ? ($exceptionParcels / $totalParcels) * 100 : 0,
            'total_revenue' => $totalRevenue,
            'revenue_growth' => $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0,
            'average_parcel_value' => $totalParcels > 0 ? $totalRevenue / $totalParcels : 0,
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(Carbon $startDate): array
    {
        // Average delivery time
        $deliveredParcels = Parcel::where('created_at', '>=', $startDate)
                                 ->where('status', 'delivered')
                                 ->whereNotNull('delivered_at')
                                 ->get();

        $totalDeliveryTime = 0;
        $deliveryCount = 0;
        $onTimeDeliveries = 0;

        foreach ($deliveredParcels as $parcel) {
            if ($parcel->shipped_at && $parcel->delivered_at) {
                $deliveryTime = $parcel->shipped_at->diffInHours($parcel->delivered_at);
                $totalDeliveryTime += $deliveryTime;
                $deliveryCount++;

                // Check if delivered on time (within estimated delivery date)
                if ($parcel->estimated_delivery_date && 
                    $parcel->delivered_at->lte($parcel->estimated_delivery_date->endOfDay())) {
                    $onTimeDeliveries++;
                }
            }
        }

        $avgDeliveryTime = $deliveryCount > 0 ? $totalDeliveryTime / $deliveryCount : 0;
        $onTimeRate = $deliveryCount > 0 ? ($onTimeDeliveries / $deliveryCount) * 100 : 0;

        // Processing time (from creation to pickup)
        $processedParcels = Parcel::where('created_at', '>=', $startDate)
                                 ->whereNotNull('shipped_at')
                                 ->get();

        $totalProcessingTime = 0;
        $processingCount = 0;

        foreach ($processedParcels as $parcel) {
            $processingTime = $parcel->created_at->diffInHours($parcel->shipped_at);
            $totalProcessingTime += $processingTime;
            $processingCount++;
        }

        $avgProcessingTime = $processingCount > 0 ? $totalProcessingTime / $processingCount : 0;

        return [
            'avg_delivery_time_hours' => round($avgDeliveryTime, 1),
            'avg_delivery_time_days' => round($avgDeliveryTime / 24, 1),
            'on_time_delivery_rate' => round($onTimeRate, 1),
            'avg_processing_time_hours' => round($avgProcessingTime, 1),
            'avg_processing_time_days' => round($avgProcessingTime / 24, 1),
            'total_delivered' => $deliveryCount,
            'total_processed' => $processingCount,
        ];
    }

    /**
     * Get revenue analytics
     */
    private function getRevenueAnalytics(Carbon $startDate): array
    {
        // Daily revenue for chart
        $dailyRevenue = Parcel::selectRaw('DATE(created_at) as date, SUM(total_cost) as revenue, COUNT(*) as parcels')
                             ->where('created_at', '>=', $startDate)
                             ->where('is_paid', true)
                             ->groupBy('date')
                             ->orderBy('date')
                             ->get();

        // Revenue by service type
        $revenueByService = Parcel::selectRaw('service_type, SUM(total_cost) as revenue, COUNT(*) as parcels')
                                 ->where('created_at', '>=', $startDate)
                                 ->where('is_paid', true)
                                 ->whereNotNull('service_type')
                                 ->groupBy('service_type')
                                 ->orderBy('revenue', 'desc')
                                 ->get();

        // Payment status distribution
        $paymentStatus = Parcel::selectRaw('is_paid, COUNT(*) as count, SUM(total_cost) as total')
                              ->where('created_at', '>=', $startDate)
                              ->groupBy('is_paid')
                              ->get();

        return [
            'daily_revenue' => $dailyRevenue,
            'revenue_by_service' => $revenueByService,
            'payment_status' => $paymentStatus,
            'total_revenue' => $dailyRevenue->sum('revenue'),
            'total_parcels' => $dailyRevenue->sum('parcels'),
            'avg_daily_revenue' => $dailyRevenue->count() > 0 ? $dailyRevenue->avg('revenue') : 0,
        ];
    }

    /**
     * Get carrier performance analytics
     */
    private function getCarrierPerformance(Carbon $startDate): array
    {
        $carrierStats = Carrier::withCount(['parcels as total_parcels' => function ($query) use ($startDate) {
                                    $query->where('created_at', '>=', $startDate);
                                }])
                               ->withCount(['parcels as delivered_parcels' => function ($query) use ($startDate) {
                                    $query->where('created_at', '>=', $startDate)
                                          ->where('status', 'delivered');
                                }])
                               ->withCount(['parcels as exception_parcels' => function ($query) use ($startDate) {
                                    $query->where('created_at', '>=', $startDate)
                                          ->where('status', 'exception');
                                }])
                               ->withSum(['parcels as total_revenue' => function ($query) use ($startDate) {
                                    $query->where('created_at', '>=', $startDate)
                                          ->where('is_paid', true);
                                }], 'total_cost')
                               ->where('is_active', true)
                               ->get()
                               ->map(function ($carrier) {
                                   $carrier->delivery_rate = $carrier->total_parcels > 0 ?
                                       ($carrier->delivered_parcels / $carrier->total_parcels) * 100 : 0;
                                   $carrier->exception_rate = $carrier->total_parcels > 0 ?
                                       ($carrier->exception_parcels / $carrier->total_parcels) * 100 : 0;
                                   $carrier->avg_parcel_value = $carrier->total_parcels > 0 ?
                                       ($carrier->total_revenue ?? 0) / $carrier->total_parcels : 0;

                                   // Ensure logo field exists for compatibility
                                   if (!isset($carrier->logo)) {
                                       $carrier->logo = null;
                                   }

                                   return $carrier;
                               });

        return $carrierStats->toArray();
    }

    /**
     * Get geographic distribution
     */
    private function getGeographicDistribution(Carbon $startDate): array
    {
        // Top sender cities
        $topSenderCities = Parcel::selectRaw('sender_city, sender_state, COUNT(*) as count')
                                ->where('created_at', '>=', $startDate)
                                ->groupBy('sender_city', 'sender_state')
                                ->orderBy('count', 'desc')
                                ->limit(10)
                                ->get();

        // Top recipient cities
        $topRecipientCities = Parcel::selectRaw('recipient_city, recipient_state, COUNT(*) as count')
                                   ->where('created_at', '>=', $startDate)
                                   ->groupBy('recipient_city', 'recipient_state')
                                   ->orderBy('count', 'desc')
                                   ->limit(10)
                                   ->get();

        // State distribution
        $stateDistribution = Parcel::selectRaw('recipient_state, COUNT(*) as count')
                                  ->where('created_at', '>=', $startDate)
                                  ->groupBy('recipient_state')
                                  ->orderBy('count', 'desc')
                                  ->get();

        return [
            'top_sender_cities' => $topSenderCities,
            'top_recipient_cities' => $topRecipientCities,
            'state_distribution' => $stateDistribution,
        ];
    }

    /**
     * Get service type analytics
     */
    private function getServiceTypeAnalytics(Carbon $startDate): array
    {
        $serviceStats = Parcel::selectRaw('
                                service_type,
                                COUNT(*) as total_parcels,
                                SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered_parcels,
                                SUM(CASE WHEN status = "exception" THEN 1 ELSE 0 END) as exception_parcels,
                                AVG(total_cost) as avg_cost,
                                SUM(CASE WHEN is_paid = 1 THEN total_cost ELSE 0 END) as total_revenue
                            ')
                            ->where('created_at', '>=', $startDate)
                            ->whereNotNull('service_type')
                            ->groupBy('service_type')
                            ->get()
                            ->map(function ($service) {
                                $service->delivery_rate = $service->total_parcels > 0 ? 
                                    ($service->delivered_parcels / $service->total_parcels) * 100 : 0;
                                $service->exception_rate = $service->total_parcels > 0 ? 
                                    ($service->exception_parcels / $service->total_parcels) * 100 : 0;
                                return $service;
                            });

        return $serviceStats->toArray();
    }

    /**
     * Get chart data for parcels over time
     */
    public function getParcelTrendsData(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30');
            $startDate = Carbon::now()->subDays($period);

            $data = Parcel::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                         ->where('created_at', '>=', $startDate)
                         ->groupBy('date')
                         ->orderBy('date')
                         ->get();

            return response()->json($data);
        } catch (\Exception $e) {
            \Log::error('Parcel Trends Data Error: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Get status distribution data
     */
    public function getStatusDistributionData(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30');
            $startDate = Carbon::now()->subDays($period);

            $data = Parcel::selectRaw('status, COUNT(*) as count')
                         ->where('created_at', '>=', $startDate)
                         ->groupBy('status')
                         ->get();

            return response()->json($data);
        } catch (\Exception $e) {
            \Log::error('Status Distribution Data Error: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Get revenue trends data
     */
    public function getRevenueTrendsData(Request $request): JsonResponse
    {
        try {
            $period = $request->get('period', '30');
            $startDate = Carbon::now()->subDays($period);

            $data = Parcel::selectRaw('DATE(created_at) as date, SUM(total_cost) as revenue')
                         ->where('created_at', '>=', $startDate)
                         ->where('is_paid', true)
                         ->groupBy('date')
                         ->orderBy('date')
                         ->get();

            return response()->json($data);
        } catch (\Exception $e) {
            \Log::error('Revenue Trends Data Error: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * Default overview stats for error handling
     */
    private function getDefaultOverviewStats(): array
    {
        return [
            'total_parcels' => 0,
            'parcels_growth' => 0,
            'delivered_parcels' => 0,
            'delivery_rate' => 0,
            'in_transit_parcels' => 0,
            'exception_parcels' => 0,
            'exception_rate' => 0,
            'total_revenue' => 0,
            'revenue_growth' => 0,
            'average_parcel_value' => 0,
        ];
    }

    /**
     * Default performance metrics for error handling
     */
    private function getDefaultPerformanceMetrics(): array
    {
        return [
            'avg_delivery_time_hours' => 0,
            'avg_delivery_time_days' => 0,
            'on_time_delivery_rate' => 0,
            'avg_processing_time_hours' => 0,
            'avg_processing_time_days' => 0,
            'total_delivered' => 0,
            'total_processed' => 0,
        ];
    }

    /**
     * Default revenue analytics for error handling
     */
    private function getDefaultRevenueAnalytics(): array
    {
        return [
            'daily_revenue' => collect(),
            'revenue_by_service' => collect(),
            'payment_status' => collect(),
            'total_revenue' => 0,
            'total_parcels' => 0,
            'avg_daily_revenue' => 0,
        ];
    }

    /**
     * Default geographic data for error handling
     */
    private function getDefaultGeographicData(): array
    {
        return [
            'top_sender_cities' => collect(),
            'top_recipient_cities' => collect(),
            'state_distribution' => collect(),
        ];
    }
}
