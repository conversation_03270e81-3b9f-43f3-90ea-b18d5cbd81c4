@extends('documents.base')

@section('title', 'Waybill #' . $parcel->tracking_number)
@section('document-title', 'WAYBILL')

@section('header-right-extra')
    <div style="margin-top: 10px;">
        <span class="badge badge-{{ $parcel->status === 'delivered' ? 'success' : ($parcel->status === 'in_transit' ? 'info' : 'warning') }}">
            {{ ucfirst(str_replace('_', ' ', $parcel->status)) }}
        </span>
    </div>
@endsection

@section('content')
    <!-- Tracking Information -->
    <div class="info-box">
        <div class="info-box-title">Tracking Information</div>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Tracking Number:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $parcel->tracking_number }}</td>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Service Type:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ ucfirst($parcel->service_type) }}</td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Pickup Date:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $parcel->pickup_date ? $parcel->pickup_date->format('M j, Y') : 'TBD' }}</td>
                <td style="border: none; padding: 5px 0;"><strong>Expected Delivery:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $parcel->expected_delivery_date ? $parcel->expected_delivery_date->format('M j, Y') : 'TBD' }}</td>
            </tr>
        </table>
    </div>

    <!-- Sender and Receiver Information -->
    <div class="address-section">
        <div class="address-box">
            <div class="address-title">Sender Information:</div>
            <div class="address-content">
                <strong>{{ $parcel->sender_name }}</strong><br>
                @if($parcel->sender_company)
                    {{ $parcel->sender_company }}<br>
                @endif
                {{ $parcel->sender_address }}<br>
                {{ $parcel->sender_city }}, {{ $parcel->sender_state }} {{ $parcel->sender_postal_code }}<br>
                {{ $parcel->sender_country }}<br>
                @if($parcel->sender_phone)
                    Phone: {{ $parcel->sender_phone }}<br>
                @endif
                @if($parcel->sender_email)
                    Email: {{ $parcel->sender_email }}
                @endif
            </div>
        </div>
        <div class="address-box">
            <div class="address-title">Receiver Information:</div>
            <div class="address-content">
                <strong>{{ $parcel->receiver_name }}</strong><br>
                @if($parcel->receiver_company)
                    {{ $parcel->receiver_company }}<br>
                @endif
                {{ $parcel->receiver_address }}<br>
                {{ $parcel->receiver_city }}, {{ $parcel->receiver_state }} {{ $parcel->receiver_postal_code }}<br>
                {{ $parcel->receiver_country }}<br>
                @if($parcel->receiver_phone)
                    Phone: {{ $parcel->receiver_phone }}<br>
                @endif
                @if($parcel->receiver_email)
                    Email: {{ $parcel->receiver_email }}
                @endif
            </div>
        </div>
    </div>

    <!-- Package Details -->
    <div class="section">
        <div class="section-title">Package Details</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 30%;">Description</th>
                    <th style="width: 15%; text-align: center;">Quantity</th>
                    <th style="width: 15%; text-align: center;">Weight</th>
                    <th style="width: 20%; text-align: center;">Dimensions</th>
                    <th style="width: 20%; text-align: right;">Declared Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $parcel->description ?: 'General Package' }}</td>
                    <td class="text-center">{{ $parcel->quantity ?: 1 }}</td>
                    <td class="text-center">{{ $parcel->weight ? $parcel->weight . ' kg' : 'N/A' }}</td>
                    <td class="text-center">
                        @if($parcel->length && $parcel->width && $parcel->height)
                            {{ $parcel->length }} x {{ $parcel->width }} x {{ $parcel->height }} cm
                        @else
                            N/A
                        @endif
                    </td>
                    <td class="text-right">
                        @if($parcel->declared_value)
                            @currency($parcel->declared_value)
                        @else
                            N/A
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Service Information -->
    <div class="section">
        <div class="section-title">Service Information</div>
        <div class="info-box">
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="border: none; padding: 5px 0; width: 25%;"><strong>Service Type:</strong></td>
                    <td style="border: none; padding: 5px 0; width: 25%;">{{ ucfirst($parcel->service_type) }}</td>
                    <td style="border: none; padding: 5px 0; width: 25%;"><strong>Priority:</strong></td>
                    <td style="border: none; padding: 5px 0; width: 25%;">{{ ucfirst($parcel->priority ?? 'standard') }}</td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Insurance:</strong></td>
                    <td style="border: none; padding: 5px 0;">
                        {{ $parcel->insurance_amount ? '@currency(' . $parcel->insurance_amount . ')' : 'No' }}
                    </td>
                    <td style="border: none; padding: 5px 0;"><strong>COD Amount:</strong></td>
                    <td style="border: none; padding: 5px 0;">
                        {{ $parcel->cod_amount ? '@currency(' . $parcel->cod_amount . ')' : 'No' }}
                    </td>
                </tr>
                @if($parcel->special_instructions)
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Special Instructions:</strong></td>
                    <td style="border: none; padding: 5px 0;" colspan="3">{{ $parcel->special_instructions }}</td>
                </tr>
                @endif
            </table>
        </div>
    </div>

    <!-- Tracking History -->
    @if($parcel->trackingEvents && $parcel->trackingEvents->count() > 0)
    <div class="section">
        <div class="section-title">Tracking History</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 20%;">Date & Time</th>
                    <th style="width: 25%;">Status</th>
                    <th style="width: 30%;">Location</th>
                    <th style="width: 25%;">Description</th>
                </tr>
            </thead>
            <tbody>
                @foreach($parcel->trackingEvents->sortByDesc('created_at') as $event)
                <tr>
                    <td>{{ $event->created_at->format('M j, Y g:i A') }}</td>
                    <td>
                        <span class="badge badge-{{ $event->status === 'delivered' ? 'success' : ($event->status === 'in_transit' ? 'info' : 'warning') }}">
                            {{ ucfirst(str_replace('_', ' ', $event->status)) }}
                        </span>
                    </td>
                    <td>{{ $event->location ?: 'N/A' }}</td>
                    <td>{{ $event->description ?: 'Status updated' }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif

    <!-- Charges and Payment -->
    @if($parcel->shipping_cost || $parcel->insurance_amount || $parcel->cod_amount)
    <div class="section">
        <div class="section-title">Charges</div>
        <div class="totals-section">
            <table class="totals-table">
                @if($parcel->shipping_cost)
                <tr>
                    <td class="total-label">Shipping Cost:</td>
                    <td class="total-value">@currency($parcel->shipping_cost)</td>
                </tr>
                @endif
                @if($parcel->insurance_amount)
                <tr>
                    <td class="total-label">Insurance:</td>
                    <td class="total-value">@currency($parcel->insurance_amount)</td>
                </tr>
                @endif
                @if($parcel->additional_charges)
                <tr>
                    <td class="total-label">Additional Charges:</td>
                    <td class="total-value">@currency($parcel->additional_charges)</td>
                </tr>
                @endif
                @if($parcel->cod_amount)
                <tr>
                    <td class="total-label">COD Amount:</td>
                    <td class="total-value">@currency($parcel->cod_amount)</td>
                </tr>
                @endif
                <tr class="grand-total">
                    <td class="total-label">Total Charges:</td>
                    <td class="total-value">
                        @currency(($parcel->shipping_cost ?? 0) + ($parcel->insurance_amount ?? 0) + ($parcel->additional_charges ?? 0))
                    </td>
                </tr>
            </table>
        </div>
    </div>
    @endif

    <!-- Terms and Conditions -->
    <div class="section">
        <div class="section-title">Terms & Conditions</div>
        <div style="font-size: 10px; line-height: 1.4; color: #666;">
            <p><strong>Liability:</strong> Our liability is limited to the declared value of the package or actual value, whichever is less.</p>
            <p><strong>Delivery:</strong> Delivery times are estimates and may vary due to circumstances beyond our control.</p>
            <p><strong>Claims:</strong> Any claims for loss or damage must be reported within 7 days of delivery or expected delivery date.</p>
            <p><strong>Prohibited Items:</strong> Sender confirms that the package does not contain any prohibited or dangerous items.</p>
            <p><strong>Inspection:</strong> We reserve the right to inspect packages for security and customs purposes.</p>
            @if(isset($siteSettings['waybill_terms']))
                <p>{{ $siteSettings['waybill_terms'] }}</p>
            @endif
        </div>
    </div>

    <!-- Signatures -->
    <div class="section">
        <div class="section-title">Signatures</div>
        <table style="width: 100%; border: 1px solid #dee2e6;">
            <tr>
                <td style="border: 1px solid #dee2e6; padding: 40px 15px; width: 50%; vertical-align: bottom;">
                    <div style="border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; text-align: center; font-size: 10px;">
                        <strong>Sender Signature</strong><br>
                        Date: _______________
                    </div>
                </td>
                <td style="border: 1px solid #dee2e6; padding: 40px 15px; width: 50%; vertical-align: bottom;">
                    <div style="border-top: 1px solid #333; margin-top: 30px; padding-top: 5px; text-align: center; font-size: 10px;">
                        <strong>Receiver Signature</strong><br>
                        Date: _______________
                    </div>
                </td>
            </tr>
        </table>
    </div>
@endsection

@section('footer')
    <div style="text-align: center; margin-bottom: 10px;">
        <strong>Track your package online at {{ $siteSettings['company_website'] ?? 'our website' }}</strong>
    </div>
    <div style="text-align: center; font-size: 10px;">
        Tracking Number: {{ $parcel->tracking_number }} | 
        Customer Service: {{ $siteSettings['company_phone'] ?? 'Phone Number' }}
    </div>
@endsection
