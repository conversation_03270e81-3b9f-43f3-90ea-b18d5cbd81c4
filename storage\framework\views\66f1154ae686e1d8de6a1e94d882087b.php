<?php $__env->startSection('title', $career->title . ' - Careers'); ?>
<?php $__env->startSection('meta_description', Str::limit(strip_tags($career->description), 160)); ?>

<?php $__env->startSection('content'); ?>
    <!-- Career Header -->
    <section class="bg-gradient-to-r from-blue-600 to-green-600 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center gap-3 mb-4">
                    <h1 class="text-3xl md:text-5xl font-bold"><?php echo e($career->title); ?></h1>
                    <?php if($career->is_featured): ?>
                        <span class="bg-yellow-400 text-yellow-900 text-sm font-medium px-3 py-1 rounded-full">
                            Featured
                        </span>
                    <?php endif; ?>
                </div>
                
                <div class="flex flex-wrap gap-6 text-lg mb-6">
                    <?php if($career->department): ?>
                        <div class="flex items-center gap-2">
                            <i class="fas fa-building"></i>
                            <?php echo e($career->department); ?>

                        </div>
                    <?php endif; ?>
                    <div class="flex items-center gap-2">
                        <i class="fas fa-map-marker-alt"></i>
                        <?php echo e($career->location); ?>

                    </div>
                    <div class="flex items-center gap-2">
                        <i class="fas fa-clock"></i>
                        <?php echo e(ucfirst(str_replace('-', ' ', $career->employment_type))); ?>

                    </div>
                    <div class="flex items-center gap-2">
                        <i class="fas fa-chart-line"></i>
                        <?php echo e(ucfirst($career->experience_level)); ?> Level
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <?php if(!$career->isApplicationDeadlinePassed()): ?>
                        <a href="<?php echo e(route('careers.apply', $career->slug)); ?>" 
                           class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors text-center">
                            Apply for This Position
                        </a>
                    <?php else: ?>
                        <span class="bg-gray-500 text-white px-8 py-3 rounded-lg font-medium text-center cursor-not-allowed">
                            Applications Closed
                        </span>
                    <?php endif; ?>
                    <a href="<?php echo e(route('careers.index')); ?>" 
                       class="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-lg font-medium transition-colors text-center border border-white/30">
                        View All Positions
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Career Details -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Job Description -->
                        <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Job Description</h2>
                            <div class="prose prose-lg max-w-none text-gray-700">
                                <?php echo nl2br(e($career->description)); ?>

                            </div>
                        </div>

                        <!-- Responsibilities -->
                        <?php if($career->responsibilities): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Key Responsibilities</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    <?php echo nl2br(e($career->responsibilities)); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Requirements -->
                        <?php if($career->requirements): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    <?php echo nl2br(e($career->requirements)); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Benefits -->
                        <?php if($career->benefits): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Benefits & Perks</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    <?php echo nl2br(e($career->benefits)); ?>

                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Quick Info -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Position Details</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Salary:</span>
                                    <span class="font-medium text-green-600"><?php echo e($career->formatted_salary); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Type:</span>
                                    <span class="font-medium"><?php echo e(ucfirst(str_replace('-', ' ', $career->employment_type))); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Level:</span>
                                    <span class="font-medium"><?php echo e(ucfirst($career->experience_level)); ?></span>
                                </div>
                                <?php if($career->application_deadline): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Deadline:</span>
                                        <span class="font-medium text-red-600"><?php echo e($career->application_deadline->format('M d, Y')); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Required Skills -->
                        <?php if($career->required_skills && count($career->required_skills) > 0): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Required Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $career->required_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                                            <?php echo e($skill); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Preferred Skills -->
                        <?php if($career->preferred_skills && count($career->preferred_skills) > 0): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Preferred Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $career->preferred_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                                            <?php echo e($skill); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Contact Info -->
                        <?php if($career->contact_email || $career->contact_phone): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Information</h3>
                                <div class="space-y-3">
                                    <?php if($career->contact_email): ?>
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                            <a href="mailto:<?php echo e($career->contact_email); ?>" class="text-blue-600 hover:text-blue-800">
                                                <?php echo e($career->contact_email); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($career->contact_phone): ?>
                                        <div class="flex items-center gap-3">
                                            <i class="fas fa-phone text-gray-400"></i>
                                            <a href="tel:<?php echo e($career->contact_phone); ?>" class="text-blue-600 hover:text-blue-800">
                                                <?php echo e($career->contact_phone); ?>

                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Apply Button (Sticky) -->
                        <?php if(!$career->isApplicationDeadlinePassed()): ?>
                            <div class="sticky top-4">
                                <a href="<?php echo e(route('careers.apply', $career->slug)); ?>" 
                                   class="block w-full bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg font-medium transition-colors text-center">
                                    Apply for This Position
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Positions -->
    <?php
        $relatedCareers = \App\Models\Career::active()
            ->where('id', '!=', $career->id)
            ->where(function($query) use ($career) {
                $query->where('department', $career->department)
                      ->orWhere('experience_level', $career->experience_level);
            })
            ->limit(3)
            ->get();
    ?>

    <?php if($relatedCareers->count() > 0): ?>
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Related Positions</h2>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $relatedCareers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedCareer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                                <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($relatedCareer->title); ?></h3>
                                <div class="text-sm text-gray-600 mb-3">
                                    <?php echo e($relatedCareer->department); ?> • <?php echo e($relatedCareer->location); ?>

                                </div>
                                <p class="text-gray-700 mb-4 line-clamp-3">
                                    <?php echo e(Str::limit(strip_tags($relatedCareer->description), 100)); ?>

                                </p>
                                <a href="<?php echo e(route('careers.show', $relatedCareer->slug)); ?>" 
                                   class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium transition-colors">
                                    View Details
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/careers/show.blade.php ENDPATH**/ ?>