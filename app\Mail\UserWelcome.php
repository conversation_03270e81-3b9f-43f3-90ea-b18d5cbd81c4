<?php

namespace App\Mail;

use App\Models\User;
use App\Models\SiteSetting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class UserWelcome extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;
    public $temporaryPassword;
    public $siteSettings;
    public $loginUrl;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, string $temporaryPassword)
    {
        $this->user = $user;
        $this->temporaryPassword = $temporaryPassword;
        $this->siteSettings = SiteSetting::pluck('value', 'key_name')->toArray();
        
        // Determine login URL based on user role
        $this->loginUrl = match($user->role) {
            'admin', 'staff' => route('admin.login'),
            'customer' => route('customer.login'),
            default => route('login')
        };
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $siteName = $this->siteSettings['site_name'] ?? config('app.name');
        
        return new Envelope(
            subject: "Welcome to {$siteName} - Your Account is Ready!",
            from: $this->siteSettings['notification_email'] ?? config('mail.from.address'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.user-welcome',
            with: [
                'user' => $this->user,
                'temporaryPassword' => $this->temporaryPassword,
                'siteSettings' => $this->siteSettings,
                'loginUrl' => $this->loginUrl,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
