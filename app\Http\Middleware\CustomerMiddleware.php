<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CustomerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('customer.login')
                           ->with('error', 'Please log in to access this page.');
        }

        // Check if user is a customer
        if (Auth::user()->role !== 'customer') {
            Auth::logout();
            return redirect()->route('customer.login')
                           ->with('error', 'Access denied. Customer account required.');
        }

        // Check if user account is active
        if (!Auth::user()->is_active) {
            Auth::logout();
            return redirect()->route('customer.login')
                           ->with('error', 'Your account has been deactivated. Please contact support.');
        }

        return $next($request);
    }
}
