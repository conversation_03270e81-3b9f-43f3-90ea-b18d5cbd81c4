@extends('layouts.customer')

@section('title', 'My Quotes')
@section('page-title', 'My Quotes')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Quick Actions -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-0">My Quote Requests</h4>
                    <p class="text-muted mb-0">Track and manage your shipping quotes</p>
                </div>
                <a href="{{ route('customer.quotes.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Request New Quote
                </a>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('customer.quotes.index') }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Quote number, description...">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                @foreach($statuses as $value => $label)
                                    <option value="{{ $value }}" {{ request('status') === $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="service_type" class="form-label">Service Type</label>
                            <select class="form-select" id="service_type" name="service_type">
                                <option value="">All Services</option>
                                @foreach($serviceTypes as $value => $label)
                                    <option value="{{ $value }}" {{ request('service_type') === $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Quotes List -->
            @if($quotes->count() > 0)
                <div class="row">
                    @foreach($quotes as $quote)
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 quote-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">{{ $quote->quote_number }}</h6>
                                        <small class="text-muted">{{ $quote->created_at->format('M d, Y') }}</small>
                                    </div>
                                    <span class="badge bg-{{ $quote->status_badge_color }}">
                                        {{ $quote->formatted_status }}
                                    </span>
                                </div>
                                
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6 class="text-primary mb-2">
                                            <i class="fas fa-truck me-1"></i>
                                            {{ $quote->formatted_service_type }}
                                        </h6>
                                        <p class="text-muted small mb-2">{{ Str::limit($quote->description, 100) }}</p>
                                    </div>

                                    <div class="mb-3">
                                        <div class="row text-center">
                                            <div class="col-6">
                                                <div class="border-end">
                                                    <i class="fas fa-map-marker-alt text-success"></i>
                                                    <div class="small">
                                                        <strong>From</strong><br>
                                                        {{ $quote->origin_city }}, {{ $quote->origin_country }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <i class="fas fa-map-marker-alt text-danger"></i>
                                                <div class="small">
                                                    <strong>To</strong><br>
                                                    {{ $quote->destination_city }}, {{ $quote->destination_country }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">Priority:</small>
                                                <br><span class="badge bg-{{ $quote->priority_badge_color }}">{{ $quote->formatted_priority }}</span>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Packages:</small>
                                                <br><strong>{{ $quote->package_count }}</strong>
                                            </div>
                                        </div>
                                    </div>

                                    @if($quote->quoted_price)
                                        <div class="mb-3">
                                            <div class="text-center p-3 bg-light rounded">
                                                <h5 class="text-primary mb-0">
                                                    ${{ number_format($quote->final_price ?? $quote->quoted_price, 2) }}
                                                </h5>
                                                <small class="text-muted">Quoted Price</small>
                                                @if($quote->expires_at)
                                                    <br><small class="text-warning">
                                                        Expires {{ $quote->expires_at->diffForHumans() }}
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    @endif

                                    @if($quote->assignedTo)
                                        <div class="mb-3">
                                            <small class="text-muted">Assigned to:</small>
                                            <br><span class="badge bg-secondary">{{ $quote->assignedTo->name }}</span>
                                        </div>
                                    @endif
                                </div>
                                
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <a href="{{ route('customer.quotes.show', $quote) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                        
                                        <div class="btn-group">
                                            @if($quote->canBeAccepted())
                                                <button type="button" class="btn btn-sm btn-success" 
                                                        onclick="acceptQuote({{ $quote->id }})">
                                                    <i class="fas fa-check me-1"></i>Accept
                                                </button>
                                            @endif
                                            
                                            @if($quote->canBeRejected())
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="rejectQuote({{ $quote->id }})">
                                                    <i class="fas fa-times me-1"></i>Reject
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $quotes->links() }}
                </div>
            @else
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-quote-left fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No quotes found</h5>
                        <p class="text-muted">You haven't requested any quotes yet.</p>
                        <a href="{{ route('customer.quotes.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Request Your First Quote
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .quote-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }
    
    .quote-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .quote-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
    }
</style>
@endpush

@push('scripts')
<script>
    function acceptQuote(quoteId) {
        if (confirm('Are you sure you want to accept this quote? This action cannot be undone.')) {
            fetch(`/customer/quotes/${quoteId}/accept`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Error accepting quote. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }

    function rejectQuote(quoteId) {
        if (confirm('Are you sure you want to reject this quote?')) {
            fetch(`/customer/quotes/${quoteId}/reject`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Error rejecting quote. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }
</script>
@endpush
