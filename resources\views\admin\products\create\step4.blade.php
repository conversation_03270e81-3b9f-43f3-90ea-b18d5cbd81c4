@extends('layouts.admin')

@section('title', 'Create Product - Step 4')
@section('page-title', 'Create New Product - Images & Media')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.create.step3') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Step 3
        </a>
    </div>
@endsection

@section('content')
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="progress-steps">
                        <div class="step completed">
                            <div class="step-number"><i class="fas fa-check"></i></div>
                            <div class="step-title">Basic Info</div>
                        </div>
                        <div class="step completed">
                            <div class="step-number"><i class="fas fa-check"></i></div>
                            <div class="step-title">Content & SEO</div>
                        </div>
                        <div class="step completed">
                            <div class="step-number"><i class="fas fa-check"></i></div>
                            <div class="step-title">Pricing & Inventory</div>
                        </div>
                        <div class="step active">
                            <div class="step-number">4</div>
                            <div class="step-title">Images & Media</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.products.create.step4.store') }}" enctype="multipart/form-data">
                @csrf

                <!-- Featured Image -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-image me-2"></i>
                            Featured Image
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="featured_image" class="form-label">Product Featured Image</label>
                                <input type="file" class="form-control @error('featured_image') is-invalid @enderror" 
                                       id="featured_image" name="featured_image" accept="image/*" 
                                       onchange="previewFeaturedImage(this)">
                                @error('featured_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Recommended: 800x800px, max 2MB (JPG, PNG, GIF)</small>
                            </div>
                            <div class="col-md-6">
                                <div id="featured-image-preview" class="text-center" style="display: none;">
                                    <img id="featured-preview-img" src="#" alt="Featured Image Preview" 
                                         class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="removeFeaturedImage()">
                                            <i class="fas fa-trash me-1"></i> Remove
                                        </button>
                                    </div>
                                </div>
                                <div id="featured-image-placeholder" class="text-center p-4 border border-dashed rounded">
                                    <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No image selected</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gallery Images -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>
                            Product Gallery
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="gallery_images" class="form-label">Gallery Images</label>
                            <input type="file" class="form-control @error('gallery_images.*') is-invalid @enderror" 
                                   id="gallery_images" name="gallery_images[]" accept="image/*" multiple
                                   onchange="previewGalleryImages(this)">
                            @error('gallery_images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Select multiple images for product gallery. Max 2MB each (JPG, PNG, GIF)</small>
                        </div>

                        <!-- Gallery Preview -->
                        <div id="gallery-preview" class="row" style="display: none;">
                            <div class="col-12 mb-3">
                                <h6>Gallery Preview:</h6>
                            </div>
                        </div>

                        <!-- Gallery Placeholder -->
                        <div id="gallery-placeholder" class="text-center p-4 border border-dashed rounded">
                            <i class="fas fa-images fa-3x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No gallery images selected</p>
                            <small class="text-muted">You can select multiple images at once</small>
                        </div>
                    </div>
                </div>

                <!-- Additional Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Additional Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="reviews_allowed" name="reviews_allowed" 
                                   value="1" {{ old('reviews_allowed', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="reviews_allowed">
                                <strong>Allow Customer Reviews</strong>
                                <br><small class="text-muted">Enable customers to leave reviews for this product</small>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                    <a href="{{ route('admin.products.create.step3') }}" class="btn btn-outline-secondary" id="prevBtn">
                        <i class="fas fa-arrow-left me-1"></i> Previous: Pricing & Inventory
                    </a>
                    <button type="submit" class="btn btn-success btn-lg" id="createBtn">
                        <i class="fas fa-check me-1"></i> Create Product
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Progress Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-flag-checkered me-2"></i>
                        Final Step!
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Images & Media</h6>
                    <p class="text-muted mb-3">
                        Upload product images and finalize your product setup.
                    </p>
                    
                    <div class="progress mb-3">
                        <div class="progress-bar bg-success" role="progressbar" style="width: 100%">100%</div>
                    </div>
                    
                    <h6>Product Summary:</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        <strong>{{ $step1Data['name'] }}</strong><br>
                        <small class="text-muted">{{ $step1Data['short_description'] ?? 'No short description' }}</small>
                        <hr class="my-2">
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">Category</small><br>
                                <strong>{{ $step1Data['category_name'] ?? 'N/A' }}</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Status</small><br>
                                @if($step1Data['is_active'] ?? false)
                                    <span class="badge bg-success">Active</span>
                                @else
                                    <span class="badge bg-secondary">Inactive</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-success">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Almost done!</strong> Upload images and click "Create Product" to finish.
                    </div>
                </div>
            </div>

            <!-- Image Guidelines -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Image Guidelines
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Featured Image:</strong> Main product photo (800x800px recommended)</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Gallery:</strong> Multiple angles and details</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Format:</strong> JPG, PNG, or GIF</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Size:</strong> Maximum 2MB per image</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Quality:</strong> High resolution for best results</small>
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Background:</strong> Clean, neutral backgrounds work best</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        After Creation
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">What you can do after creating the product:</p>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-eye text-primary me-2"></i>
                            <small>Preview the product</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-edit text-info me-2"></i>
                            <small>Edit product details</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-copy text-secondary me-2"></i>
                            <small>Duplicate for similar products</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-chart-line text-success me-2"></i>
                            <small>View sales analytics</small>
                        </li>
                        <li>
                            <i class="fas fa-share text-warning me-2"></i>
                            <small>Share on social media</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin: 0 20px;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #198754;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .step.active .step-number {
        background-color: #0d6efd;
        color: white;
    }

    .step.completed .step-number {
        background-color: #198754;
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6c757d;
        text-align: center;
    }

    .step.active .step-title {
        color: #0d6efd;
    }

    .step.completed .step-title {
        color: #198754;
    }

    .gallery-item {
        position: relative;
        margin-bottom: 1rem;
    }

    .gallery-item .remove-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(220, 53, 69, 0.8);
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .gallery-item .remove-btn:hover {
        background: rgba(220, 53, 69, 1);
    }

    .border-dashed {
        border-style: dashed !important;
    }

    /* Loading button styles */
    .btn.loading {
        position: relative;
        pointer-events: none;
        opacity: 0.8;
    }

    .btn.loading .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush

@push('scripts')
<script>
    // Preview featured image
    function previewFeaturedImage(input) {
        const preview = document.getElementById('featured-image-preview');
        const previewImg = document.getElementById('featured-preview-img');
        const placeholder = document.getElementById('featured-image-placeholder');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
                placeholder.style.display = 'none';
            };
            
            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
            placeholder.style.display = 'block';
        }
    }

    // Remove featured image
    function removeFeaturedImage() {
        document.getElementById('featured_image').value = '';
        document.getElementById('featured-image-preview').style.display = 'none';
        document.getElementById('featured-image-placeholder').style.display = 'block';
    }

    // Preview gallery images
    function previewGalleryImages(input) {
        const preview = document.getElementById('gallery-preview');
        const placeholder = document.getElementById('gallery-placeholder');
        
        // Clear existing previews
        const existingPreviews = preview.querySelectorAll('.gallery-item');
        existingPreviews.forEach(item => item.remove());
        
        if (input.files && input.files.length > 0) {
            preview.style.display = 'block';
            placeholder.style.display = 'none';
            
            Array.from(input.files).forEach((file, index) => {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const galleryItem = document.createElement('div');
                    galleryItem.className = 'col-md-3 gallery-item';
                    galleryItem.innerHTML = `
                        <div class="position-relative">
                            <img src="${e.target.result}" alt="Gallery Image ${index + 1}" 
                                 class="img-thumbnail w-100" style="height: 150px; object-fit: cover;">
                            <button type="button" class="remove-btn" onclick="removeGalleryImage(this, ${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    
                    preview.appendChild(galleryItem);
                };
                
                reader.readAsDataURL(file);
            });
        } else {
            preview.style.display = 'none';
            placeholder.style.display = 'block';
        }
    }

    // Remove gallery image
    function removeGalleryImage(button, index) {
        const input = document.getElementById('gallery_images');
        const dt = new DataTransfer();
        
        // Rebuild file list without the removed file
        Array.from(input.files).forEach((file, i) => {
            if (i !== index) {
                dt.items.add(file);
            }
        });
        
        input.files = dt.files;
        
        // Remove the preview
        button.closest('.gallery-item').remove();
        
        // Check if any images remain
        if (input.files.length === 0) {
            document.getElementById('gallery-preview').style.display = 'none';
            document.getElementById('gallery-placeholder').style.display = 'block';
        }
    }

    // File size validation
    function validateFileSize(input, maxSizeMB = 2) {
        const files = input.files;
        const maxSize = maxSizeMB * 1024 * 1024; // Convert to bytes
        
        for (let i = 0; i < files.length; i++) {
            if (files[i].size > maxSize) {
                alert(`File "${files[i].name}" is too large. Maximum size is ${maxSizeMB}MB.`);
                input.value = '';
                return false;
            }
        }
        return true;
    }

    // Add file size validation to inputs
    document.getElementById('featured_image').addEventListener('change', function() {
        if (!validateFileSize(this)) {
            removeFeaturedImage();
        }
    });

    document.getElementById('gallery_images').addEventListener('change', function() {
        if (!validateFileSize(this)) {
            this.value = '';
            document.getElementById('gallery-preview').style.display = 'none';
            document.getElementById('gallery-placeholder').style.display = 'block';
        }
    });

    // Form submission confirmation
    document.querySelector('form').addEventListener('submit', function(e) {
        const featuredImage = document.getElementById('featured_image').files.length > 0;
        const galleryImages = document.getElementById('gallery_images').files.length > 0;
        
        if (!featuredImage && !galleryImages) {
            const confirm = window.confirm('No images have been uploaded. Are you sure you want to create the product without images?');
            if (!confirm) {
                e.preventDefault();
                return false;
            }
        }
        
        // Show loading state
        const submitBtn = document.getElementById('createBtn');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Creating Product...';
        }
    });

    // Add spinner to previous button click
    document.getElementById('prevBtn').addEventListener('click', function(e) {
        this.classList.add('loading');
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    });
</script>
@endpush
