<?php

namespace Database\Seeders;

use App\Models\Carrier;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CarrierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $carriers = [
            [
                'name' => 'DHL Express',
                'code' => 'DHL',
                'type' => 'dhl',
                'description' => 'International express delivery service',
                'website' => 'https://www.dhl.com',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-225-5345',
                'service_types' => ['express', 'overnight', 'international'],
                'base_rate' => 15.00,
                'per_kg_rate' => 8.50,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'USPS (Post Office)',
                'code' => 'USPS',
                'type' => 'post_office',
                'description' => 'United States Postal Service',
                'website' => 'https://www.usps.com',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-275-8777',
                'service_types' => ['standard', 'priority', 'express'],
                'base_rate' => 8.00,
                'per_kg_rate' => 5.25,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Atrix Custom Delivery',
                'code' => 'ATX',
                'type' => 'custom',
                'description' => 'Our in-house delivery service for local and regional shipments',
                'website' => null,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-123-4567',
                'service_types' => ['standard', 'express', 'same_day'],
                'base_rate' => 12.00,
                'per_kg_rate' => 6.75,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'FedEx',
                'code' => 'FEDEX',
                'type' => 'fedex',
                'description' => 'Federal Express delivery service',
                'website' => 'https://www.fedex.com',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-463-3339',
                'service_types' => ['ground', 'express', 'overnight'],
                'base_rate' => 14.00,
                'per_kg_rate' => 7.80,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'UPS',
                'code' => 'UPS',
                'type' => 'ups',
                'description' => 'United Parcel Service',
                'website' => 'https://www.ups.com',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '******-742-5877',
                'service_types' => ['ground', 'air', 'international'],
                'base_rate' => 13.50,
                'per_kg_rate' => 7.25,
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($carriers as $carrierData) {
            Carrier::create($carrierData);
        }
    }
}
