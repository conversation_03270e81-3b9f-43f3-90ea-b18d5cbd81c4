<?php

namespace App\Services;

use App\Models\Parcel;
use App\Models\Order;
use App\Models\Payment;
use App\Models\SiteSetting;
use App\Models\User;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class StripePaymentService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent for the parcel
     */
    public function createPaymentIntent(Parcel $parcel, array $options = []): array
    {
        try {
            // Get currency settings
            $currency = strtolower(SiteSetting::getValue('base_currency', 'USD'));
            
            // Convert amount to cents (Stripe requires amounts in smallest currency unit)
            $amount = $this->convertToCents($parcel->total_cost, $currency);

            // Create or get Stripe customer
            $customer = $this->createOrGetCustomer($parcel->user);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount,
                'currency' => $currency,
                'customer' => $customer->id,
                'description' => "Payment for parcel {$parcel->tracking_number}",
                'metadata' => [
                    'parcel_id' => $parcel->id,
                    'tracking_number' => $parcel->tracking_number,
                    'user_id' => $parcel->user_id,
                ],
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'confirmation_method' => 'manual',
                'confirm' => false,
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $parcel->total_cost,
                'currency' => $currency,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent creation failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage(),
                'code' => $e->getStripeCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'code' => $e->getStripeCode(),
            ];
        }
    }

    /**
     * Confirm a payment intent
     */
    public function confirmPaymentIntent(string $paymentIntentId, array $paymentMethod = []): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            $confirmData = [];
            if (!empty($paymentMethod)) {
                $confirmData['payment_method'] = $paymentMethod;
            }

            $paymentIntent = $paymentIntent->confirm($confirmData);

            return [
                'success' => true,
                'status' => $paymentIntent->status,
                'payment_intent' => $paymentIntent,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Payment Intent confirmation failed', [
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage(),
                'code' => $e->getStripeCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'code' => $e->getStripeCode(),
            ];
        }
    }

    /**
     * Handle successful payment
     */
    public function handleSuccessfulPayment(string $paymentIntentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            
            if ($paymentIntent->status === 'succeeded') {
                return [
                    'success' => true,
                    'transaction_id' => $paymentIntent->id,
                    'status' => 'completed',
                    'message' => 'Payment processed successfully via Stripe.',
                    'gateway_response' => [
                        'payment_intent_id' => $paymentIntent->id,
                        'status' => $paymentIntent->status,
                        'amount_received' => $paymentIntent->amount_received,
                        'currency' => $paymentIntent->currency,
                        'payment_method' => $paymentIntent->payment_method,
                        'created' => $paymentIntent->created,
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Payment not completed',
                    'status' => $paymentIntent->status,
                ];
            }

        } catch (ApiErrorException $e) {
            Log::error('Stripe payment verification failed', [
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create or get Stripe customer
     */
    private function createOrGetCustomer(User $user): Customer
    {
        try {
            // Check if user already has a Stripe customer ID
            if ($user->stripe_customer_id) {
                return Customer::retrieve($user->stripe_customer_id);
            }

            // Create new Stripe customer
            $customer = Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);

            // Save Stripe customer ID to user
            $user->update(['stripe_customer_id' => $customer->id]);

            return $customer;

        } catch (ApiErrorException $e) {
            Log::error('Stripe customer creation failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Convert amount to cents based on currency
     */
    private function convertToCents(float $amount, string $currency): int
    {
        // Zero-decimal currencies (amounts are in the smallest unit already)
        $zeroDecimalCurrencies = [
            'bif', 'clp', 'djf', 'gnf', 'jpy', 'kmf', 'krw', 
            'mga', 'pyg', 'rwf', 'ugx', 'vnd', 'vuv', 'xaf', 
            'xof', 'xpf'
        ];

        if (in_array(strtolower($currency), $zeroDecimalCurrencies)) {
            return (int) round($amount);
        }

        // Most currencies use 2 decimal places
        return (int) round($amount * 100);
    }

    /**
     * Convert cents back to amount based on currency
     */
    public function convertFromCents(int $cents, string $currency): float
    {
        $zeroDecimalCurrencies = [
            'bif', 'clp', 'djf', 'gnf', 'jpy', 'kmf', 'krw', 
            'mga', 'pyg', 'rwf', 'ugx', 'vnd', 'vuv', 'xaf', 
            'xof', 'xpf'
        ];

        if (in_array(strtolower($currency), $zeroDecimalCurrencies)) {
            return (float) $cents;
        }

        return $cents / 100;
    }

    /**
     * Create a refund
     */
    public function createRefund(string $paymentIntentId, float $amount = null): array
    {
        try {
            $refundData = ['payment_intent' => $paymentIntentId];
            
            if ($amount !== null) {
                $currency = strtolower(SiteSetting::getValue('base_currency', 'USD'));
                $refundData['amount'] = $this->convertToCents($amount, $currency);
            }

            $refund = \Stripe\Refund::create($refundData);

            return [
                'success' => true,
                'refund_id' => $refund->id,
                'status' => $refund->status,
                'amount' => $this->convertFromCents($refund->amount, $refund->currency),
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe refund failed', [
                'payment_intent_id' => $paymentIntentId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment method details
     */
    public function getPaymentMethodDetails(string $paymentMethodId): array
    {
        try {
            $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodId);

            return [
                'success' => true,
                'type' => $paymentMethod->type,
                'card' => $paymentMethod->card ? [
                    'brand' => $paymentMethod->card->brand,
                    'last4' => $paymentMethod->card->last4,
                    'exp_month' => $paymentMethod->card->exp_month,
                    'exp_year' => $paymentMethod->card->exp_year,
                ] : null,
            ];

        } catch (ApiErrorException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a payment intent for an order
     */
    public function createOrderPaymentIntent(Order $order, array $options = []): array
    {
        try {
            // Get currency settings
            $currency = strtolower(SiteSetting::getValue('base_currency', 'USD'));

            // Convert amount to cents (Stripe requires amounts in smallest currency unit)
            $amount = $this->convertToCents($order->total_amount, $currency);

            // Create or get Stripe customer
            $customer = $this->createOrGetCustomer($order->customer);

            // Create payment intent
            $paymentIntent = PaymentIntent::create([
                'amount' => $amount,
                'currency' => $currency,
                'customer' => $customer->id,
                'description' => "Payment for order {$order->order_number}",
                'metadata' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'customer_id' => $order->customer_id,
                ],
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
                'confirmation_method' => 'manual',
                'confirm' => false,
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id,
                'amount' => $order->total_amount,
                'currency' => $currency,
            ];

        } catch (\Exception $e) {
            Log::error('Stripe order payment intent creation failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create payment intent: ' . $e->getMessage(),
            ];
        }
    }
}
