<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;

class TeamMemberController extends Controller
{
    /**
     * Display a listing of team members
     */
    public function index(): View
    {
        $teamMembers = TeamMember::orderBy('sort_order')->orderBy('name')->paginate(20);

        return view('admin.team.index', compact('teamMembers'));
    }

    /**
     * Show the form for creating a new team member
     */
    public function create(): View
    {
        return view('admin.team.create');
    }

    /**
     * Store a newly created team member
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'department' => 'nullable|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'social_links' => 'nullable|array',
            'social_links.*' => 'nullable|url|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['is_active'] = $request->has('is_active');

        // Handle photo upload
        if ($request->hasFile('photo')) {
            $validated['photo'] = $request->file('photo')->store('uploads/team', 'public');
        }

        // Set sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = TeamMember::max('sort_order') + 1;
        }

        TeamMember::create($validated);

        return redirect()->route('admin.cms.team.index')
                        ->with('success', 'Team member created successfully.');
    }

    /**
     * Display the specified team member
     */
    public function show(TeamMember $team): View
    {
        return view('admin.team.show', compact('team'));
    }

    /**
     * Show the form for editing the specified team member
     */
    public function edit(TeamMember $team): View
    {
        return view('admin.team.edit', compact('team'));
    }

    /**
     * Update the specified team member
     */
    public function update(Request $request, TeamMember $team): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'department' => 'nullable|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'social_links' => 'nullable|array',
            'social_links.*' => 'nullable|url|max:500',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['is_active'] = $request->has('is_active');

        // Handle photo upload
        if ($request->hasFile('photo')) {
            // Delete old photo
            if ($team->photo && Storage::disk('public')->exists($team->photo)) {
                Storage::disk('public')->delete($team->photo);
            }

            $validated['photo'] = $request->file('photo')->store('uploads/team', 'public');
        }

        $team->update($validated);

        return redirect()->route('admin.cms.team.show', $team)
                        ->with('success', 'Team member updated successfully.');
    }

    /**
     * Remove the specified team member
     */
    public function destroy(TeamMember $team): RedirectResponse
    {
        // Delete photo if exists
        if ($team->photo && Storage::disk('public')->exists($team->photo)) {
            Storage::disk('public')->delete($team->photo);
        }

        $team->delete();

        return redirect()->route('admin.cms.team.index')
                        ->with('success', 'Team member deleted successfully.');
    }

    /**
     * Update team members display order
     */
    public function updateOrder(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'orders' => 'required|array',
            'orders.*' => 'integer|exists:team_members,id',
        ]);

        foreach ($validated['orders'] as $order => $id) {
            TeamMember::where('id', $id)->update(['sort_order' => $order + 1]);
        }

        return redirect()->route('admin.cms.team.index')
                        ->with('success', 'Team members order updated successfully.');
    }

    /**
     * Toggle team member active status
     */
    public function toggleStatus(TeamMember $team): RedirectResponse
    {
        $team->update(['is_active' => !$team->is_active]);

        $status = $team->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Team member {$status} successfully.");
    }
}
