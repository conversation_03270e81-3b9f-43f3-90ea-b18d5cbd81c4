@extends('layouts.frontend')

@section('title', 'My Addresses')

@section('content')
<!-- Addresses Header -->
<section class="py-12 bg-gradient-to-r from-green-600 to-green-700">
    <div class="container mx-auto px-4">
        <div class="text-center text-white">
            <h1 class="text-4xl font-bold mb-4">My Addresses</h1>
            <p class="text-xl opacity-90">Manage your shipping and billing addresses</p>
        </div>
    </div>
</section>

<!-- Addresses Content -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Add New Address Button -->
            <div class="mb-8">
                <button onclick="showAddAddressModal()" 
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add New Address
                </button>
            </div>

            @if($addresses->count() > 0)
                <!-- Addresses Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($addresses as $address)
                    <div class="bg-white rounded-lg shadow-lg p-6 relative">
                        <!-- Default Badge -->
                        @if($address->is_default)
                        <div class="absolute top-4 right-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                                Default
                            </span>
                        </div>
                        @endif

                        <!-- Address Type -->
                        <div class="mb-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                {{ $address->type === 'shipping' ? 'bg-blue-100 text-blue-800' : 
                                   ($address->type === 'billing' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800') }}">
                                <i class="fas fa-{{ $address->type === 'shipping' ? 'truck' : ($address->type === 'billing' ? 'credit-card' : 'home') }} mr-1"></i>
                                {{ ucfirst($address->type) }}
                            </span>
                        </div>

                        <!-- Address Details -->
                        <div class="mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                {{ $address->display_label }}
                            </h3>
                            <div class="text-gray-600 space-y-1">
                                <p class="font-medium">{{ $address->full_name }}</p>
                                @if($address->company)
                                <p>{{ $address->company }}</p>
                                @endif
                                <p>{{ $address->address_line_1 }}</p>
                                @if($address->address_line_2)
                                <p>{{ $address->address_line_2 }}</p>
                                @endif
                                <p>{{ $address->city }}, {{ $address->state }} {{ $address->postal_code }}</p>
                                <p>{{ $address->country }}</p>
                                @if($address->phone)
                                <p><i class="fas fa-phone mr-1"></i>{{ $address->phone }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                            <div class="flex space-x-2">
                                <button onclick="editAddress({{ $address->id }})" 
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                                
                                @if(!$address->is_default)
                                <button onclick="setDefaultAddress({{ $address->id }})" 
                                        class="text-green-600 hover:text-green-800 text-sm font-medium">
                                    <i class="fas fa-star mr-1"></i>Set Default
                                </button>
                                @endif
                            </div>
                            
                            <button onclick="deleteAddress({{ $address->id }})" 
                                    class="text-red-600 hover:text-red-800 text-sm font-medium">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </div>
                    </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-16">
                    <div class="mb-8">
                        <i class="fas fa-map-marker-alt text-6xl text-gray-300"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">No addresses found</h2>
                    <p class="text-gray-600 mb-8">Add your first address to get started with faster checkout.</p>
                    <button onclick="showAddAddressModal()" 
                            class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        Add Your First Address
                    </button>
                </div>
            @endif
        </div>
    </div>
</section>

<!-- Add/Edit Address Modal -->
<div id="address-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 id="modal-title" class="text-lg font-bold text-gray-900">Add New Address</h3>
                <button onclick="closeAddressModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="address-form">
                @csrf
                <input type="hidden" id="address-id" name="address_id">
                <input type="hidden" id="form-method" value="POST">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Type</label>
                    <select name="type" id="address-type" required 
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="shipping">Shipping</option>
                        <option value="billing">Billing</option>
                        <option value="both">Both</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Label (Optional)</label>
                    <input type="text" name="label" id="address-label" placeholder="e.g., Home, Office" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" name="first_name" id="first-name" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" name="last_name" id="last-name" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Company (Optional)</label>
                    <input type="text" name="company" id="company" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 1</label>
                    <input type="text" name="address_line_1" id="address-line-1" required 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address Line 2 (Optional)</label>
                    <input type="text" name="address_line_2" id="address-line-2" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <input type="text" name="city" id="city" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">State</label>
                        <input type="text" name="state" id="state" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
                        <input type="text" name="postal_code" id="postal-code" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                        <input type="text" name="country" id="country" required 
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                    <input type="tel" name="phone" id="phone" 
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_default" id="is-default" class="mr-2">
                        <span class="text-sm text-gray-700">Set as default address</span>
                    </label>
                </div>
                
                <div class="flex gap-4">
                    <button type="button" onclick="closeAddressModal()" 
                            class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg font-medium transition-colors">
                        Cancel
                    </button>
                    <button type="submit" id="submit-btn"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                        Add Address
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Show add address modal
function showAddAddressModal() {
    document.getElementById('modal-title').textContent = 'Add New Address';
    document.getElementById('submit-btn').textContent = 'Add Address';
    document.getElementById('form-method').value = 'POST';
    document.getElementById('address-id').value = '';
    document.getElementById('address-form').reset();
    document.getElementById('address-modal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

// Close address modal
function closeAddressModal() {
    document.getElementById('address-modal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    document.getElementById('address-form').reset();
}

// Edit address
function editAddress(addressId) {
    // Fetch address data and populate form
    fetch(`/addresses/${addressId}/edit`)
        .then(response => response.text())
        .then(html => {
            // For now, just show the modal - in a real implementation,
            // you'd fetch the address data via API and populate the form
            document.getElementById('modal-title').textContent = 'Edit Address';
            document.getElementById('submit-btn').textContent = 'Update Address';
            document.getElementById('form-method').value = 'PUT';
            document.getElementById('address-id').value = addressId;
            document.getElementById('address-modal').classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to load address data', 'error');
        });
}

// Set default address
function setDefaultAddress(addressId) {
    fetch(`/addresses/${addressId}/set-default`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

// Delete address
function deleteAddress(addressId) {
    if (!confirm('Are you sure you want to delete this address?')) {
        return;
    }
    
    fetch(`/addresses/${addressId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

// Handle form submission
document.getElementById('address-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    const method = document.getElementById('form-method').value;
    const addressId = document.getElementById('address-id').value;
    
    let url = '/addresses';
    if (method === 'PUT' && addressId) {
        url = `/addresses/${addressId}`;
    }
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeAddressModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification(data.message || 'Failed to save address', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
});
</script>
@endpush
