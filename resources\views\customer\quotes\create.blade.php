@extends('layouts.customer')

@section('title', 'Request Quote')
@section('page-title', 'Request a Quote')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-quote-left me-2"></i>
                        Request a Quote
                    </h5>
                    <p class="text-muted mb-0">Fill out the form below to get a customized quote for your shipping needs.</p>
                </div>
                
                <div class="card-body">
                    @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    @if($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    @endif

                    <form action="{{ route('customer.quotes.store') }}" method="POST" id="quoteForm">
                        @csrf
                        <input type="hidden" name="quote_type" value="shipping">
                        
                        <!-- Service Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-truck me-2"></i>Service Information
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="service_type" class="form-label">Service Type *</label>
                                <select class="form-select @error('service_type') is-invalid @enderror" 
                                        id="service_type" name="service_type" required>
                                    <option value="">Select Service Type</option>
                                    @foreach($serviceTypes as $value => $label)
                                        <option value="{{ $value }}" {{ old('service_type') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('service_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">Priority *</label>
                                <select class="form-select @error('priority') is-invalid @enderror" 
                                        id="priority" name="priority" required>
                                    @foreach($priorities as $value => $label)
                                        <option value="{{ $value }}" {{ old('priority', 'standard') === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('priority')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">Description *</label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="4" required
                                          placeholder="Please describe your shipping requirements in detail...">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Origin Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Origin (Pickup Location)
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="origin_address" class="form-label">Address *</label>
                                <input type="text" class="form-control @error('origin_address') is-invalid @enderror" 
                                       id="origin_address" name="origin_address" value="{{ old('origin_address') }}" required>
                                @error('origin_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="origin_city" class="form-label">City *</label>
                                <input type="text" class="form-control @error('origin_city') is-invalid @enderror" 
                                       id="origin_city" name="origin_city" value="{{ old('origin_city') }}" required>
                                @error('origin_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="origin_state" class="form-label">State/Province</label>
                                <input type="text" class="form-control @error('origin_state') is-invalid @enderror" 
                                       id="origin_state" name="origin_state" value="{{ old('origin_state') }}">
                                @error('origin_state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="origin_postal_code" class="form-label">Postal Code</label>
                                <input type="text" class="form-control @error('origin_postal_code') is-invalid @enderror" 
                                       id="origin_postal_code" name="origin_postal_code" value="{{ old('origin_postal_code') }}">
                                @error('origin_postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="origin_country" class="form-label">Country *</label>
                                <input type="text" class="form-control @error('origin_country') is-invalid @enderror" 
                                       id="origin_country" name="origin_country" value="{{ old('origin_country') }}" required>
                                @error('origin_country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Destination Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Destination (Delivery Location)
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="destination_address" class="form-label">Address *</label>
                                <input type="text" class="form-control @error('destination_address') is-invalid @enderror" 
                                       id="destination_address" name="destination_address" value="{{ old('destination_address') }}" required>
                                @error('destination_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="destination_city" class="form-label">City *</label>
                                <input type="text" class="form-control @error('destination_city') is-invalid @enderror" 
                                       id="destination_city" name="destination_city" value="{{ old('destination_city') }}" required>
                                @error('destination_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="destination_state" class="form-label">State/Province</label>
                                <input type="text" class="form-control @error('destination_state') is-invalid @enderror" 
                                       id="destination_state" name="destination_state" value="{{ old('destination_state') }}">
                                @error('destination_state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="destination_postal_code" class="form-label">Postal Code</label>
                                <input type="text" class="form-control @error('destination_postal_code') is-invalid @enderror" 
                                       id="destination_postal_code" name="destination_postal_code" value="{{ old('destination_postal_code') }}">
                                @error('destination_postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="destination_country" class="form-label">Country *</label>
                                <input type="text" class="form-control @error('destination_country') is-invalid @enderror" 
                                       id="destination_country" name="destination_country" value="{{ old('destination_country') }}" required>
                                @error('destination_country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Package Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-box me-2"></i>Package Information
                                </h6>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="package_count" class="form-label">Number of Packages *</label>
                                <input type="number" class="form-control @error('package_count') is-invalid @enderror" 
                                       id="package_count" name="package_count" value="{{ old('package_count', 1) }}" min="1" required>
                                @error('package_count')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="total_weight" class="form-label">Total Weight</label>
                                <input type="number" class="form-control @error('total_weight') is-invalid @enderror" 
                                       id="total_weight" name="total_weight" value="{{ old('total_weight') }}" step="0.1" min="0">
                                @error('total_weight')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="weight_unit" class="form-label">Weight Unit *</label>
                                <select class="form-select @error('weight_unit') is-invalid @enderror" 
                                        id="weight_unit" name="weight_unit" required>
                                    <option value="kg" {{ old('weight_unit', 'kg') === 'kg' ? 'selected' : '' }}>Kilograms (kg)</option>
                                    <option value="lbs" {{ old('weight_unit') === 'lbs' ? 'selected' : '' }}>Pounds (lbs)</option>
                                </select>
                                @error('weight_unit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="package_type" class="form-label">Package Type</label>
                                <input type="text" class="form-control @error('package_type') is-invalid @enderror" 
                                       id="package_type" name="package_type" value="{{ old('package_type') }}" 
                                       placeholder="Box, Envelope, Pallet, etc.">
                                @error('package_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="package_description" class="form-label">Package Description</label>
                                <textarea class="form-control @error('package_description') is-invalid @enderror" 
                                          id="package_description" name="package_description" rows="3"
                                          placeholder="Describe the contents and any special handling requirements...">{{ old('package_description') }}</textarea>
                                @error('package_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Dimensions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-ruler me-2"></i>Dimensions (Optional)
                                </h6>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="length" class="form-label">Length</label>
                                <input type="number" class="form-control" id="length" name="length" 
                                       value="{{ old('length') }}" step="0.1" min="0">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="width" class="form-label">Width</label>
                                <input type="number" class="form-control" id="width" name="width" 
                                       value="{{ old('width') }}" step="0.1" min="0">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="height" class="form-label">Height</label>
                                <input type="number" class="form-control" id="height" name="height" 
                                       value="{{ old('height') }}" step="0.1" min="0">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="dimension_unit" class="form-label">Unit</label>
                                <select class="form-select" id="dimension_unit" name="dimension_unit">
                                    <option value="cm" {{ old('dimension_unit', 'cm') === 'cm' ? 'selected' : '' }}>Centimeters (cm)</option>
                                    <option value="in" {{ old('dimension_unit') === 'in' ? 'selected' : '' }}>Inches (in)</option>
                                </select>
                            </div>
                        </div>

                        <!-- Special Requirements -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Special Requirements
                                </h6>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input type="hidden" name="fragile" value="0">
                                    <input class="form-check-input" type="checkbox" id="fragile" name="fragile" value="1"
                                           {{ old('fragile') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="fragile">
                                        Fragile Items
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input type="hidden" name="hazardous" value="0">
                                    <input class="form-check-input" type="checkbox" id="hazardous" name="hazardous" value="1"
                                           {{ old('hazardous') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="hazardous">
                                        Hazardous Materials
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input type="hidden" name="insurance_required" value="0">
                                    <input class="form-check-input" type="checkbox" id="insurance_required" name="insurance_required" value="1"
                                           {{ old('insurance_required') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="insurance_required">
                                        Insurance Required
                                    </label>
                                </div>

                                <div class="form-check">
                                    <input type="hidden" name="signature_required" value="0">
                                    <input class="form-check-input" type="checkbox" id="signature_required" name="signature_required" value="1"
                                           {{ old('signature_required') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="signature_required">
                                        Signature Required
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="declared_value" class="form-label">Declared Value ($)</label>
                                <input type="number" class="form-control @error('declared_value') is-invalid @enderror" 
                                       id="declared_value" name="declared_value" value="{{ old('declared_value') }}" 
                                       step="0.01" min="0" placeholder="0.00">
                                @error('declared_value')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Delivery Options -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-calendar me-2"></i>Delivery Options
                                </h6>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="delivery_speed" class="form-label">Delivery Speed *</label>
                                <select class="form-select @error('delivery_speed') is-invalid @enderror" 
                                        id="delivery_speed" name="delivery_speed" required>
                                    <option value="standard" {{ old('delivery_speed', 'standard') === 'standard' ? 'selected' : '' }}>Standard</option>
                                    <option value="express" {{ old('delivery_speed') === 'express' ? 'selected' : '' }}>Express</option>
                                    <option value="overnight" {{ old('delivery_speed') === 'overnight' ? 'selected' : '' }}>Overnight</option>
                                    <option value="same_day" {{ old('delivery_speed') === 'same_day' ? 'selected' : '' }}>Same Day</option>
                                </select>
                                @error('delivery_speed')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="preferred_pickup_date" class="form-label">Preferred Pickup Date</label>
                                <input type="date" class="form-control @error('preferred_pickup_date') is-invalid @enderror" 
                                       id="preferred_pickup_date" name="preferred_pickup_date" value="{{ old('preferred_pickup_date') }}" 
                                       min="{{ date('Y-m-d') }}">
                                @error('preferred_pickup_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="required_delivery_date" class="form-label">Required Delivery Date</label>
                                <input type="date" class="form-control @error('required_delivery_date') is-invalid @enderror" 
                                       id="required_delivery_date" name="required_delivery_date" value="{{ old('required_delivery_date') }}" 
                                       min="{{ date('Y-m-d') }}">
                                @error('required_delivery_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Additional Notes -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-sticky-note me-2"></i>Additional Notes
                                </h6>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="customer_notes" class="form-label">Special Instructions or Notes</label>
                                <textarea class="form-control @error('customer_notes') is-invalid @enderror" 
                                          id="customer_notes" name="customer_notes" rows="4"
                                          placeholder="Any additional information or special instructions...">{{ old('customer_notes') }}</textarea>
                                @error('customer_notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('customer.quotes.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Quotes
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>Submit Quote Request
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-update delivery date based on pickup date
    document.getElementById('preferred_pickup_date').addEventListener('change', function() {
        const pickupDate = new Date(this.value);
        const deliveryDateInput = document.getElementById('required_delivery_date');

        if (pickupDate && !deliveryDateInput.value) {
            // Set delivery date to 3 days after pickup by default
            pickupDate.setDate(pickupDate.getDate() + 3);
            deliveryDateInput.value = pickupDate.toISOString().split('T')[0];
        }
    });

    // Validate delivery date is after pickup date
    document.getElementById('required_delivery_date').addEventListener('change', function() {
        const pickupDate = new Date(document.getElementById('preferred_pickup_date').value);
        const deliveryDate = new Date(this.value);

        if (pickupDate && deliveryDate && deliveryDate <= pickupDate) {
            alert('Delivery date must be after pickup date.');
            this.value = '';
        }
    });

    // Enhanced form submission with error handling
    document.getElementById('quoteForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;

        // Show loading state
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
        submitButton.disabled = true;

        // Clear previous errors
        document.querySelectorAll('.alert').forEach(alert => alert.remove());
        document.querySelectorAll('.is-invalid').forEach(field => field.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(feedback => feedback.remove());

        // Log form data for debugging
        console.log('Form data being sent:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ': ' + value);
        }

        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            }
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else if (response.status === 422) {
                return response.json().then(data => Promise.reject(data));
            } else {
                throw new Error('Network response was not ok');
            }
        })
        .then(data => {
            console.log('Success response:', data);

            // Show success message
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success alert-dismissible fade show';
            successAlert.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${data.message || 'Quote submitted successfully!'}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const cardBody = document.querySelector('.card-body');
            cardBody.insertBefore(successAlert, cardBody.firstChild);

            // Redirect if provided
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error response:', error);

            if (error.errors) {
                // Show validation errors
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger alert-dismissible fade show';
                errorAlert.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Please fix the following errors:</strong>
                    <ul class="mb-0 mt-2">
                        ${error.errors.map(err => `<li>${err}</li>`).join('')}
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const cardBody = document.querySelector('.card-body');
                cardBody.insertBefore(errorAlert, cardBody.firstChild);

                // Scroll to top to show errors
                errorAlert.scrollIntoView({ behavior: 'smooth' });
            } else {
                // Show generic error
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger alert-dismissible fade show';
                errorAlert.innerHTML = `
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    An error occurred while submitting your quote. Please try again.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                const cardBody = document.querySelector('.card-body');
                cardBody.insertBefore(errorAlert, cardBody.firstChild);
            }
        })
        .finally(() => {
            // Restore button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    });
</script>
@endpush
