@extends('layouts.frontend')

@section('title', 'Careers - Join Our Team')
@section('meta_description', 'Explore exciting career opportunities at ' . ($siteSettings['site_name'] ?? 'Atrix Logistics') . '. Join our team and grow your career in the logistics industry.')

@section('content')
    <!-- Hero Section with Breadcrumbs -->
    <x-page-hero
        title="Join Our Team"
        subtitle="Build your career with {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }} and be part of a dynamic team that's shaping the future of logistics."
        description="Explore exciting career opportunities and grow your career in the logistics industry."
        :breadcrumbs="[
            ['title' => 'Home', 'url' => route('home')],
            ['title' => 'Careers']
        ]"
        gradient="from-blue-600 to-green-600"
        :stats="[
            ['number' => $careers->total(), 'label' => 'Open Positions'],
            ['number' => $departments->count(), 'label' => 'Departments'],
            ['number' => 'Global', 'label' => 'Opportunities'],
            ['number' => '15+', 'label' => 'Years Experience']
        ]"
    />

    <!-- Filter Section -->
    @if($departments->count() > 0)
        <section class="py-8 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="flex flex-wrap gap-4 justify-center">
                    <a href="{{ route('careers.index') }}" 
                       class="px-4 py-2 rounded-full border {{ !request('department') ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:border-blue-600' }} transition-colors">
                        All Departments
                    </a>
                    @foreach($departments as $dept)
                        <a href="{{ route('careers.filter', ['department' => $dept]) }}" 
                           class="px-4 py-2 rounded-full border {{ request('department') === $dept ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:border-blue-600' }} transition-colors">
                            {{ $dept }}
                        </a>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Career Listings -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            @if($careers->count() > 0)
                <div class="grid gap-6 md:gap-8">
                    @foreach($careers as $career)
                        <div class="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                            <div class="p-6 md:p-8">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-3">
                                            <h3 class="text-xl md:text-2xl font-bold text-gray-900">
                                                {{ $career->title }}
                                            </h3>
                                            @if($career->is_featured)
                                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                    Featured
                                                </span>
                                            @endif
                                        </div>
                                        
                                        <div class="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
                                            @if($career->department)
                                                <div class="flex items-center gap-1">
                                                    <i class="fas fa-building text-gray-400"></i>
                                                    {{ $career->department }}
                                                </div>
                                            @endif
                                            <div class="flex items-center gap-1">
                                                <i class="fas fa-map-marker-alt text-gray-400"></i>
                                                {{ $career->location }}
                                            </div>
                                            <div class="flex items-center gap-1">
                                                <i class="fas fa-clock text-gray-400"></i>
                                                {{ ucfirst(str_replace('-', ' ', $career->employment_type)) }}
                                            </div>
                                            <div class="flex items-center gap-1">
                                                <i class="fas fa-chart-line text-gray-400"></i>
                                                {{ ucfirst($career->experience_level) }} Level
                                            </div>
                                        </div>

                                        <p class="text-gray-700 mb-4 line-clamp-3">
                                            {{ Str::limit(strip_tags($career->description), 200) }}
                                        </p>

                                        @if($career->required_skills && count($career->required_skills) > 0)
                                            <div class="flex flex-wrap gap-2 mb-4">
                                                @foreach(array_slice($career->required_skills, 0, 5) as $skill)
                                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                                        {{ $skill }}
                                                    </span>
                                                @endforeach
                                                @if(count($career->required_skills) > 5)
                                                    <span class="text-gray-500 text-xs">
                                                        +{{ count($career->required_skills) - 5 }} more
                                                    </span>
                                                @endif
                                            </div>
                                        @endif

                                        <div class="flex items-center justify-between">
                                            <div class="text-lg font-semibold text-green-600">
                                                {{ $career->formatted_salary }}
                                            </div>
                                            @if($career->application_deadline)
                                                <div class="text-sm text-gray-500">
                                                    Apply by {{ $career->application_deadline->format('M d, Y') }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="mt-6 md:mt-0 md:ml-8 flex flex-col gap-3">
                                        <a href="{{ route('careers.show', $career->slug) }}" 
                                           class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors text-center">
                                            View Details
                                        </a>
                                        @if(!$career->isApplicationDeadlinePassed())
                                            <a href="{{ route('careers.apply', $career->slug) }}" 
                                               class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors text-center">
                                                Apply Now
                                            </a>
                                        @else
                                            <span class="bg-gray-400 text-white px-6 py-3 rounded-lg font-medium text-center cursor-not-allowed">
                                                Applications Closed
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    {{ $careers->links() }}
                </div>
            @else
                <div class="text-center py-16">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-briefcase text-6xl text-gray-300 mb-6"></i>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">No Open Positions</h3>
                        <p class="text-gray-600 mb-6">
                            @if(request('department'))
                                No positions are currently available in the {{ request('department') }} department.
                            @else
                                We don't have any open positions at the moment, but we're always looking for talented individuals.
                            @endif
                        </p>
                        <div class="space-y-3">
                            @if(request('department'))
                                <a href="{{ route('careers.index') }}" 
                                   class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    View All Positions
                                </a>
                            @endif
                            <div>
                                <a href="{{ route('contact') }}" 
                                   class="inline-block bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    Contact Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Why Join Us Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Join {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}?</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    We offer more than just a job - we provide a platform for growth, innovation, and making a real impact in the logistics industry.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-line text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Career Growth</h3>
                    <p class="text-gray-600">Continuous learning opportunities and clear career progression paths.</p>
                </div>

                <div class="text-center">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Great Team</h3>
                    <p class="text-gray-600">Work with passionate professionals who support each other's success.</p>
                </div>

                <div class="text-center">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-balance-scale text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Work-Life Balance</h3>
                    <p class="text-gray-600">Flexible working arrangements and comprehensive benefits package.</p>
                </div>

                <div class="text-center">
                    <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-lightbulb text-2xl text-yellow-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Innovation</h3>
                    <p class="text-gray-600">Be part of cutting-edge logistics solutions and technology advancement.</p>
                </div>

                <div class="text-center">
                    <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Global Impact</h3>
                    <p class="text-gray-600">Make a difference in global trade and supply chain management.</p>
                </div>

                <div class="text-center">
                    <div class="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-award text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Recognition</h3>
                    <p class="text-gray-600">Your contributions are valued and recognized through various programs.</p>
                </div>
            </div>
        </div>
    </section>
@endsection
