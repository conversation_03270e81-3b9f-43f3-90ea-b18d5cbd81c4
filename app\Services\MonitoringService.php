<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MonitoringService
{
    /**
     * Perform comprehensive health check
     */
    public function healthCheck(): array
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'storage' => $this->checkStorage(),
            'queue' => $this->checkQueue(),
            'mail' => $this->checkMail(),
            'memory' => $this->checkMemoryUsage(),
            'disk_space' => $this->checkDiskSpace(),
        ];

        $overall = collect($checks)->every(fn($check) => $check['status'] === 'ok');

        return [
            'status' => $overall ? 'ok' : 'error',
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
            'response_time' => $this->getResponseTime(),
        ];
    }

    /**
     * Check database connectivity
     */
    private function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'ok',
                'message' => 'Database connection successful',
                'response_time_ms' => $responseTime,
            ];
        } catch (\Exception $e) {
            Log::error('Database health check failed', ['error' => $e->getMessage()]);
            
            return [
                'status' => 'error',
                'message' => 'Database connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache functionality
     */
    private function checkCache(): array
    {
        try {
            $key = 'health_check_' . time();
            $value = 'test_value';
            
            Cache::put($key, $value, 60);
            $retrieved = Cache::get($key);
            Cache::forget($key);

            if ($retrieved === $value) {
                return [
                    'status' => 'ok',
                    'message' => 'Cache is working properly',
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'Cache value mismatch',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Cache health check failed', ['error' => $e->getMessage()]);
            
            return [
                'status' => 'error',
                'message' => 'Cache check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage accessibility
     */
    private function checkStorage(): array
    {
        try {
            $testFile = 'health_check_' . time() . '.txt';
            $testContent = 'Health check test';
            
            Storage::disk('public')->put($testFile, $testContent);
            $retrieved = Storage::disk('public')->get($testFile);
            Storage::disk('public')->delete($testFile);

            if ($retrieved === $testContent) {
                return [
                    'status' => 'ok',
                    'message' => 'Storage is accessible',
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => 'Storage content mismatch',
                ];
            }
        } catch (\Exception $e) {
            Log::error('Storage health check failed', ['error' => $e->getMessage()]);
            
            return [
                'status' => 'error',
                'message' => 'Storage check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue status
     */
    private function checkQueue(): array
    {
        try {
            // Check if queue workers are running (simplified check)
            $queueSize = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            $status = 'ok';
            $message = 'Queue is operational';

            if ($queueSize > 1000) {
                $status = 'warning';
                $message = 'Queue has many pending jobs';
            }

            if ($failedJobs > 100) {
                $status = 'error';
                $message = 'Queue has many failed jobs';
            }

            return [
                'status' => $status,
                'message' => $message,
                'pending_jobs' => $queueSize,
                'failed_jobs' => $failedJobs,
            ];
        } catch (\Exception $e) {
            Log::error('Queue health check failed', ['error' => $e->getMessage()]);
            
            return [
                'status' => 'error',
                'message' => 'Queue check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check mail configuration
     */
    private function checkMail(): array
    {
        try {
            $mailer = config('mail.default');
            $host = config("mail.mailers.{$mailer}.host");
            
            if (empty($host)) {
                return [
                    'status' => 'warning',
                    'message' => 'Mail configuration incomplete',
                ];
            }

            return [
                'status' => 'ok',
                'message' => 'Mail configuration appears valid',
                'mailer' => $mailer,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Mail check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check memory usage
     */
    private function checkMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryPercent = ($memoryUsage / $memoryLimit) * 100;

        $status = 'ok';
        $message = 'Memory usage is normal';

        if ($memoryPercent > 80) {
            $status = 'warning';
            $message = 'High memory usage detected';
        }

        if ($memoryPercent > 95) {
            $status = 'error';
            $message = 'Critical memory usage';
        }

        return [
            'status' => $status,
            'message' => $message,
            'memory_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
            'memory_limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'memory_percent' => round($memoryPercent, 2),
        ];
    }

    /**
     * Check disk space
     */
    private function checkDiskSpace(): array
    {
        $path = storage_path();
        $freeBytes = disk_free_space($path);
        $totalBytes = disk_total_space($path);
        $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;

        $status = 'ok';
        $message = 'Disk space is sufficient';

        if ($usedPercent > 80) {
            $status = 'warning';
            $message = 'Disk space is running low';
        }

        if ($usedPercent > 95) {
            $status = 'error';
            $message = 'Critical disk space shortage';
        }

        return [
            'status' => $status,
            'message' => $message,
            'free_space_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
            'total_space_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
            'used_percent' => round($usedPercent, 2),
        ];
    }

    /**
     * Get application response time
     */
    private function getResponseTime(): float
    {
        return defined('LARAVEL_START') ? round((microtime(true) - LARAVEL_START) * 1000, 2) : 0;
    }

    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Log performance metrics
     */
    public function logPerformanceMetrics(): void
    {
        $metrics = [
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'execution_time' => $this->getResponseTime(),
            'timestamp' => now()->toISOString(),
        ];

        Log::channel('performance')->info('Performance metrics', $metrics);
    }

    /**
     * Get system information
     */
    public function getSystemInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'operating_system' => PHP_OS,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => config('app.timezone'),
            'environment' => app()->environment(),
        ];
    }
}
