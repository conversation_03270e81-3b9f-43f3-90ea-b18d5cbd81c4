<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Quote;
use App\Models\Parcel;
use App\Services\DocumentGenerationService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;

class DocumentController extends Controller
{
    protected DocumentGenerationService $documentService;

    public function __construct(DocumentGenerationService $documentService)
    {
        $this->documentService = $documentService;
    }

    /**
     * Generate and download invoice PDF
     */
    public function generateInvoice(Order $order)
    {
        try {
            $pdfPath = $this->documentService->generateInvoice($order);
            
            if (!$pdfPath || !Storage::disk('public')->exists($pdfPath)) {
                return response()->json(['error' => 'Failed to generate invoice'], 500);
            }

            $fileName = "invoice_{$order->order_number}.pdf";
            
            return response()->download(
                Storage::disk('public')->path($pdfPath),
                $fileName,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
                ]
            );
        } catch (\Exception $e) {
            logger()->error('Invoice generation failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json(['error' => 'Failed to generate invoice'], 500);
        }
    }

    /**
     * Generate and download quote PDF
     */
    public function generateQuote(Quote $quote)
    {
        try {
            $pdfPath = $this->documentService->generateQuote($quote);
            
            if (!$pdfPath || !Storage::disk('public')->exists($pdfPath)) {
                return response()->json(['error' => 'Failed to generate quote'], 500);
            }

            $fileName = "quote_{$quote->quote_number}.pdf";
            
            return response()->download(
                Storage::disk('public')->path($pdfPath),
                $fileName,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
                ]
            );
        } catch (\Exception $e) {
            logger()->error('Quote generation failed', [
                'quote_id' => $quote->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json(['error' => 'Failed to generate quote'], 500);
        }
    }

    /**
     * Generate and download waybill PDF
     */
    public function generateWaybill(Parcel $parcel)
    {
        try {
            $pdfPath = $this->documentService->generateWaybill($parcel);
            
            if (!$pdfPath || !Storage::disk('public')->exists($pdfPath)) {
                return response()->json(['error' => 'Failed to generate waybill'], 500);
            }

            $fileName = "waybill_{$parcel->tracking_number}.pdf";
            
            return response()->download(
                Storage::disk('public')->path($pdfPath),
                $fileName,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
                ]
            );
        } catch (\Exception $e) {
            logger()->error('Waybill generation failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json(['error' => 'Failed to generate waybill'], 500);
        }
    }

    /**
     * Generate and download shipping label PDF
     */
    public function generateShippingLabel(Parcel $parcel)
    {
        try {
            $pdfPath = $this->documentService->generateShippingLabel($parcel);
            
            if (!$pdfPath || !Storage::disk('public')->exists($pdfPath)) {
                return response()->json(['error' => 'Failed to generate shipping label'], 500);
            }

            $fileName = "shipping_label_{$parcel->tracking_number}.pdf";
            
            return response()->download(
                Storage::disk('public')->path($pdfPath),
                $fileName,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
                ]
            );
        } catch (\Exception $e) {
            logger()->error('Shipping label generation failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json(['error' => 'Failed to generate shipping label'], 500);
        }
    }

    /**
     * Generate and download delivery receipt PDF
     */
    public function generateDeliveryReceipt(Parcel $parcel)
    {
        try {
            $pdfPath = $this->documentService->generateDeliveryReceipt($parcel);
            
            if (!$pdfPath || !Storage::disk('public')->exists($pdfPath)) {
                return response()->json(['error' => 'Failed to generate delivery receipt'], 500);
            }

            $fileName = "delivery_receipt_{$parcel->tracking_number}.pdf";
            
            return response()->download(
                Storage::disk('public')->path($pdfPath),
                $fileName,
                [
                    'Content-Type' => 'application/pdf',
                    'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
                ]
            );
        } catch (\Exception $e) {
            logger()->error('Delivery receipt generation failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage()
            ]);
            
            return response()->json(['error' => 'Failed to generate delivery receipt'], 500);
        }
    }

    /**
     * Preview document in browser (for testing)
     */
    public function previewInvoice(Order $order)
    {
        try {
            return $this->documentService->previewInvoice($order);
        } catch (\Exception $e) {
            logger()->error('Invoice preview failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            
            return response('Failed to preview invoice', 500);
        }
    }

    /**
     * Preview quote in browser (for testing)
     */
    public function previewQuote(Quote $quote)
    {
        try {
            return $this->documentService->previewQuote($quote);
        } catch (\Exception $e) {
            logger()->error('Quote preview failed', [
                'quote_id' => $quote->id,
                'error' => $e->getMessage()
            ]);
            
            return response('Failed to preview quote', 500);
        }
    }

    /**
     * Preview waybill in browser (for testing)
     */
    public function previewWaybill(Parcel $parcel)
    {
        try {
            return $this->documentService->previewWaybill($parcel);
        } catch (\Exception $e) {
            logger()->error('Waybill preview failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage()
            ]);
            
            return response('Failed to preview waybill', 500);
        }
    }

    /**
     * Bulk document generation
     */
    public function bulkGenerate(Request $request)
    {
        $request->validate([
            'type' => 'required|in:invoice,quote,waybill,shipping_label,delivery_receipt',
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|integer|min:1'
        ]);

        try {
            $type = $request->type;
            $ids = $request->ids;
            $zipPath = $this->documentService->generateBulkDocuments($type, $ids);
            
            if (!$zipPath || !Storage::disk('public')->exists($zipPath)) {
                return response()->json(['error' => 'Failed to generate bulk documents'], 500);
            }

            $fileName = "bulk_{$type}_" . date('Y-m-d_H-i-s') . '.zip';
            
            return response()->download(
                Storage::disk('public')->path($zipPath),
                $fileName,
                [
                    'Content-Type' => 'application/zip',
                    'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
                ]
            )->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            logger()->error('Bulk document generation failed', [
                'type' => $request->type,
                'ids' => $request->ids,
                'error' => $e->getMessage()
            ]);
            
            return response()->json(['error' => 'Failed to generate bulk documents'], 500);
        }
    }
}
