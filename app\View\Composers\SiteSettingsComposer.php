<?php

namespace App\View\Composers;

use App\Models\SiteSetting;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;

class SiteSettingsComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $siteSettings = $this->getSiteSettings();
        $view->with('siteSettings', $siteSettings);
    }

    /**
     * Get site settings with caching
     */
    private function getSiteSettings(): array
    {
        // In testing environment, don't cache and provide fallbacks immediately
        if (app()->environment('testing')) {
            try {
                return SiteSetting::pluck('value', 'key_name')->toArray();
            } catch (\Exception $e) {
                return $this->getFallbackSettings();
            }
        }

        return Cache::remember('global_site_settings', 3600, function () {
            try {
                return SiteSetting::pluck('value', 'key_name')->toArray();
            } catch (\Exception $e) {
                return $this->getFallbackSettings();
            }
        });
    }

    /**
     * Get fallback settings when database is not available
     */
    private function getFallbackSettings(): array
    {
        return [
            'site_name' => 'Atrix Logistics',
            'site_title' => 'Atrix Logistics - Professional Shipping Solutions',
            'site_tagline' => 'We ship anything, anywhere, anytime',
            'site_logo' => 'assets/images/logo.png',
            'site_favicon' => 'assets/images/favicon.ico',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+****************',
            'url_structure_type' => 'subdirectory',
            'default_locale' => 'en-US',
            'hide_default_locale_in_url' => true,
            'supported_locales' => json_encode([
                'en-US' => ['name' => 'English (United States)', 'url' => '', 'enabled' => true]
            ]),
        ];
    }
}
