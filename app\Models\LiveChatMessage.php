<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LiveChatMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'sender_type',
        'staff_id',
        'message',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    /**
     * Get the session this message belongs to
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(LiveChatSession::class, 'session_id');
    }

    /**
     * Get the staff member who sent this message (if applicable)
     */
    public function staff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'staff_id');
    }

    /**
     * Check if message was sent by visitor
     */
    public function isFromVisitor(): bool
    {
        return $this->sender_type === 'visitor';
    }

    /**
     * Check if message was sent by staff
     */
    public function isFromStaff(): bool
    {
        return $this->sender_type === 'staff';
    }

    /**
     * Check if message is a system message
     */
    public function isSystemMessage(): bool
    {
        return $this->sender_type === 'system';
    }

    /**
     * Mark message as read
     */
    public function markAsRead(): void
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    /**
     * Get sender name for display
     */
    public function getSenderNameAttribute(): string
    {
        if ($this->isFromStaff() && $this->staff) {
            return $this->staff->name;
        }
        
        return $this->session->visitor_name ?? 'Visitor';
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for visitor messages
     */
    public function scopeFromVisitor($query)
    {
        return $query->where('sender_type', 'visitor');
    }

    /**
     * Scope for staff messages
     */
    public function scopeFromStaff($query)
    {
        return $query->where('sender_type', 'staff');
    }

    /**
     * Scope for system messages
     */
    public function scopeSystemMessages($query)
    {
        return $query->where('sender_type', 'system');
    }
}
