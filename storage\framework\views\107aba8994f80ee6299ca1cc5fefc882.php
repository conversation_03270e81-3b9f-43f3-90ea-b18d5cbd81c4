<?php $__env->startSection('title', 'Team Members'); ?>
<?php $__env->startSection('page-title', 'Team Members Management'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.cms.team.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Team Member
        </a>
        <button type="button" class="btn btn-outline-info" onclick="toggleSortMode()">
            <i class="fas fa-sort me-1"></i> Reorder
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                Team Members (<?php echo e($teamMembers->total()); ?> total)
            </h6>
            <div class="text-muted small">
                Showing <?php echo e($teamMembers->firstItem() ?? 0); ?> to <?php echo e($teamMembers->lastItem() ?? 0); ?> of <?php echo e($teamMembers->total()); ?> results
            </div>
        </div>
        <div class="card-body">
            <?php if($teamMembers->count() > 0): ?>
                <div id="sortable-container">
                    <div class="row" id="team-grid">
                        <?php $__currentLoopData = $teamMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-4 col-md-6 mb-4" data-id="<?php echo e($member->id); ?>">
                                <div class="card team-member-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-<?php echo e($member->is_active ? 'success' : 'secondary'); ?> me-2">
                                                <?php echo e($member->is_active ? 'Active' : 'Inactive'); ?>

                                            </span>
                                            <small class="text-muted">Order: <?php echo e($member->sort_order); ?></small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                    data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="<?php echo e(route('admin.cms.team.show', $member)); ?>">
                                                    <i class="fas fa-eye me-2"></i> View
                                                </a></li>
                                                <li><a class="dropdown-item" href="<?php echo e(route('admin.cms.team.edit', $member)); ?>">
                                                    <i class="fas fa-edit me-2"></i> Edit
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" 
                                                       onclick="toggleStatus('<?php echo e($member->id); ?>', '<?php echo e($member->name); ?>', <?php echo e($member->is_active ? 'false' : 'true'); ?>)">
                                                    <i class="fas fa-<?php echo e($member->is_active ? 'eye-slash' : 'eye'); ?> me-2"></i> 
                                                    <?php echo e($member->is_active ? 'Deactivate' : 'Activate'); ?>

                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" 
                                                       onclick="confirmDelete('<?php echo e($member->name); ?>', '<?php echo e(route('admin.cms.team.destroy', $member)); ?>')">
                                                    <i class="fas fa-trash me-2"></i> Delete
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <?php if($member->photo): ?>
                                                <img src="<?php echo e(Storage::url($member->photo)); ?>" 
                                                     alt="<?php echo e($member->name); ?>" 
                                                     class="rounded-circle img-thumbnail" 
                                                     style="width: 100px; height: 100px; object-fit: cover;">
                                            <?php else: ?>
                                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto" 
                                                     style="width: 100px; height: 100px;">
                                                    <i class="fas fa-user fa-2x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <h6 class="card-title mb-1"><?php echo e($member->name); ?></h6>
                                        <p class="text-muted small mb-2"><?php echo e($member->position); ?></p>
                                        
                                        <?php if($member->bio): ?>
                                            <p class="card-text small"><?php echo e(Str::limit($member->bio, 100)); ?></p>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex justify-content-center gap-2 mt-3">
                                            <?php if($member->email): ?>
                                                <a href="mailto:<?php echo e($member->email); ?>" class="btn btn-sm btn-outline-primary" title="Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($member->phone): ?>
                                                <a href="tel:<?php echo e($member->phone); ?>" class="btn btn-sm btn-outline-success" title="Phone">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($member->linkedin_url): ?>
                                                <a href="<?php echo e($member->linkedin_url); ?>" target="_blank" class="btn btn-sm btn-outline-info" title="LinkedIn">
                                                    <i class="fab fa-linkedin"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($member->twitter_url): ?>
                                                <a href="<?php echo e($member->twitter_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary" title="Twitter">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($member->facebook_url): ?>
                                                <a href="<?php echo e($member->facebook_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary" title="Facebook">
                                                    <i class="fab fa-facebook"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="card-footer text-center">
                                        <small class="text-muted">
                                            Added <?php echo e($member->created_at->format('M d, Y')); ?>

                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    <?php echo e($teamMembers->links('pagination.admin')); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">No Team Members Found</h5>
                    <p class="text-muted">Get started by adding your first team member.</p>
                    <a href="<?php echo e(route('admin.cms.team.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add First Team Member
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete team member <strong id="deleteMemberName"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">Delete Team Member</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Sort Mode Instructions -->
    <div id="sortInstructions" class="alert alert-info" style="display: none;">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Sort Mode Active:</strong> Drag and drop team member cards to reorder them. Click "Save Order" when finished.
        <button type="button" class="btn btn-sm btn-success ms-3" onclick="saveOrder()">
            <i class="fas fa-save me-1"></i> Save Order
        </button>
        <button type="button" class="btn btn-sm btn-secondary ms-2" onclick="cancelSort()">
            <i class="fas fa-times me-1"></i> Cancel
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .team-member-card {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    .team-member-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    .sortable-mode .team-member-card {
        cursor: move;
        border-color: #007bff;
    }
    .sortable-mode .team-member-card:hover {
        border-color: #0056b3;
    }
    .ui-sortable-helper {
        transform: rotate(5deg);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.3);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
<script>
    let sortMode = false;
    let originalOrder = [];

    function confirmDelete(memberName, deleteUrl) {
        document.getElementById('deleteMemberName').textContent = memberName;
        document.getElementById('deleteForm').action = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus(memberId, memberName, newStatus) {
        if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'activate' : 'deactivate'} ${memberName}?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/cms/team/${memberId}/toggle-status`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            document.body.appendChild(form);
            form.submit();
        }
    }

    function toggleSortMode() {
        sortMode = !sortMode;
        const container = document.getElementById('sortable-container');
        const instructions = document.getElementById('sortInstructions');
        
        if (sortMode) {
            // Store original order
            originalOrder = Array.from(document.querySelectorAll('[data-id]')).map(el => el.dataset.id);
            
            // Enable sorting
            container.classList.add('sortable-mode');
            instructions.style.display = 'block';
            
            // Initialize jQuery UI sortable
            $('#team-grid').sortable({
                items: '.col-lg-4',
                placeholder: 'ui-state-highlight',
                helper: 'clone',
                opacity: 0.8
            });
        } else {
            cancelSort();
        }
    }

    function saveOrder() {
        const newOrder = Array.from(document.querySelectorAll('[data-id]')).map(el => el.dataset.id);
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/cms/team/update-order';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);
        
        newOrder.forEach((id, index) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `orders[${index}]`;
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }

    function cancelSort() {
        sortMode = false;
        const container = document.getElementById('sortable-container');
        const instructions = document.getElementById('sortInstructions');
        
        container.classList.remove('sortable-mode');
        instructions.style.display = 'none';
        
        // Destroy sortable
        if ($('#team-grid').hasClass('ui-sortable')) {
            $('#team-grid').sortable('destroy');
        }
        
        // Restore original order if needed
        if (originalOrder.length > 0) {
            const grid = document.getElementById('team-grid');
            const items = Array.from(grid.children);
            
            originalOrder.forEach(id => {
                const item = items.find(el => el.dataset.id === id);
                if (item) {
                    grid.appendChild(item);
                }
            });
        }
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/team/index.blade.php ENDPATH**/ ?>