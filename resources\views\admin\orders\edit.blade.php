@extends('layouts.admin')

@section('title', 'Edit Order')
@section('page-title', 'Edit Order: ' . $order->order_number)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Orders
        </a>
        <a href="{{ route('admin.orders.show', $order) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Details
        </a>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <form method="POST" action="{{ route('admin.orders.update', $order) }}">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Order Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Order Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Order Status *</label>
                                <select class="form-select @error('status') is-invalid @enderror" 
                                        id="status" name="status" required>
                                    <option value="pending" {{ old('status', $order->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="processing" {{ old('status', $order->status) == 'processing' ? 'selected' : '' }}>Processing</option>
                                    <option value="shipped" {{ old('status', $order->status) == 'shipped' ? 'selected' : '' }}>Shipped</option>
                                    <option value="delivered" {{ old('status', $order->status) == 'delivered' ? 'selected' : '' }}>Delivered</option>
                                    <option value="cancelled" {{ old('status', $order->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_status" class="form-label">Payment Status *</label>
                                <select class="form-select @error('payment_status') is-invalid @enderror" 
                                        id="payment_status" name="payment_status" required>
                                    <option value="pending" {{ old('payment_status', $order->payment_status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="paid" {{ old('payment_status', $order->payment_status) == 'paid' ? 'selected' : '' }}>Paid</option>
                                    <option value="failed" {{ old('payment_status', $order->payment_status) == 'failed' ? 'selected' : '' }}>Failed</option>
                                    <option value="refunded" {{ old('payment_status', $order->payment_status) == 'refunded' ? 'selected' : '' }}>Refunded</option>
                                </select>
                                @error('payment_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select @error('payment_method') is-invalid @enderror" 
                                        id="payment_method" name="payment_method">
                                    <option value="">-- Select Payment Method --</option>
                                    <option value="manual" {{ old('payment_method', $order->payment_method) == 'manual' ? 'selected' : '' }}>Manual Payment</option>
                                    <option value="paypal" {{ old('payment_method', $order->payment_method) == 'paypal' ? 'selected' : '' }}>PayPal</option>
                                    <option value="stripe" {{ old('payment_method', $order->payment_method) == 'stripe' ? 'selected' : '' }}>Credit/Debit Card (Stripe)</option>
                                </select>
                                @error('payment_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="payment_reference" class="form-label">Payment Reference</label>
                                <input type="text" class="form-control @error('payment_reference') is-invalid @enderror" 
                                       id="payment_reference" name="payment_reference" 
                                       value="{{ old('payment_reference', $order->payment_reference) }}"
                                       placeholder="Transaction ID, Reference Number, etc.">
                                @error('payment_reference')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-truck me-2"></i>Shipping Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="shipping_method" class="form-label">Shipping Method</label>
                                <input type="text" class="form-control @error('shipping_method') is-invalid @enderror" 
                                       id="shipping_method" name="shipping_method" 
                                       value="{{ old('shipping_method', $order->shipping_method) }}"
                                       placeholder="e.g., Standard Shipping, Express Delivery">
                                @error('shipping_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tracking_number" class="form-label">Tracking Number</label>
                                <input type="text" class="form-control @error('tracking_number') is-invalid @enderror" 
                                       id="tracking_number" name="tracking_number" 
                                       value="{{ old('tracking_number', $order->tracking_number) }}"
                                       placeholder="Enter tracking number">
                                @error('tracking_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="shipped_at" class="form-label">Shipped Date</label>
                                <input type="datetime-local" class="form-control @error('shipped_at') is-invalid @enderror" 
                                       id="shipped_at" name="shipped_at" 
                                       value="{{ old('shipped_at', $order->shipped_at ? $order->shipped_at->format('Y-m-d\TH:i') : '') }}">
                                @error('shipped_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="delivered_at" class="form-label">Delivered Date</label>
                                <input type="datetime-local" class="form-control @error('delivered_at') is-invalid @enderror" 
                                       id="delivered_at" name="delivered_at" 
                                       value="{{ old('delivered_at', $order->delivered_at ? $order->delivered_at->format('Y-m-d\TH:i') : '') }}">
                                @error('delivered_at')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Notes -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Order Notes</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="notes" class="form-label">Customer Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="4" 
                                          placeholder="Notes from customer...">{{ old('notes', $order->notes) }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="admin_notes" class="form-label">Admin Notes</label>
                                <textarea class="form-control @error('admin_notes') is-invalid @enderror" 
                                          id="admin_notes" name="admin_notes" rows="4" 
                                          placeholder="Internal notes for admin use...">{{ old('admin_notes', $order->admin_notes) }}</textarea>
                                @error('admin_notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Order Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Order Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Order Number</label>
                            <p class="mb-0"><code>{{ $order->order_number }}</code></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Customer</label>
                            <p class="mb-0">
                                <strong>{{ $order->customer_name }}</strong><br>
                                <a href="mailto:{{ $order->customer_email }}">{{ $order->customer_email }}</a>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Order Date</label>
                            <p class="mb-0">{{ $order->created_at->format('M d, Y h:i A') }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">Total Amount</label>
                            <p class="mb-0 h5 text-success">@currency($order->total_amount)</p>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-box me-2"></i>Order Items ({{ $order->items->count() }})</h6>
                    </div>
                    <div class="card-body">
                        @foreach($order->items as $item)
                            <div class="d-flex align-items-center mb-3 pb-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                @if($item->product_image)
                                    <img src="{{ Storage::url($item->product_image) }}" 
                                         alt="{{ $item->product_name }}" 
                                         class="me-3" 
                                         style="width: 40px; height: 40px; object-fit: cover;">
                                @else
                                    <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                @endif
                                <div class="flex-grow-1">
                                    <div class="fw-bold">{{ $item->product_name }}</div>
                                    <small class="text-muted">Qty: {{ $item->quantity }} × @currency($item->unit_price)</small>
                                </div>
                                <div class="text-end">
                                    <strong>@currency($item->total_price)</strong>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Update Order
                            </button>
                            <a href="{{ route('admin.orders.show', $order) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-update shipped_at when status changes to shipped
    const statusSelect = document.getElementById('status');
    const shippedAtInput = document.getElementById('shipped_at');
    
    statusSelect.addEventListener('change', function() {
        if (this.value === 'shipped' && !shippedAtInput.value) {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            shippedAtInput.value = localDateTime;
        }
    });

    // Auto-update delivered_at when status changes to delivered
    const deliveredAtInput = document.getElementById('delivered_at');
    
    statusSelect.addEventListener('change', function() {
        if (this.value === 'delivered' && !deliveredAtInput.value) {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            deliveredAtInput.value = localDateTime;
        }
    });
});
</script>
@endpush
