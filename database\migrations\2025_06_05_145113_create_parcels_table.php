<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parcels', function (Blueprint $table) {
            $table->id();
            $table->string('tracking_number', 100)->unique();
            $table->foreignId('carrier_id')->constrained('carriers');
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();

            // Sender Information
            $table->string('sender_name');
            $table->string('sender_email')->nullable();
            $table->string('sender_phone', 20)->nullable();
            $table->text('sender_address');
            $table->string('sender_city', 100);
            $table->string('sender_state', 100);
            $table->string('sender_postal_code', 20);
            $table->string('sender_country', 100)->default('USA');

            // Recipient Information
            $table->string('recipient_name');
            $table->string('recipient_email')->nullable();
            $table->string('recipient_phone', 20)->nullable();
            $table->text('recipient_address');
            $table->string('recipient_city', 100);
            $table->string('recipient_state', 100);
            $table->string('recipient_postal_code', 20);
            $table->string('recipient_country', 100)->default('USA');

            // Parcel Details
            $table->text('description');
            $table->decimal('weight', 8, 2)->nullable(); // in kg
            $table->string('dimensions')->nullable(); // LxWxH in cm
            $table->decimal('declared_value', 10, 2)->nullable();
            $table->string('service_type', 100)->nullable(); // standard, express, overnight

            // Status and Tracking
            $table->enum('status', ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'returned'])->default('pending');
            $table->text('special_instructions')->nullable();
            $table->timestamp('shipped_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->date('estimated_delivery_date')->nullable();

            // Pricing
            $table->decimal('shipping_cost', 10, 2)->nullable();
            $table->decimal('insurance_cost', 10, 2)->nullable();
            $table->decimal('total_cost', 10, 2)->nullable();
            $table->boolean('is_paid')->default(false);

            $table->timestamps();

            // Indexes
            $table->index('tracking_number');
            $table->index(['carrier_id', 'status']);
            $table->index(['user_id', 'created_at']);
            $table->index('status');
            $table->index('estimated_delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parcels');
    }
};
