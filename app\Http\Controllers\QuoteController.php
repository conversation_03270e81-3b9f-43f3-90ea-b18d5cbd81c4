<?php

namespace App\Http\Controllers;

use App\Models\Quote;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class QuoteController extends Controller
{
    /**
     * Store a new quote request (public - no authentication required)
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
            'quote_type' => 'required|in:shipping,product',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'priority' => 'required|in:standard,urgent,express',
            'description' => 'nullable|string|max:5000',
            
            // Shipping quote fields
            'service_type' => 'required|in:domestic_shipping,international_shipping,express_delivery,freight_shipping,warehousing,custom_logistics,bulk_shipping,specialized_transport,product_inquiry',
            'origin_address' => 'required_if:quote_type,shipping|nullable|string|max:500',
            'origin_city' => 'required_if:quote_type,shipping|nullable|string|max:100',
            'origin_state' => 'nullable|string|max:100',
            'origin_postal_code' => 'nullable|string|max:20',
            'origin_country' => 'nullable|string|max:100',
            'destination_address' => 'required_if:quote_type,shipping|nullable|string|max:500',
            'destination_city' => 'required_if:quote_type,shipping|nullable|string|max:100',
            'destination_state' => 'nullable|string|max:100',
            'destination_postal_code' => 'nullable|string|max:20',
            'destination_country' => 'nullable|string|max:100',
            'package_count' => 'nullable|integer|min:1',
            'total_weight' => 'nullable|numeric|min:0',
            'weight_unit' => 'nullable|in:kg,lbs',
            'declared_value' => 'nullable|numeric|min:0',
            'preferred_pickup_date' => 'nullable|date|after:today',
            'required_delivery_date' => 'nullable|date|after:preferred_pickup_date',
            'fragile' => 'nullable|boolean',
            'hazardous' => 'nullable|boolean',
            'insurance_required' => 'nullable|boolean',
            'signature_required' => 'nullable|boolean',
            
            // Product quote fields
            'products' => 'required_if:quote_type,product|nullable|json',
            'products_total' => 'required_if:quote_type,product|nullable|numeric|min:0',
            'product_requirements' => 'nullable|string|max:2000',

            // Track which tab the data came from
            'quote_source' => 'nullable|string|max:50',
        ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->wantsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => array_values($e->validator->errors()->all())
                ], 422);
            }
            throw $e;
        }

        // Set user_id if authenticated
        if (Auth::check()) {
            $validated['user_id'] = Auth::id();
        }

        // Handle empty description
        if (empty($validated['description'])) {
            if ($validated['quote_type'] === 'product') {
                $validated['description'] = 'Product inquiry - no additional description provided.';
            } else {
                $validated['description'] = 'Shipping quote request - no additional description provided.';
            }
        }

        // Set boolean values
        $validated['fragile'] = $request->has('fragile');
        $validated['hazardous'] = $request->has('hazardous');
        $validated['insurance_required'] = $request->has('insurance_required');
        $validated['signature_required'] = $request->has('signature_required');
        $validated['status'] = 'pending';

        // Handle product quote specific data
        if ($validated['quote_type'] === 'product') {
            $products = json_decode($validated['products'], true);
            
            // Validate products exist and update with current data
            $validatedProducts = [];
            $totalValue = 0;
            
            foreach ($products as $productData) {
                $product = Product::find($productData['product_id']);
                if ($product && $product->is_active) {
                    $quantity = max(1, intval($productData['quantity']));
                    $priceAtTime = $product->isOnSale() ? $product->sale_price : $product->price;
                    
                    $validatedProducts[] = [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'product_sku' => $product->sku,
                        'quantity' => $quantity,
                        'price_at_time' => $priceAtTime,
                        'total' => $priceAtTime * $quantity,
                        'notes' => $productData['notes'] ?? null,
                    ];
                    
                    $totalValue += $priceAtTime * $quantity;
                }
            }
            
            if (empty($validatedProducts)) {
                if ($request->wantsJson() || $request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['No valid products found in your quote request.']
                    ], 422);
                }
                return back()->withErrors(['products' => 'No valid products found in your quote request.']);
            }
            
            $validated['products'] = $validatedProducts;
            $validated['products_total'] = $totalValue;
            
            // Set service type for product quotes
            $validated['service_type'] = 'product_inquiry';
        }

        $quote = Quote::create($validated);

        // Send notification email (you can implement this later)
        // $this->sendQuoteNotification($quote);

        $message = 'Quote request submitted successfully! Quote #' . $quote->quote_number;

        if ($validated['quote_type'] === 'product') {
            $message .= ' for ' . count($validatedProducts) . ' product(s).';
        }

        $message .= ' We will contact you within 24 hours.';

        // Handle AJAX requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'quote_number' => $quote->quote_number
            ]);
        }

        return back()->with('success', $message);
    }

    /**
     * Show quote lookup form or quote details
     */
    public function show(Request $request)
    {
        // If no parameters, show lookup form
        if (!$request->has('quote_number') && !$request->has('email')) {
            return view('quotes.lookup');
        }

        $request->validate([
            'quote_number' => 'required|string',
            'email' => 'required|email',
        ]);

        $quote = Quote::where('quote_number', $request->quote_number)
                    ->where('customer_email', $request->email)
                    ->first();

        if (!$quote) {
            return back()->withErrors(['quote_number' => 'Quote not found or email does not match.']);
        }

        return view('quotes.show', compact('quote'));
    }

    /**
     * Accept a quote (public access)
     */
    public function accept(Request $request, Quote $quote)
    {
        $request->validate([
            'customer_email' => 'required|email',
        ]);

        // Verify email matches
        if ($quote->customer_email !== $request->customer_email) {
            return back()->withErrors(['customer_email' => 'Email does not match quote records.']);
        }

        // Only allow accepting if quote is quoted and not expired
        if (!$quote->canBeAccepted()) {
            return back()->withErrors(['quote' => 'This quote cannot be accepted.']);
        }

        $quote->update([
            'status' => 'accepted',
            'accepted_at' => now(),
        ]);

        return back()->with('success', 'Quote accepted successfully! We will contact you to proceed with the order.');
    }

    /**
     * Reject a quote (public access)
     */
    public function reject(Request $request, Quote $quote)
    {
        $request->validate([
            'customer_email' => 'required|email',
            'rejection_reason' => 'nullable|string|max:500',
        ]);

        // Verify email matches
        if ($quote->customer_email !== $request->customer_email) {
            return back()->withErrors(['customer_email' => 'Email does not match quote records.']);
        }

        // Only allow rejecting if quote can be rejected
        if (!$quote->canBeRejected()) {
            return back()->withErrors(['quote' => 'This quote cannot be rejected.']);
        }

        $quote->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'customer_notes' => $request->rejection_reason,
        ]);

        return back()->with('success', 'Quote rejected. Thank you for your feedback.');
    }

    /**
     * Send quote notification email
     */
    private function sendQuoteNotification(Quote $quote)
    {
        // Implement email notification logic here
        // You can use Laravel's Mail facade to send emails
    }
}
