<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{ $siteSettings['site_name'] ?? config('app.name') }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .content {
            padding: 40px 30px;
        }
        .welcome-message {
            font-size: 18px;
            margin-bottom: 25px;
            color: #2c3e50;
        }
        .credentials-box {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 25px 0;
            border-radius: 5px;
        }
        .credentials-box h3 {
            margin-top: 0;
            color: #667eea;
            font-size: 16px;
        }
        .credential-item {
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .credential-item:last-child {
            border-bottom: none;
        }
        .credential-label {
            font-weight: 600;
            color: #495057;
            display: inline-block;
            width: 80px;
        }
        .credential-value {
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            color: white;
        }
        .features {
            margin: 30px 0;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 25px 0;
        }
        .security-notice h4 {
            color: #856404;
            margin-top: 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        .role-badge {
            display: inline-block;
            background-color: {{ $user->role === 'admin' ? '#dc3545' : ($user->role === 'staff' ? '#ffc107' : '#007bff') }};
            color: {{ $user->role === 'staff' ? '#212529' : 'white' }};
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to {{ $siteSettings['site_name'] ?? config('app.name') }}</h1>
            <p>Your account has been created successfully!</p>
        </div>

        <div class="content">
            <div class="welcome-message">
                <strong>Hello {{ $user->name }},</strong>
            </div>

            <p>Welcome to {{ $siteSettings['site_name'] ?? config('app.name') }}! Your account has been created by our administrator and you now have access to our platform.</p>

            <div class="credentials-box">
                <h3>🔐 Your Login Credentials</h3>
                <div class="credential-item">
                    <span class="credential-label">Email:</span>
                    <span class="credential-value">{{ $user->email }}</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Password:</span>
                    <span class="credential-value">{{ $temporaryPassword }}</span>
                </div>
                <div class="credential-item">
                    <span class="credential-label">Role:</span>
                    <span class="role-badge">{{ ucfirst($user->role) }}</span>
                </div>
            </div>

            <div class="security-notice">
                <h4>🛡️ Security Notice</h4>
                <p><strong>Important:</strong> For your security, please change your password immediately after your first login. This temporary password should not be shared with anyone.</p>
            </div>

            <div style="text-align: center;">
                <a href="{{ $loginUrl }}" class="btn">Login to Your Account</a>
            </div>

            <div class="features">
                <h3>What you can do with your {{ ucfirst($user->role) }} account:</h3>
                
                @if($user->role === 'admin')
                    <div class="feature-item">
                        <div class="feature-icon">⚙️</div>
                        <div>
                            <strong>Full System Access</strong><br>
                            <small>Manage all users, settings, and system configuration</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">👥</div>
                        <div>
                            <strong>User Management</strong><br>
                            <small>Create, edit, and manage all user accounts</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <div>
                            <strong>Analytics & Reports</strong><br>
                            <small>Access comprehensive reports and analytics</small>
                        </div>
                    </div>
                @elseif($user->role === 'staff')
                    <div class="feature-item">
                        <div class="feature-icon">📦</div>
                        <div>
                            <strong>Parcel Management</strong><br>
                            <small>Create, track, and manage parcels and shipments</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🛒</div>
                        <div>
                            <strong>Order Processing</strong><br>
                            <small>Process and manage customer orders</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">💬</div>
                        <div>
                            <strong>Customer Support</strong><br>
                            <small>Handle customer inquiries and support tickets</small>
                        </div>
                    </div>
                @else
                    <div class="feature-item">
                        <div class="feature-icon">🛒</div>
                        <div>
                            <strong>Place Orders</strong><br>
                            <small>Create and manage your shipping orders</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📍</div>
                        <div>
                            <strong>Track Parcels</strong><br>
                            <small>Real-time tracking of your shipments</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">👤</div>
                        <div>
                            <strong>Manage Profile</strong><br>
                            <small>Update your personal information and preferences</small>
                        </div>
                    </div>
                @endif
            </div>

            <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

            <p>Best regards,<br>
            <strong>{{ $siteSettings['site_name'] ?? config('app.name') }} Team</strong></p>
        </div>

        <div class="footer">
            <p>This email was sent to {{ $user->email }} because an account was created for you.</p>
            <p>
                <a href="{{ $loginUrl }}">Login to Your Account</a> | 
                <a href="{{ $siteSettings['site_url'] ?? config('app.url') }}">Visit Our Website</a>
            </p>
            <p>&copy; {{ date('Y') }} {{ $siteSettings['site_name'] ?? config('app.name') }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
