@props([
    'title' => null,
    'description' => null,
    'keywords' => null,
    'image' => null,
    'url' => null,
    'type' => 'website',
    'robots' => null,
    'canonical' => null,
    'locale' => null
])

@php
    $seoService = app(\App\Services\SeoService::class);
    $siteSettings = cache()->remember('site_settings', 3600, function () {
        return \App\Models\SiteSetting::pluck('value', 'key_name')->toArray();
    });
    
    // Optimize title and description
    $optimizedTitle = $seoService->optimizeTitle(
        $title ?: ($siteSettings['site_name'] ?? config('app.name')),
        $siteSettings['site_name'] ?? config('app.name')
    );
    
    $optimizedDescription = $seoService->optimizeMetaDescription(
        $description ?: ($siteSettings['site_description'] ?? 'Professional logistics solutions')
    );
    
    // Generate Open Graph and Twitter Card tags
    $ogTags = $seoService->generateOpenGraphTags([
        'type' => $type,
        'title' => $optimizedTitle,
        'description' => $optimizedDescription,
        'image' => $image,
        'url' => $url ?: request()->url(),
        'locale' => $locale ?: app()->getLocale(),
    ]);
    
    $twitterTags = $seoService->generateTwitterCardTags([
        'title' => $optimizedTitle,
        'description' => $optimizedDescription,
        'image' => $image,
    ]);
    
    // Generate canonical URL
    $canonicalUrl = $canonical ?: $seoService->generateCanonicalUrl();
    
    // Generate robots tag
    $robotsTag = $robots ?: $seoService->generateRobotsTag();
@endphp

<!-- Basic Meta Tags -->
<title>{{ $optimizedTitle }}</title>
<meta name="description" content="{{ $optimizedDescription }}">
@if($keywords)
<meta name="keywords" content="{{ $keywords }}">
@endif
<meta name="robots" content="{{ $robotsTag }}">
<meta name="author" content="{{ $siteSettings['site_name'] ?? config('app.name') }}">

<!-- Canonical URL -->
<link rel="canonical" href="{{ $canonicalUrl }}">

<!-- Open Graph Meta Tags -->
@foreach($ogTags as $property => $content)
<meta property="{{ $property }}" content="{{ $content }}">
@endforeach

<!-- Twitter Card Meta Tags -->
@foreach($twitterTags as $name => $content)
<meta name="{{ $name }}" content="{{ $content }}">
@endforeach

<!-- Additional SEO Meta Tags -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="theme-color" content="{{ $siteSettings['theme_color'] ?? '#1f2937' }}">
<meta name="msapplication-TileColor" content="{{ $siteSettings['theme_color'] ?? '#1f2937' }}">

<!-- Favicon and Icons -->
<link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
<link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">
<link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicon-32x32.png') }}">
<link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicon-16x16.png') }}">
<link rel="manifest" href="{{ asset('site.webmanifest') }}">

<!-- Language and Locale -->
<meta http-equiv="content-language" content="{{ $locale ?: app()->getLocale() }}">

<!-- Security Headers -->
<meta http-equiv="X-Content-Type-Options" content="nosniff">
<meta http-equiv="X-Frame-Options" content="DENY">
<meta http-equiv="X-XSS-Protection" content="1; mode=block">
<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

<!-- DNS Prefetch for Performance -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
