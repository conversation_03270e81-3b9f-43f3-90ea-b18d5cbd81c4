<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quote_requests', function (Blueprint $table) {
            $table->id();
            $table->string('quote_number', 100)->unique();
            $table->string('name');
            $table->string('email');
            $table->string('phone', 20)->nullable();
            $table->string('company')->nullable();

            // Shipping Details
            $table->text('origin_address')->nullable();
            $table->text('destination_address')->nullable();
            $table->string('service_type', 100)->nullable();
            $table->decimal('estimated_weight', 8, 2)->nullable();
            $table->string('estimated_dimensions')->nullable();
            $table->date('preferred_delivery_date')->nullable();

            // Quote Details
            $table->text('message')->nullable();
            $table->text('special_requirements')->nullable();
            $table->decimal('total_estimated_value', 10, 2)->nullable();

            // Status and Processing
            $table->enum('status', ['pending', 'processing', 'quoted', 'accepted', 'declined', 'expired'])->default('pending');
            $table->decimal('quoted_amount', 10, 2)->nullable();
            $table->timestamp('quoted_at')->nullable();
            $table->foreignId('quoted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamp('expires_at')->nullable();

            // Tracking
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('source', 100)->default('website'); // website, product_page, modal, etc.

            $table->timestamps();

            // Indexes
            $table->index('quote_number');
            $table->index('email');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quote_requests');
    }
};
