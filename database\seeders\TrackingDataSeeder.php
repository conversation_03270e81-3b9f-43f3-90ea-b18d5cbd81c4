<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Parcel;
use App\Models\TrackingEvent;
use Carbon\Carbon;

class TrackingDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create first sample parcel
        $parcel1 = Parcel::create([
            'tracking_number' => 'TRK123456789',
            'status' => 'in_transit',
            'carrier_id' => 1,
            'user_id' => 3,
            'sender_name' => 'Atrix Logistics',
            'sender_address' => '123 Main St',
            'sender_city' => 'New York',
            'sender_state' => 'NY',
            'sender_postal_code' => '10001',
            'sender_country' => 'USA',
            'recipient_name' => 'John <PERSON>',
            'recipient_address' => '456 Oak Ave',
            'recipient_city' => 'Los Angeles',
            'recipient_state' => 'CA',
            'recipient_postal_code' => '90210',
            'recipient_country' => 'USA',
            'description' => 'Electronics package',
            'estimated_delivery_date' => Carbon::now()->addDays(3),
            'weight' => 5.2,
            'service_type' => 'standard',
        ]);

        // Create tracking events for first parcel
        TrackingEvent::create([
            'parcel_id' => $parcel1->id,
            'event_date' => Carbon::now()->subDays(2),
            'description' => 'Package received at sorting facility',
            'location' => 'New York, NY',
            'status' => 'pending',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel1->id,
            'event_date' => Carbon::now()->subDays(1),
            'description' => 'Package departed from origin facility',
            'location' => 'New York, NY',
            'status' => 'picked_up',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel1->id,
            'event_date' => Carbon::now()->subHours(12),
            'description' => 'Package arrived at regional hub',
            'location' => 'Chicago, IL',
            'status' => 'in_transit',
        ]);

        // Create second sample parcel
        $parcel2 = Parcel::create([
            'tracking_number' => 'TRK987654321',
            'status' => 'delivered',
            'carrier_id' => 1,
            'user_id' => 3,
            'sender_name' => 'Atrix Logistics',
            'sender_address' => '789 Harbor St',
            'sender_city' => 'Boston',
            'sender_state' => 'MA',
            'sender_postal_code' => '02101',
            'sender_country' => 'USA',
            'recipient_name' => 'Jane Smith',
            'recipient_address' => '321 Beach Blvd',
            'recipient_city' => 'Miami',
            'recipient_state' => 'FL',
            'recipient_postal_code' => '33101',
            'recipient_country' => 'USA',
            'description' => 'Clothing package',
            'estimated_delivery_date' => Carbon::now()->subDays(1),
            'weight' => 3.7,
            'service_type' => 'express',
            'delivered_at' => Carbon::now()->subDays(1),
        ]);

        // Create tracking events for second parcel
        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'event_date' => Carbon::now()->subDays(5),
            'description' => 'Package received at sorting facility',
            'location' => 'Boston, MA',
            'status' => 'pending',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'event_date' => Carbon::now()->subDays(4),
            'description' => 'Package departed from origin facility',
            'location' => 'Boston, MA',
            'status' => 'picked_up',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'event_date' => Carbon::now()->subDays(3),
            'description' => 'Package in transit to destination',
            'location' => 'Atlanta, GA',
            'status' => 'in_transit',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'event_date' => Carbon::now()->subDays(2),
            'description' => 'Package out for delivery',
            'location' => 'Miami, FL',
            'status' => 'out_for_delivery',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel2->id,
            'event_date' => Carbon::now()->subDays(1),
            'description' => 'Package delivered',
            'location' => 'Miami, FL',
            'status' => 'delivered',
        ]);

        // Create third sample parcel
        $parcel3 = Parcel::create([
            'tracking_number' => 'TRK555666777',
            'status' => 'out_for_delivery',
            'carrier_id' => 1,
            'user_id' => 3,
            'sender_name' => 'Atrix Logistics',
            'sender_address' => '555 Pine St',
            'sender_city' => 'Seattle',
            'sender_state' => 'WA',
            'sender_postal_code' => '98101',
            'sender_country' => 'USA',
            'recipient_name' => 'Bob Johnson',
            'recipient_address' => '777 Oak St',
            'recipient_city' => 'Portland',
            'recipient_state' => 'OR',
            'recipient_postal_code' => '97201',
            'recipient_country' => 'USA',
            'description' => 'Books package',
            'estimated_delivery_date' => Carbon::now()->addDays(1),
            'weight' => 2.1,
            'service_type' => 'overnight',
        ]);

        // Create tracking events for third parcel
        TrackingEvent::create([
            'parcel_id' => $parcel3->id,
            'event_date' => Carbon::now()->subDays(3),
            'description' => 'Package received at sorting facility',
            'location' => 'Seattle, WA',
            'status' => 'pending',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel3->id,
            'event_date' => Carbon::now()->subDays(2),
            'description' => 'Package departed from origin facility',
            'location' => 'Seattle, WA',
            'status' => 'picked_up',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel3->id,
            'event_date' => Carbon::now()->subDays(1),
            'description' => 'Package in transit to destination',
            'location' => 'Tacoma, WA',
            'status' => 'in_transit',
        ]);

        TrackingEvent::create([
            'parcel_id' => $parcel3->id,
            'event_date' => Carbon::now()->subHours(6),
            'description' => 'Package out for delivery',
            'location' => 'Portland, OR',
            'status' => 'out_for_delivery',
        ]);

        $this->command->info('Sample tracking data created successfully!');
    }
}
