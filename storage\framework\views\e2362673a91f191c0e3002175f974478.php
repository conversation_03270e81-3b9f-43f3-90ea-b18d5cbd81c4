<?php $__env->startSection('title', 'Job Application Details'); ?>
<?php $__env->startSection('page-title', 'Application: ' . $jobApplication->full_name); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('admin.cms.job-applications.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Applications
        </a>
        <?php if($jobApplication->resume_path): ?>
            <a href="<?php echo e($jobApplication->resume_url); ?>" target="_blank" class="btn btn-outline-success">
                <i class="fas fa-file-pdf me-1"></i> View Resume
            </a>
        <?php endif; ?>
        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8 mb-4">
                <!-- Personal Information -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>Full Name:</strong>
                                <p class="mb-0"><?php echo e($jobApplication->full_name); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>Email:</strong>
                                <p class="mb-0">
                                    <a href="mailto:<?php echo e($jobApplication->email); ?>"><?php echo e($jobApplication->email); ?></a>
                                </p>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>Phone:</strong>
                                <p class="mb-0">
                                    <?php if($jobApplication->phone): ?>
                                        <a href="tel:<?php echo e($jobApplication->phone); ?>"><?php echo e($jobApplication->phone); ?></a>
                                    <?php else: ?>
                                        Not provided
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>Location:</strong>
                                <p class="mb-0">
                                    <?php if($jobApplication->city || $jobApplication->state || $jobApplication->country): ?>
                                        <?php echo e(collect([$jobApplication->city, $jobApplication->state, $jobApplication->country])->filter()->implode(', ')); ?>

                                    <?php else: ?>
                                        Not provided
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <?php if($jobApplication->address): ?>
                            <div class="mb-3">
                                <strong>Address:</strong>
                                <p class="mb-0"><?php echo e($jobApplication->address); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Experience & Skills -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Experience & Skills</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>Years of Experience:</strong>
                                <p class="mb-0">
                                    <?php if($jobApplication->years_of_experience): ?>
                                        <?php echo e($jobApplication->years_of_experience); ?> years
                                    <?php else: ?>
                                        Not specified
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>Expected Salary:</strong>
                                <p class="mb-0">
                                    <?php if($jobApplication->expected_salary): ?>
                                        $<?php echo e(number_format($jobApplication->expected_salary)); ?>

                                    <?php else: ?>
                                        Not specified
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>Availability:</strong>
                                <p class="mb-0"><?php echo e($jobApplication->availability ?? 'Not specified'); ?></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>Willing to Relocate:</strong>
                                <p class="mb-0">
                                    <span class="badge bg-<?php echo e($jobApplication->willing_to_relocate ? 'success' : 'secondary'); ?>">
                                        <?php echo e($jobApplication->willing_to_relocate ? 'Yes' : 'No'); ?>

                                    </span>
                                </p>
                            </div>
                        </div>

                        <?php if($jobApplication->skills && count($jobApplication->skills) > 0): ?>
                            <div class="mb-3">
                                <strong>Skills:</strong>
                                <div class="mt-2">
                                    <?php $__currentLoopData = $jobApplication->skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-primary me-1 mb-1"><?php echo e($skill); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($jobApplication->why_interested): ?>
                            <div class="mb-3">
                                <strong>Why Interested in This Position:</strong>
                                <p class="mb-0 mt-2"><?php echo e($jobApplication->why_interested); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Cover Letter -->
                <?php if($jobApplication->cover_letter): ?>
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Cover Letter</h6>
                        </div>
                        <div class="card-body">
                            <div class="border-left-primary pl-3">
                                <?php echo nl2br(e($jobApplication->cover_letter)); ?>

                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Admin Notes -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Admin Notes</h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<?php echo e(route('admin.cms.job-applications.update-status', $jobApplication)); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="mb-3">
                                <label for="admin_notes" class="form-label">Internal Notes:</label>
                                <textarea class="form-control" id="admin_notes" name="admin_notes" rows="4" 
                                          placeholder="Add internal notes about this application..."><?php echo e($jobApplication->admin_notes); ?></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Application Status:</label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="pending" <?php echo e($jobApplication->status === 'pending' ? 'selected' : ''); ?>>Pending Review</option>
                                        <option value="reviewing" <?php echo e($jobApplication->status === 'reviewing' ? 'selected' : ''); ?>>Under Review</option>
                                        <option value="shortlisted" <?php echo e($jobApplication->status === 'shortlisted' ? 'selected' : ''); ?>>Shortlisted</option>
                                        <option value="interviewed" <?php echo e($jobApplication->status === 'interviewed' ? 'selected' : ''); ?>>Interviewed</option>
                                        <option value="rejected" <?php echo e($jobApplication->status === 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                                        <option value="hired" <?php echo e($jobApplication->status === 'hired' ? 'selected' : ''); ?>>Hired</option>
                                    </select>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Update Status
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Application Summary -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Application Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Position Applied For:</strong>
                            <p class="mb-0"><?php echo e($jobApplication->career->title); ?></p>
                            <small class="text-muted"><?php echo e($jobApplication->career->department); ?></small>
                        </div>

                        <div class="mb-3">
                            <strong>Application Date:</strong>
                            <p class="mb-0"><?php echo e($jobApplication->created_at->format('M d, Y h:i A')); ?></p>
                        </div>

                        <div class="mb-3">
                            <strong>Current Status:</strong>
                            <p class="mb-0">
                                <span class="badge <?php echo e($jobApplication->status_badge_class); ?> fs-6">
                                    <?php echo e($jobApplication->status_label); ?>

                                </span>
                            </p>
                        </div>

                        <?php if($jobApplication->reviewed_at): ?>
                            <div class="mb-3">
                                <strong>Last Reviewed:</strong>
                                <p class="mb-0"><?php echo e($jobApplication->reviewed_at->format('M d, Y h:i A')); ?></p>
                                <?php if($jobApplication->reviewer): ?>
                                    <small class="text-muted">by <?php echo e($jobApplication->reviewer->name); ?></small>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php if($jobApplication->referral_source): ?>
                            <div class="mb-3">
                                <strong>How They Found Us:</strong>
                                <p class="mb-0"><?php echo e(ucfirst(str_replace('-', ' ', $jobApplication->referral_source))); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Documents -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Documents</h6>
                    </div>
                    <div class="card-body">
                        <?php if($jobApplication->resume_path): ?>
                            <div class="d-flex align-items-center mb-3">
                                <i class="fas fa-file-pdf text-danger fa-2x me-3"></i>
                                <div>
                                    <div class="font-weight-bold">Resume/CV</div>
                                    <small class="text-muted"><?php echo e(basename($jobApplication->resume_path)); ?></small>
                                </div>
                                <div class="ms-auto">
                                    <a href="<?php echo e($jobApplication->resume_url); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center py-3">
                                <i class="fas fa-file-alt fa-2x mb-2"></i><br>
                                No resume uploaded
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="mailto:<?php echo e($jobApplication->email); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-envelope me-2"></i> Send Email
                            </a>
                            <?php if($jobApplication->phone): ?>
                                <a href="tel:<?php echo e($jobApplication->phone); ?>" class="btn btn-outline-success">
                                    <i class="fas fa-phone me-2"></i> Call Applicant
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('admin.cms.careers.show', $jobApplication->career)); ?>" class="btn btn-outline-info">
                                <i class="fas fa-briefcase me-2"></i> View Position
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this job application from <strong><?php echo e($jobApplication->full_name); ?></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="<?php echo e(route('admin.cms.job-applications.destroy', $jobApplication)); ?>" style="display: inline;">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">Delete Application</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function confirmDelete() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\atrix-logistics\resources\views/admin/job-applications/show.blade.php ENDPATH**/ ?>