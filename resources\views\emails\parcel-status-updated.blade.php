<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Update - {{ $parcel->tracking_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-pending { background-color: #6c757d; color: white; }
        .status-picked_up { background-color: #17a2b8; color: white; }
        .status-in_transit { background-color: #007bff; color: white; }
        .status-out_for_delivery { background-color: #ffc107; color: #212529; }
        .status-delivered { background-color: #28a745; color: white; }
        .status-exception { background-color: #dc3545; color: white; }
        .status-returned { background-color: #6f42c1; color: white; }
        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        .info-value {
            color: #6c757d;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px 15px;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>📦 Package Update</h1>
            <p>Your package status has been updated</p>
        </div>

        <!-- Content -->
        <div class="content">
            <h2>Hello {{ $parcel->recipient_name }},</h2>
            
            <p>We have an update on your package <strong>{{ $parcel->tracking_number }}</strong>.</p>

            @if($trackingEvent)
                <div class="info-box">
                    <h3 style="margin-top: 0; color: #667eea;">Latest Update</h3>
                    <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="status-badge status-{{ $parcel->status }}">
                            {{ $parcel->getFormattedStatus() }}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Location:</span>
                        <span class="info-value">{{ $trackingEvent->location }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Date & Time:</span>
                        <span class="info-value">{{ $trackingEvent->event_date->format('M d, Y h:i A') }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Description:</span>
                        <span class="info-value">{{ $trackingEvent->description }}</span>
                    </div>
                </div>
            @endif

            <!-- Package Information -->
            <div class="info-box">
                <h3 style="margin-top: 0; color: #667eea;">Package Details</h3>
                <div class="info-row">
                    <span class="info-label">Tracking Number:</span>
                    <span class="info-value">{{ $parcel->tracking_number }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Description:</span>
                    <span class="info-value">{{ $parcel->description }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Carrier:</span>
                    <span class="info-value">{{ $parcel->carrier->name }}</span>
                </div>
                @if($parcel->estimated_delivery_date)
                    <div class="info-row">
                        <span class="info-label">Estimated Delivery:</span>
                        <span class="info-value">{{ $parcel->estimated_delivery_date->format('M d, Y') }}</span>
                    </div>
                @endif
            </div>

            <!-- Delivery Information -->
            <div class="info-box">
                <h3 style="margin-top: 0; color: #667eea;">Delivery Address</h3>
                <div class="info-row">
                    <span class="info-label">Recipient:</span>
                    <span class="info-value">{{ $parcel->recipient_name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Address:</span>
                    <span class="info-value">
                        {{ $parcel->recipient_address }}<br>
                        {{ $parcel->recipient_city }}, {{ $parcel->recipient_state }} {{ $parcel->recipient_postal_code }}<br>
                        {{ $parcel->recipient_country }}
                    </span>
                </div>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ $trackingUrl }}" class="btn">
                    🔍 Track Your Package
                </a>
            </div>

            @if($parcel->status === 'delivered')
                <div style="background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <strong>🎉 Package Delivered!</strong><br>
                    Your package has been successfully delivered. If you have any issues, please contact our customer service.
                </div>
            @elseif($parcel->status === 'exception')
                <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <strong>⚠️ Delivery Exception</strong><br>
                    There was an issue with your package delivery. Our team is working to resolve this. You will receive another update soon.
                </div>
            @endif

            <p>Thank you for choosing Atrix Logistics for your shipping needs!</p>
            
            <p>Best regards,<br>
            <strong>The Atrix Logistics Team</strong></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>
                <strong>{{ \App\Models\SiteSetting::getValue('site_name', 'Atrix Logistics') }}</strong><br>
                {{ \App\Models\SiteSetting::getValue('site_tagline', 'Professional shipping solutions worldwide') }}<br>
                <a href="mailto:{{ \App\Models\SiteSetting::getValue('support_email', '<EMAIL>') }}">{{ \App\Models\SiteSetting::getValue('support_email', '<EMAIL>') }}</a> |
                <a href="tel:{{ str_replace([' ', '(', ')', '-'], '', \App\Models\SiteSetting::getValue('support_phone', '+****************')) }}">{{ \App\Models\SiteSetting::getValue('support_phone', '+****************') }}</a>
            </p>
            <p style="margin-top: 15px; font-size: 12px; color: #adb5bd;">
                This email was sent because you have a package with tracking number {{ $parcel->tracking_number }}.<br>
                If you believe this email was sent in error, please contact our support team.
            </p>
        </div>
    </div>
</body>
</html>
