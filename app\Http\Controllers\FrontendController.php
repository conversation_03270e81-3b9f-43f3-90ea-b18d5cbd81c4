<?php

namespace App\Http\Controllers;

use App\Models\SiteSetting;
use App\Models\Slider;
use App\Models\TeamMember;
use App\Models\Contact;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class FrontendController extends Controller
{
    /**
     * Display the homepage
     */
    public function home()
    {
        $siteSettings = $this->getSiteSettings();

        $sliders = collect(); // Empty collection as fallback
        $teamMembers = collect(); // Empty collection as fallback

        try {
            $sliders = Slider::where('is_active', true)
                            ->orderBy('sort_order')
                            ->get();
        } catch (\Exception $e) {
            // Table might not exist
        }

        try {
            $teamMembers = TeamMember::where('is_active', true)
                                    ->orderBy('sort_order')
                                    ->limit(4)
                                    ->get();
        } catch (\Exception $e) {
            // Table might not exist
        }

        return view('frontend.home', compact('siteSettings', 'sliders', 'teamMembers'));
    }

    /**
     * Display the about page
     */
    public function about()
    {
        $siteSettings = $this->getSiteSettings();
        $teamMembers = collect();

        try {
            $teamMembers = TeamMember::where('is_active', true)
                                    ->orderBy('sort_order')
                                    ->get();
        } catch (\Exception $e) {
            // Table might not exist
        }

        return view('frontend.about', compact('siteSettings', 'teamMembers'));
    }

    /**
     * Display the services page
     */
    public function services()
    {
        $siteSettings = $this->getSiteSettings();

        return view('frontend.services', compact('siteSettings'));
    }

    /**
     * Display the contact page
     */
    public function contact()
    {
        $siteSettings = $this->getSiteSettings();

        return view('frontend.contact', compact('siteSettings'));
    }

    /**
     * Display the blog index page
     */
    public function blog()
    {
        $siteSettings = $this->getSiteSettings();

        // For now, return a simple blog page
        // In the future, this can be connected to a blog/posts system
        $posts = collect([
            (object) [
                'id' => 1,
                'title' => 'The Future of Global Logistics',
                'slug' => 'future-of-global-logistics',
                'excerpt' => 'Exploring how technology and innovation are reshaping the logistics industry worldwide.',
                'published_at' => now()->subDays(5),
                'featured_image' => null,
                'author' => 'Atrix Logistics Team'
            ],
            (object) [
                'id' => 2,
                'title' => 'Sustainable Shipping Solutions',
                'slug' => 'sustainable-shipping-solutions',
                'excerpt' => 'How we are implementing eco-friendly practices in our shipping and logistics operations.',
                'published_at' => now()->subDays(12),
                'featured_image' => null,
                'author' => 'Atrix Logistics Team'
            ],
            (object) [
                'id' => 3,
                'title' => 'Supply Chain Optimization Tips',
                'slug' => 'supply-chain-optimization-tips',
                'excerpt' => 'Best practices for optimizing your supply chain for maximum efficiency and cost savings.',
                'published_at' => now()->subDays(18),
                'featured_image' => null,
                'author' => 'Atrix Logistics Team'
            ]
        ]);

        return view('frontend.blog.index', compact('siteSettings', 'posts'));
    }

    /**
     * Display a single blog post
     */
    public function blogPost(string $slug)
    {
        $siteSettings = $this->getSiteSettings();

        // For now, return a simple blog post page
        // In the future, this can be connected to a blog/posts system
        $post = (object) [
            'id' => 1,
            'title' => 'The Future of Global Logistics',
            'slug' => 'future-of-global-logistics',
            'content' => 'This is a sample blog post content. In the future, this will be connected to a proper blog system.',
            'excerpt' => 'Exploring how technology and innovation are reshaping the logistics industry worldwide.',
            'published_at' => now()->subDays(5),
            'featured_image' => null,
            'author' => 'Atrix Logistics Team'
        ];

        return view('frontend.blog.show', compact('siteSettings', 'post'));
    }

    /**
     * Get site settings with fallbacks
     */
    private function getSiteSettings()
    {
        try {
            return SiteSetting::pluck('value', 'key_name')->toArray();
        } catch (\Exception $e) {
            return [
                'site_name' => 'Atrix Logistics',
                'site_description' => 'Professional logistics and shipping services',
                'phone' => '+1 (230) 456-155-23',
                'email' => '<EMAIL>',
                'address' => 'Flat 20, Reynolds Neck, North Helenaville, FV77 8WS'
            ];
        }
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:2000',
            'subscribe_newsletter' => 'nullable|boolean',
        ]);

        // Add additional data
        $validated['ip_address'] = $request->ip();
        $validated['user_agent'] = $request->userAgent();

        // Save contact to database
        $contact = Contact::create($validated);

        // Handle newsletter subscription if requested
        if ($request->boolean('subscribe_newsletter')) {
            $existing = NewsletterSubscriber::where('email', $validated['email'])->first();

            if (!$existing) {
                NewsletterSubscriber::create([
                    'email' => $validated['email'],
                    'name' => $validated['name'],
                    'subscription_source' => 'contact_form',
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
            } elseif ($existing->status === 'unsubscribed') {
                $existing->resubscribe();
            }
        }

        // Send email notification to admin
        $this->sendContactNotification($contact);

        $message = 'Thank you for your message! We will get back to you soon.';
        if ($request->boolean('subscribe_newsletter')) {
            $message .= ' You have also been subscribed to our newsletter.';
        }

        // Handle AJAX requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        }

        return back()->with('success', $message);
    }

    /**
     * Handle newsletter subscription
     */
    public function subscribeNewsletter(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|max:255',
            'name' => 'nullable|string|max:255',
        ]);

        // Check if already subscribed
        $existing = NewsletterSubscriber::where('email', $validated['email'])->first();

        if ($existing) {
            if ($existing->status === 'unsubscribed') {
                $existing->resubscribe();
                $message = 'Welcome back! You have been resubscribed to our newsletter.';
            } else {
                $message = 'You are already subscribed to our newsletter.';
            }
        } else {
            // Create new subscription
            $validated['subscription_source'] = 'website';
            $validated['ip_address'] = $request->ip();
            $validated['user_agent'] = $request->userAgent();

            NewsletterSubscriber::create($validated);
            $message = 'Thank you for subscribing to our newsletter!';
        }

        if ($request->expectsJson()) {
            return response()->json(['success' => true, 'message' => $message]);
        }

        return back()->with('success', $message);
    }

    /**
     * Handle newsletter unsubscribe
     */
    public function unsubscribeNewsletter(string $token)
    {
        $subscriber = NewsletterSubscriber::where('unsubscribe_token', $token)->first();

        if (!$subscriber) {
            abort(404, 'Invalid unsubscribe link.');
        }

        $subscriber->unsubscribe();

        return view('frontend.newsletter.unsubscribed', compact('subscriber'));
    }

    /**
     * Send contact notification email to admin
     */
    private function sendContactNotification(Contact $contact)
    {
        try {
            $siteSettings = $this->getSiteSettings();
            $adminEmail = $siteSettings['notification_email'] ?? $siteSettings['contact_email'] ?? config('mail.from.address');

            if ($adminEmail) {
                Mail::send('emails.contact-notification', compact('contact', 'siteSettings'), function ($message) use ($adminEmail, $contact) {
                    $message->to($adminEmail)
                           ->subject('New Contact Form Submission - ' . ($contact->subject ?: 'General Inquiry'));
                });
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification email: ' . $e->getMessage());
        }
    }
}
