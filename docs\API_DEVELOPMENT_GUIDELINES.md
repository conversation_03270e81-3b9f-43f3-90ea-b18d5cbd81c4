# API Development Guidelines & Error Handling

## Table of Contents
1. [Endpoint Structure](#endpoint-structure)
2. [Request/Response Standards](#requestresponse-standards)
3. [Error Handling](#error-handling)
4. [Frontend Integration](#frontend-integration)
5. [Debugging Practices](#debugging-practices)
6. [Common Pitfalls](#common-pitfalls)

---

## Endpoint Structure

### 1. Controller Design Pattern

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Exception;

class BaseApiController extends Controller
{
    /**
     * Standard API response format
     */
    protected function successResponse($data = null, string $message = 'Success', int $status = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ], $status);
    }

    /**
     * Standard error response format
     */
    protected function errorResponse(string $message, $errors = null, int $status = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->toISOString(),
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        // Add debug info in development
        if (config('app.debug')) {
            $response['debug'] = [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3),
                'request_id' => request()->header('X-Request-ID', uniqid()),
            ];
        }

        return response()->json($response, $status);
    }

    /**
     * Handle validation errors consistently
     */
    protected function validationErrorResponse(ValidationException $e): JsonResponse
    {
        return $this->errorResponse(
            'Validation failed',
            [
                'validation_errors' => $e->validator->errors()->all(),
                'field_errors' => $e->validator->errors()->toArray(),
            ],
            422
        );
    }
}
```

### 2. Endpoint Implementation Example

```php
<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Quote;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Exception;

class QuoteController extends Controller
{
    /**
     * Store a new quote with comprehensive error handling
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        // Log incoming request for debugging
        Log::info('Quote submission started', [
            'user_id' => auth()->id(),
            'request_data' => $request->except(['_token']),
            'user_agent' => $request->userAgent(),
            'ip' => $request->ip(),
        ]);

        try {
            // 1. Validate input with detailed rules
            $validated = $this->validateQuoteRequest($request);
            
            // 2. Process and sanitize data
            $processedData = $this->processQuoteData($validated, $request);
            
            // 3. Create quote
            $quote = Quote::create($processedData);
            
            // 4. Log success
            Log::info('Quote created successfully', [
                'quote_id' => $quote->id,
                'quote_number' => $quote->quote_number,
                'user_id' => auth()->id(),
            ]);

            // 5. Return appropriate response
            return $this->handleSuccessResponse($request, $quote);

        } catch (ValidationException $e) {
            return $this->handleValidationError($request, $e);
        } catch (Exception $e) {
            return $this->handleGeneralError($request, $e);
        }
    }

    /**
     * Validate quote request with detailed rules
     */
    private function validateQuoteRequest(Request $request): array
    {
        $quoteType = $request->input('quote_type', 'shipping');
        
        // Base validation rules
        $rules = [
            'quote_type' => 'required|in:shipping,product',
            'priority' => 'required|in:standard,urgent,express',
        ];

        // Add type-specific rules
        if ($quoteType === 'shipping') {
            $rules = array_merge($rules, $this->getShippingValidationRules());
        } else {
            $rules = array_merge($rules, $this->getProductValidationRules());
        }

        // Custom validation messages
        $messages = [
            'quote_type.required' => 'Quote type is required.',
            'quote_type.in' => 'Quote type must be either shipping or product.',
            'service_type.required' => 'Service type is required for shipping quotes.',
            'description.required' => 'Description is required for shipping quotes.',
            'fragile.in' => 'Fragile field must be 0 or 1.',
            'hazardous.in' => 'Hazardous field must be 0 or 1.',
        ];

        return $request->validate($rules, $messages);
    }

    /**
     * Get shipping-specific validation rules
     */
    private function getShippingValidationRules(): array
    {
        return [
            'service_type' => 'required|in:domestic_shipping,international_shipping,express_delivery,freight_shipping,warehousing,custom_logistics,bulk_shipping,specialized_transport',
            'description' => 'required|string|max:5000',
            'origin_address' => 'required|string|max:500',
            'origin_city' => 'required|string|max:100',
            'origin_country' => 'required|string|max:100',
            'destination_address' => 'required|string|max:500',
            'destination_city' => 'required|string|max:100',
            'destination_country' => 'required|string|max:100',
            'package_count' => 'required|integer|min:1',
            'weight_unit' => 'required|in:kg,lbs',
            'delivery_speed' => 'nullable|in:standard,express,overnight,same_day',
            // Boolean fields - accept 0/1 from form
            'fragile' => 'nullable|in:0,1',
            'hazardous' => 'nullable|in:0,1',
            'insurance_required' => 'nullable|in:0,1',
            'signature_required' => 'nullable|in:0,1',
        ];
    }

    /**
     * Process and sanitize quote data
     */
    private function processQuoteData(array $validated, Request $request): array
    {
        $user = auth()->user();
        
        // Add user information
        $validated['user_id'] = $user->id;
        $validated['customer_name'] = $user->name;
        $validated['customer_email'] = $user->email;
        $validated['customer_phone'] = $user->phone;
        $validated['company_name'] = $user->company_name;
        $validated['status'] = 'pending';

        // Convert string boolean values to actual booleans
        if ($validated['quote_type'] === 'shipping') {
            $validated['fragile'] = (bool) ($validated['fragile'] ?? 0);
            $validated['hazardous'] = (bool) ($validated['hazardous'] ?? 0);
            $validated['insurance_required'] = (bool) ($validated['insurance_required'] ?? 0);
            $validated['signature_required'] = (bool) ($validated['signature_required'] ?? 0);
        }

        return $validated;
    }

    /**
     * Handle successful response
     */
    private function handleSuccessResponse(Request $request, Quote $quote): JsonResponse|RedirectResponse
    {
        $message = "Quote request submitted successfully! Quote number: {$quote->quote_number}";
        
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'quote_id' => $quote->id,
                    'quote_number' => $quote->quote_number,
                    'status' => $quote->status,
                ],
                'redirect' => route('customer.quotes.show', $quote),
                'timestamp' => now()->toISOString(),
            ]);
        }

        return redirect()
            ->route('customer.quotes.show', $quote)
            ->with('success', $message);
    }

    /**
     * Handle validation errors
     */
    private function handleValidationError(Request $request, ValidationException $e): JsonResponse|RedirectResponse
    {
        Log::warning('Quote validation failed', [
            'user_id' => auth()->id(),
            'errors' => $e->validator->errors()->toArray(),
            'input' => $request->except(['_token']),
        ]);

        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => array_values($e->validator->errors()->all()),
                'field_errors' => $e->validator->errors()->toArray(),
                'timestamp' => now()->toISOString(),
            ], 422);
        }

        return back()
            ->withErrors($e->validator)
            ->withInput()
            ->with('error', 'Please fix the validation errors and try again.');
    }

    /**
     * Handle general errors
     */
    private function handleGeneralError(Request $request, Exception $e): JsonResponse|RedirectResponse
    {
        Log::error('Quote submission failed', [
            'user_id' => auth()->id(),
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'input' => $request->except(['_token']),
        ]);

        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your request.',
                'error_code' => 'QUOTE_SUBMISSION_FAILED',
                'timestamp' => now()->toISOString(),
            ], 500);
        }

        return back()
            ->withInput()
            ->with('error', 'An error occurred while submitting your quote. Please try again.');
    }
}
```

---

## Request/Response Standards

### 1. Standard Response Format

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    "quote_id": 123,
    "quote_number": "QTE20240115001"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. Error Response Format

```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "The description field is required.",
    "The fragile field must be true or false."
  ],
  "field_errors": {
    "description": ["The description field is required."],
    "fragile": ["The fragile field must be true or false."]
  },
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 3. HTTP Status Codes

```php
// Success responses
200 - OK (successful GET, PUT, PATCH)
201 - Created (successful POST)
204 - No Content (successful DELETE)

// Client error responses
400 - Bad Request (malformed request)
401 - Unauthorized (authentication required)
403 - Forbidden (insufficient permissions)
404 - Not Found (resource doesn't exist)
422 - Unprocessable Entity (validation errors)
429 - Too Many Requests (rate limiting)

// Server error responses
500 - Internal Server Error (unexpected server error)
503 - Service Unavailable (temporary server issue)
```

---

## Error Handling

### 1. Frontend Error Catching

#### JavaScript Error Handling Pattern

```javascript
class ApiClient {
    constructor() {
        this.baseURL = window.location.origin;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        };
    }

    /**
     * Make API request with comprehensive error handling
     */
    async request(url, options = {}) {
        const config = {
            method: 'GET',
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            console.log('API Request:', { url, config });

            const response = await fetch(url, config);
            const data = await response.json();

            console.log('API Response:', { status: response.status, data });

            if (!response.ok) {
                throw new ApiError(data, response.status);
            }

            return data;
        } catch (error) {
            console.error('API Error:', error);

            if (error instanceof ApiError) {
                throw error;
            }

            // Network or parsing error
            throw new ApiError({
                success: false,
                message: 'Network error or invalid response',
                error_code: 'NETWORK_ERROR'
            }, 0);
        }
    }

    /**
     * Submit form data with proper error handling
     */
    async submitForm(formElement, options = {}) {
        const formData = new FormData(formElement);

        // Log form data for debugging
        console.log('Form submission data:');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }

        try {
            const response = await this.request(formElement.action, {
                method: formElement.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            return response;
        } catch (error) {
            this.handleFormError(formElement, error);
            throw error;
        }
    }

    /**
     * Handle form-specific errors
     */
    handleFormError(formElement, error) {
        // Clear previous errors
        this.clearFormErrors(formElement);

        if (error.status === 422 && error.data.field_errors) {
            // Show field-specific errors
            this.showFieldErrors(formElement, error.data.field_errors);
        }

        // Show general error message
        this.showFormAlert(formElement, error.data.message || 'An error occurred', 'danger');
    }

    /**
     * Clear previous form errors
     */
    clearFormErrors(formElement) {
        // Remove error classes
        formElement.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });

        // Remove error messages
        formElement.querySelectorAll('.invalid-feedback').forEach(feedback => {
            feedback.remove();
        });

        // Remove alert messages
        formElement.querySelectorAll('.alert').forEach(alert => {
            alert.remove();
        });
    }

    /**
     * Show field-specific validation errors
     */
    showFieldErrors(formElement, fieldErrors) {
        Object.entries(fieldErrors).forEach(([fieldName, errors]) => {
            const field = formElement.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');

                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = errors[0]; // Show first error

                field.parentNode.appendChild(feedback);
            }
        });
    }

    /**
     * Show form alert message
     */
    showFormAlert(formElement, message, type = 'danger') {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of form
        const firstChild = formElement.firstElementChild;
        formElement.insertBefore(alert, firstChild);

        // Auto-scroll to show error
        alert.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

/**
 * Custom API Error class
 */
class ApiError extends Error {
    constructor(data, status) {
        super(data.message || 'API Error');
        this.name = 'ApiError';
        this.data = data;
        this.status = status;
        this.errors = data.errors || [];
        this.fieldErrors = data.field_errors || {};
        this.errorCode = data.error_code || 'UNKNOWN_ERROR';
    }
}

// Global API client instance
window.apiClient = new ApiClient();
```

#### Form Integration Example

```javascript
// Enhanced form submission with comprehensive error handling
document.addEventListener('DOMContentLoaded', function() {
    const quoteForm = document.getElementById('quoteForm');

    if (quoteForm) {
        quoteForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            try {
                // Show loading state
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
                submitButton.disabled = true;

                // Submit form
                const response = await window.apiClient.submitForm(this);

                // Handle success
                window.apiClient.showFormAlert(this, response.message, 'success');

                // Redirect if provided
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 2000);
                }

            } catch (error) {
                console.error('Form submission failed:', error);

                // Error handling is done in apiClient.handleFormError
                // Additional custom handling can be added here

            } finally {
                // Restore button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });
    }
});
```

### 2. Backend Error Logging

#### Structured Logging

```php
<?php

use Illuminate\Support\Facades\Log;

class ErrorLogger
{
    /**
     * Log API request/response for debugging
     */
    public static function logApiRequest(Request $request, $response = null, $error = null)
    {
        $logData = [
            'request_id' => $request->header('X-Request-ID', uniqid()),
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'input' => $request->except(['password', '_token']),
            'timestamp' => now()->toISOString(),
        ];

        if ($response) {
            $logData['response'] = [
                'status' => $response->getStatusCode(),
                'data' => $response->getData(true),
            ];
        }

        if ($error) {
            $logData['error'] = [
                'message' => $error->getMessage(),
                'file' => $error->getFile(),
                'line' => $error->getLine(),
                'trace' => $error->getTraceAsString(),
            ];
        }

        if ($error) {
            Log::error('API Request Failed', $logData);
        } else {
            Log::info('API Request', $logData);
        }
    }

    /**
     * Log validation errors with context
     */
    public static function logValidationError(ValidationException $e, Request $request)
    {
        Log::warning('Validation Error', [
            'request_id' => $request->header('X-Request-ID', uniqid()),
            'url' => $request->fullUrl(),
            'user_id' => auth()->id(),
            'errors' => $e->validator->errors()->toArray(),
            'input' => $request->except(['password', '_token']),
            'rules' => $e->validator->getRules(),
            'timestamp' => now()->toISOString(),
        ]);
    }
}
```

### 3. Debugging Middleware

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ApiDebugMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);

        // Log incoming request
        Log::debug('API Request Started', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'headers' => $request->headers->all(),
            'input' => $request->except(['password', '_token']),
        ]);

        $response = $next($request);

        // Log response
        $duration = microtime(true) - $startTime;

        Log::debug('API Request Completed', [
            'status' => $response->getStatusCode(),
            'duration' => round($duration * 1000, 2) . 'ms',
            'memory' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB',
        ]);

        return $response;
    }
}
```

---

## Frontend Integration

### 1. Form Validation Helper

```javascript
class FormValidator {
    constructor(formElement) {
        this.form = formElement;
        this.rules = {};
        this.messages = {};
    }

    /**
     * Add validation rule for a field
     */
    addRule(fieldName, rule, message) {
        if (!this.rules[fieldName]) {
            this.rules[fieldName] = [];
        }
        this.rules[fieldName].push(rule);
        this.messages[`${fieldName}.${rule.name}`] = message;
    }

    /**
     * Validate form before submission
     */
    validate() {
        const errors = {};

        Object.entries(this.rules).forEach(([fieldName, rules]) => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            const value = field ? field.value : '';

            rules.forEach(rule => {
                if (!rule.validate(value)) {
                    if (!errors[fieldName]) {
                        errors[fieldName] = [];
                    }
                    errors[fieldName].push(this.messages[`${fieldName}.${rule.name}`]);
                }
            });
        });

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * Show validation errors
     */
    showErrors(errors) {
        Object.entries(errors).forEach(([fieldName, fieldErrors]) => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');

                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = fieldErrors[0];

                field.parentNode.appendChild(feedback);
            }
        });
    }
}

// Validation rules
const ValidationRules = {
    required: {
        name: 'required',
        validate: (value) => value.trim() !== ''
    },
    email: {
        name: 'email',
        validate: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
    },
    min: (length) => ({
        name: 'min',
        validate: (value) => value.length >= length
    }),
    boolean: {
        name: 'boolean',
        validate: (value) => ['0', '1', 'true', 'false'].includes(value)
    }
};
```

### 2. Real-time Field Validation

```javascript
// Add real-time validation to form fields
function addRealTimeValidation(formElement) {
    const fields = formElement.querySelectorAll('input, select, textarea');

    fields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });

        field.addEventListener('input', function() {
            // Clear error state on input
            this.classList.remove('is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.remove();
            }
        });
    });
}

function validateField(field) {
    const value = field.value.trim();
    const rules = field.dataset.rules ? field.dataset.rules.split('|') : [];

    let isValid = true;
    let errorMessage = '';

    rules.forEach(rule => {
        if (rule === 'required' && value === '') {
            isValid = false;
            errorMessage = 'This field is required.';
        } else if (rule === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address.';
        }
        // Add more validation rules as needed
    });

    if (!isValid) {
        field.classList.add('is-invalid');

        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = errorMessage;
    } else {
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }
}
```

---

## Debugging Practices

### 1. Request/Response Logging

```javascript
// Enhanced console logging for debugging
class DebugLogger {
    static logRequest(url, options, data = null) {
        if (!window.APP_DEBUG) return;

        console.group(`🚀 API Request: ${options.method || 'GET'} ${url}`);
        console.log('Options:', options);
        if (data) {
            console.log('Data:', data);
        }
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }

    static logResponse(url, response, data) {
        if (!window.APP_DEBUG) return;

        const emoji = response.ok ? '✅' : '❌';
        console.group(`${emoji} API Response: ${response.status} ${url}`);
        console.log('Status:', response.status, response.statusText);
        console.log('Headers:', Object.fromEntries(response.headers.entries()));
        console.log('Data:', data);
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }

    static logError(error, context = {}) {
        console.group('🔥 Error Occurred');
        console.error('Error:', error);
        console.log('Context:', context);
        console.log('Stack:', error.stack);
        console.log('Timestamp:', new Date().toISOString());
        console.groupEnd();
    }
}
```

### 2. Network Monitoring

```javascript
// Monitor all fetch requests for debugging
if (window.APP_DEBUG) {
    const originalFetch = window.fetch;

    window.fetch = function(...args) {
        const [url, options = {}] = args;

        DebugLogger.logRequest(url, options);

        return originalFetch.apply(this, args)
            .then(response => {
                const clonedResponse = response.clone();

                clonedResponse.json()
                    .then(data => DebugLogger.logResponse(url, response, data))
                    .catch(() => DebugLogger.logResponse(url, response, 'Non-JSON response'));

                return response;
            })
            .catch(error => {
                DebugLogger.logError(error, { url, options });
                throw error;
            });
    };
}
```

### 3. Form Data Inspector

```javascript
// Debug form data before submission
function inspectFormData(formElement) {
    if (!window.APP_DEBUG) return;

    const formData = new FormData(formElement);

    console.group('📋 Form Data Inspection');
    console.log('Form element:', formElement);
    console.log('Action:', formElement.action);
    console.log('Method:', formElement.method);

    console.log('Form fields:');
    for (let [key, value] of formData.entries()) {
        const field = formElement.querySelector(`[name="${key}"]`);
        console.log(`${key}:`, {
            value: value,
            type: field?.type || 'unknown',
            element: field
        });
    }

    // Check for missing required fields
    const requiredFields = formElement.querySelectorAll('[required]');
    const missingFields = [];

    requiredFields.forEach(field => {
        if (!formData.has(field.name) || formData.get(field.name) === '') {
            missingFields.push(field.name);
        }
    });

    if (missingFields.length > 0) {
        console.warn('Missing required fields:', missingFields);
    }

    console.groupEnd();
}
```

---

## Common Pitfalls

### 1. Checkbox/Boolean Field Issues

**Problem:** Checkboxes send different values when checked vs unchecked
- Checked: `"on"` or `"1"`
- Unchecked: No value sent at all

**Solution:**
```html
<!-- Add hidden field to ensure unchecked sends "0" -->
<input type="hidden" name="fragile" value="0">
<input type="checkbox" name="fragile" value="1" {{ old('fragile') ? 'checked' : '' }}>
```

```php
// Validation rules
'fragile' => 'nullable|in:0,1',

// Processing
$validated['fragile'] = (bool) ($validated['fragile'] ?? 0);
```

### 2. CSRF Token Issues

**Problem:** AJAX requests fail with 419 error
**Solution:**
```html
<meta name="csrf-token" content="{{ csrf_token() }}">
```

```javascript
// Add to all AJAX requests
headers: {
    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
}
```

### 3. Silent Validation Failures

**Problem:** Form submits but validation errors aren't shown
**Solution:**
```php
// Always handle both AJAX and regular requests
if ($request->wantsJson() || $request->ajax()) {
    return response()->json([
        'success' => false,
        'errors' => array_values($e->validator->errors()->all())
    ], 422);
}

return back()->withErrors($e->validator)->withInput();
```

### 4. Missing Error Display

**Problem:** Errors occur but user sees nothing
**Solution:**
```javascript
// Always catch and display errors
.catch(error => {
    console.error('Error:', error);

    // Show user-friendly error message
    showErrorMessage(error.message || 'An error occurred');

    // Log detailed error for debugging
    if (window.APP_DEBUG) {
        console.log('Detailed error:', error);
    }
});
```

### 5. Inconsistent Response Formats

**Problem:** Different endpoints return different response structures
**Solution:**
```php
// Use consistent base controller
class BaseApiController extends Controller
{
    protected function successResponse($data = null, $message = 'Success', $status = 200)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ], $status);
    }

    protected function errorResponse($message, $errors = null, $status = 400)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => now()->toISOString(),
        ], $status);
    }
}
```

---

## Implementation Checklist

### Backend Checklist
- [ ] Consistent response format across all endpoints
- [ ] Proper HTTP status codes
- [ ] Comprehensive validation with custom messages
- [ ] Error logging with context
- [ ] CSRF protection
- [ ] Request/response logging for debugging
- [ ] Handle both AJAX and regular form submissions

### Frontend Checklist
- [ ] Proper error catching in all AJAX requests
- [ ] Display validation errors to users
- [ ] Loading states during requests
- [ ] Console logging for debugging
- [ ] CSRF token in AJAX headers
- [ ] Form data inspection tools
- [ ] Real-time field validation
- [ ] Graceful error recovery

### Testing Checklist
- [ ] Test with valid data
- [ ] Test with invalid data
- [ ] Test with missing required fields
- [ ] Test with malformed data
- [ ] Test network failures
- [ ] Test with different user roles
- [ ] Test CSRF protection
- [ ] Test rate limiting

---

## Quick Reference

### Debug Mode Setup
```php
// .env
APP_DEBUG=true
LOG_LEVEL=debug
```

```javascript
// In your layout
<script>
    window.APP_DEBUG = {{ config('app.debug') ? 'true' : 'false' }};
</script>
```

### Essential Headers
```javascript
const headers = {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
    'Accept': 'application/json'
};
```

### Error Response Structure
```json
{
    "success": false,
    "message": "Human readable error message",
    "errors": ["List of error messages"],
    "field_errors": {"field": ["Field specific errors"]},
    "error_code": "MACHINE_READABLE_CODE",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

This comprehensive guide should help prevent silent failures and make debugging much easier when issues arise!
```
