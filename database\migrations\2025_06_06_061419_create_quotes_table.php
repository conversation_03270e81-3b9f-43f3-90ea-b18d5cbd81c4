<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotes', function (Blueprint $table) {
            $table->id();
            $table->string('quote_number')->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');

            // Customer Information
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone')->nullable();
            $table->string('company_name')->nullable();

            // Quote Details
            $table->enum('service_type', [
                'domestic_shipping',
                'international_shipping',
                'express_delivery',
                'freight_shipping',
                'warehousing',
                'custom_logistics',
                'bulk_shipping',
                'specialized_transport'
            ]);
            $table->enum('priority', ['standard', 'urgent', 'express'])->default('standard');
            $table->text('description');
            $table->json('requirements')->nullable(); // Special requirements

            // Shipment Details
            $table->string('origin_address');
            $table->string('origin_city');
            $table->string('origin_state')->nullable();
            $table->string('origin_postal_code')->nullable();
            $table->string('origin_country');

            $table->string('destination_address');
            $table->string('destination_city');
            $table->string('destination_state')->nullable();
            $table->string('destination_postal_code')->nullable();
            $table->string('destination_country');

            // Package Information
            $table->integer('package_count')->default(1);
            $table->decimal('total_weight', 10, 2)->nullable();
            $table->string('weight_unit')->default('kg');
            $table->json('dimensions')->nullable(); // Length, width, height
            $table->string('package_type')->nullable(); // Box, envelope, pallet, etc.
            $table->text('package_description')->nullable();
            $table->boolean('fragile')->default(false);
            $table->boolean('hazardous')->default(false);
            $table->decimal('declared_value', 12, 2)->nullable();

            // Timing
            $table->date('preferred_pickup_date')->nullable();
            $table->date('required_delivery_date')->nullable();
            $table->enum('delivery_speed', ['standard', 'express', 'overnight', 'same_day'])->default('standard');

            // Quote Status & Pricing
            $table->enum('status', [
                'pending',
                'reviewing',
                'quoted',
                'accepted',
                'rejected',
                'expired',
                'converted'
            ])->default('pending');

            $table->decimal('quoted_price', 12, 2)->nullable();
            $table->text('pricing_breakdown')->nullable(); // JSON or text breakdown
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('final_price', 12, 2)->nullable();
            $table->string('currency', 3)->default('USD');

            // Quote Management
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->text('admin_notes')->nullable();
            $table->text('customer_notes')->nullable();
            $table->timestamp('quoted_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('rejected_at')->nullable();

            // Additional Information
            $table->boolean('insurance_required')->default(false);
            $table->boolean('signature_required')->default(false);
            $table->json('additional_services')->nullable(); // Packaging, assembly, etc.
            $table->json('attachments')->nullable(); // File attachments
            $table->json('metadata')->nullable(); // Additional data

            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'status']);
            $table->index(['assigned_to', 'status']);
            $table->index(['service_type', 'status']);
            $table->index(['quote_number']);
            $table->index(['expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotes');
    }
};
