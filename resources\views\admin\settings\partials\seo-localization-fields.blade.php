{{-- Custom fields for SEO Localization settings --}}

@if($setting->key_name === 'supported_locales')
    <div class="col-md-12">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        <p class="text-muted small mb-3">{{ $setting->description }}</p>
        
        <div id="supported-locales-container">
            @php
                $locales = is_string($setting->value) ? json_decode($setting->value, true) : ($setting->value ?: []);
                $defaultLocales = [
                    'en-US' => ['name' => 'English (United States)', 'url' => '', 'enabled' => true],
                    'en-GB' => ['name' => 'English (United Kingdom)', 'url' => '', 'enabled' => false],
                    'es-ES' => ['name' => 'Spanish (Spain)', 'url' => '', 'enabled' => false],
                    'fr-FR' => ['name' => 'French (France)', 'url' => '', 'enabled' => false],
                    'de-DE' => ['name' => 'German (Germany)', 'url' => '', 'enabled' => false],
                    'it-IT' => ['name' => 'Italian (Italy)', 'url' => '', 'enabled' => false],
                    'pt-PT' => ['name' => 'Portuguese (Portugal)', 'url' => '', 'enabled' => false],
                    'nl-NL' => ['name' => 'Dutch (Netherlands)', 'url' => '', 'enabled' => false],
                    'ja-JP' => ['name' => 'Japanese (Japan)', 'url' => '', 'enabled' => false],
                    'zh-CN' => ['name' => 'Chinese (Simplified)', 'url' => '', 'enabled' => false],
                ];
                $locales = array_merge($defaultLocales, $locales);
            @endphp
            
            @foreach($locales as $locale => $config)
                <div class="card mb-3 locale-item">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           name="supported_locales[{{ $locale }}][enabled]" 
                                           value="1" 
                                           {{ ($config['enabled'] ?? false) ? 'checked' : '' }}
                                           id="locale_{{ $locale }}_enabled">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <strong>{{ $locale }}</strong>
                            </div>
                            <div class="col-md-4">
                                <input type="text" class="form-control" 
                                       name="supported_locales[{{ $locale }}][name]" 
                                       value="{{ $config['name'] ?? '' }}" 
                                       placeholder="Language Name">
                            </div>
                            <div class="col-md-5">
                                <input type="url" class="form-control" 
                                       name="supported_locales[{{ $locale }}][url]" 
                                       value="{{ $config['url'] ?? '' }}" 
                                       placeholder="Custom URL (optional)">
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        
        <input type="hidden" name="{{ $setting->key_name }}" id="supported_locales_json">
    </div>

@elseif($setting->key_name === 'localized_meta_titles' || $setting->key_name === 'localized_meta_descriptions' || $setting->key_name === 'organization_name_localized')
    <div class="col-md-12">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        <p class="text-muted small mb-3">{{ $setting->description }}</p>
        
        @php
            $translations = is_string($setting->value) ? json_decode($setting->value, true) : ($setting->value ?: []);
            $languages = ['en' => 'English', 'es' => 'Spanish', 'fr' => 'French', 'de' => 'German', 'it' => 'Italian', 'pt' => 'Portuguese', 'nl' => 'Dutch', 'ja' => 'Japanese', 'zh' => 'Chinese'];
        @endphp
        
        <div class="row">
            @foreach($languages as $lang => $langName)
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ $langName }} ({{ $lang }})</label>
                    @if($setting->key_name === 'localized_meta_descriptions')
                        <textarea class="form-control" 
                                  name="{{ $setting->key_name }}[{{ $lang }}]" 
                                  rows="3" 
                                  placeholder="Meta description in {{ $langName }}">{{ $translations[$lang] ?? '' }}</textarea>
                    @else
                        <input type="text" class="form-control" 
                               name="{{ $setting->key_name }}[{{ $lang }}]" 
                               value="{{ $translations[$lang] ?? '' }}" 
                               placeholder="{{ $setting->key_name === 'localized_meta_titles' ? 'Meta title' : 'Organization name' }} in {{ $langName }}">
                    @endif
                </div>
            @endforeach
        </div>
    </div>

@elseif($setting->key_name === 'currency_by_locale')
    <div class="col-md-12">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        <p class="text-muted small mb-3">{{ $setting->description }}</p>
        
        @php
            $currencyMap = is_string($setting->value) ? json_decode($setting->value, true) : ($setting->value ?: []);
            $locales = ['en-US' => 'USD', 'en-GB' => 'GBP', 'en-CA' => 'CAD', 'en-AU' => 'AUD', 'es-ES' => 'EUR', 'fr-FR' => 'EUR', 'de-DE' => 'EUR', 'it-IT' => 'EUR', 'pt-PT' => 'EUR', 'nl-NL' => 'EUR', 'ja-JP' => 'JPY', 'zh-CN' => 'CNY'];
            $currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'CHF', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'RUB', 'BRL', 'MXN', 'INR', 'KRW', 'SGD', 'HKD', 'NZD', 'ZAR'];
        @endphp
        
        <div class="row">
            @foreach($locales as $locale => $defaultCurrency)
                <div class="col-md-4 mb-3">
                    <label class="form-label">{{ $locale }}</label>
                    <select class="form-select" name="{{ $setting->key_name }}[{{ $locale }}]">
                        @foreach($currencies as $currency)
                            <option value="{{ $currency }}" {{ ($currencyMap[$locale] ?? $defaultCurrency) === $currency ? 'selected' : '' }}>
                                {{ $currency }}
                            </option>
                        @endforeach
                    </select>
                </div>
            @endforeach
        </div>
    </div>

@elseif($setting->key_name === 'google_search_console_properties')
    <div class="col-md-12">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        <p class="text-muted small mb-3">{{ $setting->description }}</p>
        
        @php
            $properties = is_string($setting->value) ? json_decode($setting->value, true) : ($setting->value ?: []);
            $locales = ['en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-PT', 'nl-NL', 'ja-JP', 'zh-CN'];
        @endphp
        
        <div class="row">
            @foreach($locales as $locale)
                <div class="col-md-6 mb-3">
                    <label class="form-label">{{ $locale }} Property URL</label>
                    <input type="url" class="form-control" 
                           name="{{ $setting->key_name }}[{{ $locale }}]" 
                           value="{{ $properties[$locale] ?? '' }}" 
                           placeholder="https://search.google.com/search-console/...">
                </div>
            @endforeach
        </div>
    </div>

@elseif($setting->key_name === 'url_structure_type')
    <div class="col-md-6">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        <p class="text-muted small mb-3">{{ $setting->description }}</p>
        <select class="form-select" name="{{ $setting->key_name }}">
            <option value="subdirectory" {{ $setting->value === 'subdirectory' ? 'selected' : '' }}>
                Subdirectory (/en/, /es/)
            </option>
            <option value="subdomain" {{ $setting->value === 'subdomain' ? 'selected' : '' }}>
                Subdomain (en.example.com, es.example.com)
            </option>
            <option value="domain" {{ $setting->value === 'domain' ? 'selected' : '' }}>
                Different Domains (example.com, example.es)
            </option>
            <option value="parameter" {{ $setting->value === 'parameter' ? 'selected' : '' }}>
                URL Parameter (?lang=en, ?lang=es)
            </option>
        </select>
    </div>

@elseif($setting->key_name === 'canonical_url_strategy')
    <div class="col-md-6">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        <p class="text-muted small mb-3">{{ $setting->description }}</p>
        <select class="form-select" name="{{ $setting->key_name }}">
            <option value="locale_specific" {{ $setting->value === 'locale_specific' ? 'selected' : '' }}>
                Locale Specific (each locale has its own canonical)
            </option>
            <option value="primary_language" {{ $setting->value === 'primary_language' ? 'selected' : '' }}>
                Primary Language (all point to primary language version)
            </option>
            <option value="absolute" {{ $setting->value === 'absolute' ? 'selected' : '' }}>
                Absolute (canonical points to current URL)
            </option>
        </select>
    </div>

@else
    {{-- Default field rendering for other SEO localization settings --}}
    <div class="col-md-6">
        <label class="form-label fw-bold">{{ $setting->label }}</label>
        @if($setting->description)
            <p class="text-muted small mb-3">{{ $setting->description }}</p>
        @endif
        
        @if($setting->type === 'boolean')
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" name="{{ $setting->key_name }}" value="1" 
                       {{ $setting->value ? 'checked' : '' }} id="{{ $setting->key_name }}">
                <label class="form-check-label" for="{{ $setting->key_name }}">
                    Enable {{ $setting->label }}
                </label>
            </div>
        @elseif($setting->type === 'textarea')
            <textarea class="form-control" name="{{ $setting->key_name }}" rows="3" 
                      placeholder="{{ $setting->description }}">{{ $setting->value }}</textarea>
        @else
            <input type="{{ $setting->type === 'string' ? 'text' : $setting->type }}" 
                   class="form-control" 
                   name="{{ $setting->key_name }}" 
                   value="{{ $setting->value }}" 
                   placeholder="{{ $setting->description }}">
        @endif
    </div>
@endif

<script>
// Handle supported locales JSON serialization
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('settingsForm');
    if (form) {
        form.addEventListener('submit', function() {
            // Serialize supported locales
            const localesData = {};
            document.querySelectorAll('.locale-item').forEach(function(item) {
                const locale = item.querySelector('input[type="checkbox"]').name.match(/\[(.*?)\]/)[1];
                const enabled = item.querySelector('input[type="checkbox"]').checked;
                const name = item.querySelector('input[name*="[name]"]').value;
                const url = item.querySelector('input[name*="[url]"]').value;
                
                localesData[locale] = {
                    name: name,
                    url: url,
                    enabled: enabled
                };
            });
            
            const hiddenInput = document.getElementById('supported_locales_json');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(localesData);
            }
        });
    }
});
</script>
