<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Contact;
use App\Models\NewsletterSubscriber;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CommunicationsSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function contact_form_submission_works()
    {
        $contactData = [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'subject' => 'Test Subject',
            'message' => 'This is a test message.',
        ];

        $response = $this->post(route('contact.submit'), $contactData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('contacts', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'status' => 'new'
        ]);
    }

    /** @test */
    public function newsletter_subscription_works()
    {
        $subscriptionData = [
            'email' => '<EMAIL>',
            'name' => 'Jane Smith'
        ];

        $response = $this->post(route('newsletter.subscribe'), $subscriptionData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('newsletter_subscribers', [
            'email' => '<EMAIL>',
            'name' => 'Jane Smith',
            'status' => 'active'
        ]);
    }

    /** @test */
    public function admin_can_view_contacts()
    {
        Contact::factory()->create([
            'name' => 'Test Contact',
            'email' => '<EMAIL>'
        ]);

        $response = $this->actingAs($this->admin)
                        ->get(route('admin.communications.contacts.index'));

        $response->assertStatus(200);
        $response->assertSee('Test Contact');
        $response->assertSee('<EMAIL>');
    }

    /** @test */
    public function admin_can_view_newsletter_subscribers()
    {
        NewsletterSubscriber::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test Subscriber'
        ]);

        $response = $this->actingAs($this->admin)
                        ->get(route('admin.communications.newsletter.index'));

        $response->assertStatus(200);
        $response->assertSee('<EMAIL>');
        $response->assertSee('Test Subscriber');
    }

    /** @test */
    public function admin_can_update_contact_status()
    {
        $contact = Contact::factory()->create(['status' => 'new']);

        $response = $this->actingAs($this->admin)
                        ->put(route('admin.communications.contacts.update', $contact), [
                            'status' => 'read',
                            'admin_notes' => 'Test note'
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $contact->refresh();
        $this->assertEquals('read', $contact->status);
        $this->assertEquals('Test note', $contact->admin_notes);
    }

    /** @test */
    public function newsletter_unsubscribe_works()
    {
        $subscriber = NewsletterSubscriber::factory()->create([
            'status' => 'active',
            'unsubscribe_token' => 'test-token'
        ]);

        $response = $this->get(route('newsletter.unsubscribe', 'test-token'));

        $response->assertStatus(200);
        $response->assertSee('Successfully Unsubscribed');

        $subscriber->refresh();
        $this->assertEquals('unsubscribed', $subscriber->status);
        $this->assertNotNull($subscriber->unsubscribed_at);
    }

    /** @test */
    public function contact_with_newsletter_subscription_works()
    {
        $contactData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'Test message',
            'subscribe_newsletter' => '1'
        ];

        $response = $this->post(route('contact.submit'), $contactData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Check contact was created
        $this->assertDatabaseHas('contacts', [
            'name' => 'John Doe',
            'email' => '<EMAIL>'
        ]);

        // Check newsletter subscription was created
        $this->assertDatabaseHas('newsletter_subscribers', [
            'email' => '<EMAIL>',
            'name' => 'John Doe',
            'subscription_source' => 'contact_form'
        ]);
    }

    /** @test */
    public function admin_can_bulk_delete_contacts()
    {
        $contact1 = Contact::factory()->create();
        $contact2 = Contact::factory()->create();

        $response = $this->actingAs($this->admin)
                        ->post(route('admin.communications.contacts.bulk-action'), [
                            'action' => 'delete',
                            'contacts' => [$contact1->id, $contact2->id]
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseMissing('contacts', ['id' => $contact1->id]);
        $this->assertDatabaseMissing('contacts', ['id' => $contact2->id]);
    }

    /** @test */
    public function admin_can_import_newsletter_subscribers()
    {
        $emails = "<EMAIL>\<EMAIL>\<EMAIL>";

        $response = $this->actingAs($this->admin)
                        ->post(route('admin.communications.newsletter.import'), [
                            'emails' => $emails
                        ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('newsletter_subscribers', ['email' => '<EMAIL>']);
        $this->assertDatabaseHas('newsletter_subscribers', ['email' => '<EMAIL>']);
        $this->assertDatabaseHas('newsletter_subscribers', ['email' => '<EMAIL>']);
    }
}
