<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register ImageProcessingService as singleton
        $this->app->singleton(\App\Services\ImageProcessingService::class, function ($app) {
            return new \App\Services\ImageProcessingService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register View Composers
        \View::composer('*', \App\View\Composers\SiteSettingsComposer::class);



        // Register custom Blade directives
        \Blade::directive('currency', function ($expression) {
            return "<?php echo \App\Helpers\CurrencyHelper::format($expression); ?>";
        });

        \Blade::directive('currencyCode', function () {
            return "<?php echo \App\Helpers\CurrencyHelper::getCode(); ?>";
        });

        \Blade::directive('currencySymbol', function () {
            return "<?php echo \App\Helpers\CurrencyHelper::getSymbol(); ?>";
        });
    }
}
