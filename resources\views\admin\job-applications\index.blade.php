@extends('layouts.admin')

@section('title', 'Job Applications')
@section('page-title', 'Job Applications Management')

@section('content')
    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['pending'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Reviewing</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['reviewing'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-search fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Shortlisted</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['shortlisted'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-star fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-secondary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Interviewed</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['interviewed'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-comments fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Hired</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['hired'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-2 col-md-4 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Rejected</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['rejected'] }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Job Applications</h6>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        Bulk Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('update_status', 'reviewing')">Mark as Reviewing</a></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('update_status', 'shortlisted')">Mark as Shortlisted</a></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('update_status', 'rejected')">Mark as Rejected</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">Delete Selected</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                @if($applications->count() > 0)
                    <form id="bulkForm" method="POST" action="{{ route('admin.cms.job-applications.bulk-action') }}">
                        @csrf
                        <input type="hidden" name="action" id="bulkAction">
                        <input type="hidden" name="status" id="bulkStatus">
                        
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th width="30">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>Applicant</th>
                                        <th>Position</th>
                                        <th>Applied Date</th>
                                        <th>Experience</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($applications as $application)
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="applications[]" value="{{ $application->id }}" class="application-checkbox">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div>
                                                        <div class="font-weight-bold">{{ $application->full_name }}</div>
                                                        <div class="text-muted small">{{ $application->email }}</div>
                                                        @if($application->phone)
                                                            <div class="text-muted small">{{ $application->phone }}</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="font-weight-bold">{{ $application->career->title }}</div>
                                                <div class="text-muted small">{{ $application->career->department }}</div>
                                            </td>
                                            <td>{{ $application->created_at->format('M d, Y') }}</td>
                                            <td>
                                                @if($application->years_of_experience)
                                                    {{ $application->years_of_experience }} years
                                                @else
                                                    Not specified
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge {{ $application->status_badge_class }}">
                                                    {{ $application->status_label }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.cms.job-applications.show', $application) }}" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if($application->resume_path)
                                                        <a href="{{ $application->resume_url }}" target="_blank"
                                                           class="btn btn-sm btn-outline-success" title="View Resume">
                                                            <i class="fas fa-file-pdf"></i>
                                                        </a>
                                                    @endif
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete({{ $application->id }})" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $applications->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-file-alt fa-3x text-gray-300 mb-3"></i>
                        <h5 class="text-gray-600">No Job Applications Found</h5>
                        <p class="text-gray-500">Applications will appear here when candidates apply for positions.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this job application?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Application</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Select all functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.application-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    function confirmDelete(applicationId) {
        const form = document.getElementById('deleteForm');
        form.action = `/admin/cms/job-applications/${applicationId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function bulkAction(action, status = null) {
        const checkedBoxes = document.querySelectorAll('.application-checkbox:checked');
        if (checkedBoxes.length === 0) {
            alert('Please select at least one application.');
            return;
        }

        if (action === 'delete' && !confirm('Are you sure you want to delete the selected applications?')) {
            return;
        }

        document.getElementById('bulkAction').value = action;
        if (status) {
            document.getElementById('bulkStatus').value = status;
        }
        document.getElementById('bulkForm').submit();
    }
</script>
@endpush
