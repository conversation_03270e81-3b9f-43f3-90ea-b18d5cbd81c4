@extends('documents.base')

@section('title', 'Shipping Label - ' . $parcel->tracking_number)
@section('document-title', 'SHIPPING LABEL')

@section('header-right-extra')
    <div style="margin-top: 10px;">
        <span class="badge badge-{{ $parcel->status === 'delivered' ? 'success' : ($parcel->status === 'in_transit' ? 'info' : 'warning') }}">
            {{ ucfirst(str_replace('_', ' ', $parcel->status)) }}
        </span>
    </div>
@endsection

@section('content')
    <!-- Shipping Information -->
    <div class="address-section">
        <div class="address-box">
            <div class="address-title">FROM:</div>
            <div class="address-content">
                <strong>{{ $parcel->sender_name }}</strong><br>
                @if($parcel->sender_company)
                    {{ $parcel->sender_company }}<br>
                @endif
                {{ $parcel->sender_address }}<br>
                {{ $parcel->sender_city }}, {{ $parcel->sender_state }} {{ $parcel->sender_postal_code }}<br>
                {{ $parcel->sender_country }}<br>
                @if($parcel->sender_phone)
                    Phone: {{ $parcel->sender_phone }}
                @endif
            </div>
        </div>
        <div class="address-box">
            <div class="address-title">TO:</div>
            <div class="address-content">
                <strong>{{ $parcel->receiver_name }}</strong><br>
                @if($parcel->receiver_company)
                    {{ $parcel->receiver_company }}<br>
                @endif
                {{ $parcel->receiver_address }}<br>
                {{ $parcel->receiver_city }}, {{ $parcel->receiver_state }} {{ $parcel->receiver_postal_code }}<br>
                {{ $parcel->receiver_country }}<br>
                @if($parcel->receiver_phone)
                    Phone: {{ $parcel->receiver_phone }}
                @endif
            </div>
        </div>
    </div>

    <!-- Tracking and Service Information -->
    <div class="info-box">
        <div class="info-box-title">Shipping Details</div>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Tracking Number:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $parcel->tracking_number }}</td>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Service Type:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ ucfirst($parcel->service_type) }}</td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Ship Date:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $parcel->pickup_date ? $parcel->pickup_date->format('M j, Y') : now()->format('M j, Y') }}</td>
                <td style="border: none; padding: 5px 0;"><strong>Expected Delivery:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $parcel->expected_delivery_date ? $parcel->expected_delivery_date->format('M j, Y') : 'TBD' }}</td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Weight:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $parcel->weight ? $parcel->weight . ' kg' : 'N/A' }}</td>
                <td style="border: none; padding: 5px 0;"><strong>Priority:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ ucfirst($parcel->priority ?? 'standard') }}</td>
            </tr>
        </table>
    </div>

    <!-- Package Information -->
    <div class="section">
        <div class="section-title">Package Information</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 40%;">Description</th>
                    <th style="width: 15%; text-align: center;">Quantity</th>
                    <th style="width: 20%; text-align: center;">Dimensions</th>
                    <th style="width: 25%; text-align: right;">Declared Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $parcel->description ?: 'General Package' }}</td>
                    <td class="text-center">{{ $parcel->quantity ?: 1 }}</td>
                    <td class="text-center">
                        @if($parcel->length && $parcel->width && $parcel->height)
                            {{ $parcel->length }} x {{ $parcel->width }} x {{ $parcel->height }} cm
                        @else
                            N/A
                        @endif
                    </td>
                    <td class="text-right">
                        @if($parcel->declared_value)
                            @currency($parcel->declared_value)
                        @else
                            N/A
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Large Tracking Number for Scanning -->
    <div class="barcode-section" style="margin: 30px 0; padding: 20px; text-align: center;">
        <div style="font-size: 14px; font-weight: bold; margin-bottom: 10px;">TRACKING NUMBER</div>
        <div style="font-family: 'Courier New', monospace; font-size: 24px; font-weight: bold; letter-spacing: 3px; margin-bottom: 10px;">
            {{ $parcel->tracking_number }}
        </div>
        <div style="font-size: 10px; color: #666;">
            Scan this code for tracking updates
        </div>
    </div>

    <!-- Special Instructions -->
    @if($parcel->special_instructions)
    <div class="section">
        <div class="section-title">Special Instructions</div>
        <div class="info-box">
            <p style="margin: 0; font-size: 11px;">{{ $parcel->special_instructions }}</p>
        </div>
    </div>
    @endif

    <!-- Service Features -->
    <div class="section">
        <div class="section-title">Service Features</div>
        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 50%; padding-right: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Included Services</div>
                    <ul style="margin: 0; padding-left: 20px; font-size: 10px;">
                        <li>Real-time tracking</li>
                        <li>Delivery confirmation</li>
                        <li>Basic insurance coverage</li>
                        @if($parcel->insurance_amount)
                        <li>Additional insurance: @currency($parcel->insurance_amount)</li>
                        @endif
                        @if($parcel->cod_amount)
                        <li>Cash on Delivery: @currency($parcel->cod_amount)</li>
                        @endif
                    </ul>
                </div>
            </div>
            <div style="display: table-cell; width: 50%; padding-left: 10px;">
                <div class="info-box">
                    <div class="info-box-title">Delivery Options</div>
                    <ul style="margin: 0; padding-left: 20px; font-size: 10px;">
                        <li>Signature required</li>
                        <li>Safe location delivery</li>
                        <li>Delivery notifications</li>
                        <li>Rescheduling available</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Handling Instructions -->
    <div class="section">
        <div class="section-title">Handling Instructions</div>
        <div style="display: table; width: 100%;">
            <div style="display: table-cell; width: 25%; text-align: center; padding: 10px; border: 1px solid #dee2e6;">
                <i class="fas fa-arrow-up" style="font-size: 20px; color: #2563eb;"></i><br>
                <small><strong>THIS WAY UP</strong></small>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center; padding: 10px; border: 1px solid #dee2e6;">
                <i class="fas fa-tint" style="font-size: 20px; color: #2563eb;"></i><br>
                <small><strong>KEEP DRY</strong></small>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center; padding: 10px; border: 1px solid #dee2e6;">
                <i class="fas fa-exclamation-triangle" style="font-size: 20px; color: #f59e0b;"></i><br>
                <small><strong>FRAGILE</strong></small>
            </div>
            <div style="display: table-cell; width: 25%; text-align: center; padding: 10px; border: 1px solid #dee2e6;">
                <i class="fas fa-thermometer-half" style="font-size: 20px; color: #ef4444;"></i><br>
                <small><strong>TEMPERATURE SENSITIVE</strong></small>
            </div>
        </div>
    </div>

    <!-- Terms and Liability -->
    <div class="section">
        <div class="section-title">Terms & Liability</div>
        <div style="font-size: 9px; line-height: 1.3; color: #666;">
            <p><strong>Liability:</strong> Carrier liability is limited to the declared value or actual value, whichever is less, unless additional insurance is purchased.</p>
            <p><strong>Delivery:</strong> Delivery times are estimates. Carrier is not liable for delays due to weather, customs, or circumstances beyond control.</p>
            <p><strong>Claims:</strong> Damage or loss claims must be reported within 7 days of delivery or expected delivery date.</p>
            <p><strong>Acceptance:</strong> By accepting this shipment, receiver acknowledges receipt in good condition unless noted.</p>
        </div>
    </div>
@endsection

@section('footer')
    <div style="text-align: center; margin-bottom: 10px;">
        <strong>Handle with Care - Professional Logistics Service</strong>
    </div>
    <div style="text-align: center; font-size: 10px;">
        Track online: {{ $siteSettings['company_website'] ?? 'www.atrixlogistics.com' }} | 
        Customer Service: {{ $siteSettings['company_phone'] ?? 'Phone Number' }}
    </div>
@endsection
