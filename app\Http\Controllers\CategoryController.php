<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\View\View;

class CategoryController extends Controller
{
    /**
     * Display products in a specific category
     */
    public function show(Category $category, Request $request): View
    {
        // Ensure category is active
        if (!$category->is_active) {
            abort(404);
        }

        // Get category IDs to include (category and its children)
        $categoryIds = [$category->id];
        if ($category->children->count() > 0) {
            $categoryIds = array_merge($categoryIds, $category->children->pluck('id')->toArray());
        }

        $query = Product::with(['category'])
                       ->active()
                       ->whereIn('category_id', $categoryIds)
                       ->orderBy('sort_order')
                       ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sort options
        $sortBy = $request->get('sort', 'newest');
        switch ($sortBy) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            default: // newest
                $query->orderBy('created_at', 'desc');
                break;
        }

        $products = $query->paginate(12);
        
        // Get subcategories for filter
        $subcategories = $category->children()
                                 ->active()
                                 ->orderBy('sort_order')
                                 ->get();

        // Get price range for filter
        $priceRange = Product::active()
                           ->whereIn('category_id', $categoryIds)
                           ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
                           ->first();

        return view('frontend.categories.show', compact(
            'category',
            'products',
            'subcategories',
            'priceRange'
        ));
    }
}
