<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subtotal = fake()->randomFloat(2, 10, 1000);
        $taxAmount = $subtotal * 0.08; // 8% tax
        $shippingAmount = fake()->randomFloat(2, 0, 25);
        $discountAmount = fake()->randomFloat(2, 0, 50);
        $totalAmount = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

        return [
            'order_number' => 'ORD' . fake()->dateTimeBetween('-1 year')->format('Ymd') . fake()->numberBetween(1000, 9999),
            'customer_id' => User::factory(),
            'status' => fake()->randomElement(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled']),
            'customer_name' => fake()->name(),
            'customer_email' => fake()->safeEmail(),
            'customer_phone' => fake()->phoneNumber(),
            
            // Billing address
            'billing_first_name' => fake()->firstName(),
            'billing_last_name' => fake()->lastName(),
            'billing_company' => fake()->optional()->company(),
            'billing_address_1' => fake()->streetAddress(),
            'billing_address_2' => fake()->optional()->secondaryAddress(),
            'billing_city' => fake()->city(),
            'billing_state' => fake()->stateAbbr(),
            'billing_postal_code' => fake()->postcode(),
            'billing_country' => fake()->country(),
            
            // Shipping address
            'shipping_first_name' => fake()->firstName(),
            'shipping_last_name' => fake()->lastName(),
            'shipping_company' => fake()->optional()->company(),
            'shipping_address_1' => fake()->streetAddress(),
            'shipping_address_2' => fake()->optional()->secondaryAddress(),
            'shipping_city' => fake()->city(),
            'shipping_state' => fake()->stateAbbr(),
            'shipping_postal_code' => fake()->postcode(),
            'shipping_country' => fake()->country(),
            
            // Financial details
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'total_amount' => max(0, $totalAmount),
            
            // Payment details
            'payment_status' => fake()->randomElement(['pending', 'paid', 'partially_paid', 'refunded', 'failed']),
            'payment_method' => fake()->randomElement(['manual', 'paypal', 'stripe']),
            'payment_reference' => fake()->optional()->uuid(),
            'paid_at' => fake()->optional()->dateTimeBetween('-1 month'),
            
            // Shipping details
            'shipping_method' => fake()->optional()->randomElement(['standard', 'express', 'overnight']),
            'tracking_number' => fake()->optional()->regexify('[A-Z]{2}[0-9]{10}'),
            'shipped_at' => fake()->optional()->dateTimeBetween('-2 weeks'),
            'delivered_at' => fake()->optional()->dateTimeBetween('-1 week'),
            
            // Notes
            'notes' => fake()->optional()->sentence(),
            'admin_notes' => fake()->optional()->sentence(),
            
            // Metadata
            'metadata' => fake()->optional()->randomElement([
                ['source' => 'web'],
                ['source' => 'mobile'],
                ['promo_code' => 'SAVE10'],
            ]),
            
            // Cancellation
            'cancelled_at' => null,
            'cancellation_reason' => null,
        ];
    }

    /**
     * Indicate that the order is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'payment_status' => 'pending',
            'shipped_at' => null,
            'delivered_at' => null,
        ]);
    }

    /**
     * Indicate that the order is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'paid_at' => fake()->dateTimeBetween('-1 week'),
        ]);
    }

    /**
     * Indicate that the order is shipped.
     */
    public function shipped(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'shipped',
            'payment_status' => 'paid',
            'paid_at' => fake()->dateTimeBetween('-2 weeks'),
            'shipped_at' => fake()->dateTimeBetween('-1 week'),
            'tracking_number' => fake()->regexify('[A-Z]{2}[0-9]{10}'),
        ]);
    }

    /**
     * Indicate that the order is delivered.
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'payment_status' => 'paid',
            'paid_at' => fake()->dateTimeBetween('-3 weeks'),
            'shipped_at' => fake()->dateTimeBetween('-2 weeks'),
            'delivered_at' => fake()->dateTimeBetween('-1 week'),
            'tracking_number' => fake()->regexify('[A-Z]{2}[0-9]{10}'),
        ]);
    }

    /**
     * Indicate that the order is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancelled_at' => fake()->dateTimeBetween('-1 month'),
            'cancellation_reason' => fake()->randomElement([
                'Customer request',
                'Out of stock',
                'Payment failed',
                'Shipping issues',
            ]),
        ]);
    }

    /**
     * Indicate that the order uses manual payment.
     */
    public function manualPayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'manual',
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the order uses PayPal payment.
     */
    public function paypalPayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'paypal',
            'payment_reference' => 'PAYPAL-' . fake()->uuid(),
        ]);
    }

    /**
     * Indicate that the order uses Stripe payment.
     */
    public function stripePayment(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'stripe',
            'payment_reference' => 'pi_' . fake()->regexify('[a-zA-Z0-9]{24}'),
        ]);
    }
}
