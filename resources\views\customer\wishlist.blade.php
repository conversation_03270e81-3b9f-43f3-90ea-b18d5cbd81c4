@extends('layouts.customer')

@section('title', 'My Wishlist')
@section('page-title', 'My Wishlist')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        @if($wishlistItems->count() > 0)
            <button type="button" class="btn btn-outline-danger" onclick="clearWishlist()">
                <i class="fas fa-trash me-1"></i> Clear Wishlist
            </button>
        @endif
    </div>
@endsection

@section('content')
    <!-- Wishlist Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="d-flex justify-content-between text-dark align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $wishlistItems->total() }}</h3>
                        <p class="mb-0">Items in Wishlist</p>
                    </div>
                    <div>
                        <i class="fas fa-heart fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $wishlistItems->where('product.is_active', true)->count() }}</h3>
                        <p class="mb-0">Available Items</p>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">${{ number_format($wishlistItems->sum(function($item) { return $item->product ? $item->product->price : 0; }), 0) }}</h3>
                        <p class="mb-0">Total Value</p>
                    </div>
                    <div>
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $wishlistItems->where('product.manage_stock', true)->where('product.stock_quantity', 0)->count() }}</h3>
                        <p class="mb-0">Out of Stock</p>
                    </div>
                    <div>
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wishlist Items -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-heart me-2"></i>
                Your Wishlist ({{ $wishlistItems->total() }})
            </h5>
        </div>
        
        <div class="card-body">
            @if($wishlistItems->count() > 0)
                <div class="row">
                    @foreach($wishlistItems as $item)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 wishlist-item" data-wishlist-id="{{ $item->id }}">
                                @if($item->product)
                                    <div class="position-relative">
                                        @if($item->product->featured_image)
                                            <img src="{{ Storage::url($item->product->featured_image) }}" 
                                                 class="card-img-top" 
                                                 alt="{{ $item->product->name }}"
                                                 style="height: 200px; object-fit: cover;">
                                        @else
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                                 style="height: 200px;">
                                                <i class="fas fa-image fa-3x text-muted"></i>
                                            </div>
                                        @endif
                                        
                                        <!-- Stock Status Badge -->
                                        @if($item->product->manage_stock)
                                            @if($item->product->stock_quantity == 0)
                                                <span class="position-absolute top-0 end-0 badge bg-danger m-2">
                                                    Out of Stock
                                                </span>
                                            @elseif($item->product->stock_quantity <= $item->product->min_stock_level)
                                                <span class="position-absolute top-0 end-0 badge bg-warning m-2">
                                                    Low Stock
                                                </span>
                                            @endif
                                        @endif
                                        
                                        <!-- Remove Button -->
                                        <button type="button" class="btn btn-sm btn-danger position-absolute top-0 start-0 m-2" 
                                                onclick="removeFromWishlist({{ $item->id }})" title="Remove from wishlist">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    
                                    <div class="card-body d-flex flex-column">
                                        <div class="mb-2">
                                            @if($item->product->category)
                                                <span class="badge bg-secondary">{{ $item->product->category->name }}</span>
                                            @endif
                                            @if($item->product->is_featured)
                                                <span class="badge bg-warning">Featured</span>
                                            @endif
                                        </div>
                                        
                                        <h5 class="card-title">{{ $item->product->name }}</h5>
                                        
                                        @if($item->product->short_description)
                                            <p class="card-text text-muted">
                                                {{ Str::limit($item->product->short_description, 100) }}
                                            </p>
                                        @endif
                                        
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="h5 text-primary">${{ number_format($item->product->price, 2) }}</span>
                                                    @if($item->product->compare_price && $item->product->compare_price > $item->product->price)
                                                        <small class="text-muted text-decoration-line-through ms-2">
                                                            ${{ number_format($item->product->compare_price, 2) }}
                                                        </small>
                                                    @endif
                                                </div>
                                                @if($item->product->manage_stock)
                                                    <small class="text-muted">
                                                        Stock: {{ $item->product->stock_quantity }}
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        @if($item->notes)
                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    <strong>Notes:</strong> {{ $item->notes }}
                                                </small>
                                            </div>
                                        @endif
                                        
                                        <div class="mt-auto">
                                            <small class="text-muted">
                                                Added {{ $item->created_at->diffForHumans() }}
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="card-footer bg-transparent">
                                        <div class="d-grid gap-2">
                                            @if($item->product->is_active && (!$item->product->manage_stock || $item->product->stock_quantity > 0))
                                                <button type="button" class="btn btn-primary" onclick="addToCart({{ $item->product->id }})">
                                                    <i class="fas fa-cart-plus me-2"></i>
                                                    Add to Cart
                                                </button>
                                            @else
                                                <button type="button" class="btn btn-secondary" disabled>
                                                    <i class="fas fa-ban me-2"></i>
                                                    Unavailable
                                                </button>
                                            @endif
                                            
                                            <div class="btn-group" role="group">
                                                <a href="#" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye me-1"></i> View
                                                </a>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                        onclick="editNotes({{ $item->id }}, '{{ addslashes($item->notes) }}')">
                                                    <i class="fas fa-edit me-1"></i> Notes
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="removeFromWishlist({{ $item->id }})">
                                                    <i class="fas fa-trash me-1"></i> Remove
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <!-- Product no longer exists -->
                                    <div class="card-body text-center">
                                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                        <h5 class="card-title">Product Unavailable</h5>
                                        <p class="card-text text-muted">This product is no longer available.</p>
                                        <button type="button" class="btn btn-outline-danger" onclick="removeFromWishlist({{ $item->id }})">
                                            <i class="fas fa-trash me-2"></i>
                                            Remove from Wishlist
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing {{ $wishlistItems->firstItem() }} to {{ $wishlistItems->lastItem() }} of {{ $wishlistItems->total() }} items
                    </div>
                    {{ $wishlistItems->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Your wishlist is empty</h5>
                    <p class="text-muted">Start adding products to your wishlist to keep track of items you love.</p>
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-2"></i> Start Shopping
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Notes Modal -->
    <div class="modal fade" id="notesModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Notes</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="notesForm">
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" 
                                      placeholder="Add your notes about this product..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveNotes()">Save Notes</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .wishlist-item {
        transition: transform 0.2s;
    }
    
    .wishlist-item:hover {
        transform: translateY(-5px);
    }
    
    .stats-card {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.success {
        border-left-color: #28a745;
    }
    
    .stats-card.warning {
        border-left-color: #ffc107;
    }
    
    .stats-card.info {
        border-left-color: #17a2b8;
    }
</style>
@endpush

@push('scripts')
<script>
    let currentWishlistId = null;

    function removeFromWishlist(wishlistId) {
        if (confirm('Are you sure you want to remove this item from your wishlist?')) {
            fetch(`/customer/wishlist/${wishlistId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the item from the page
                    document.querySelector(`[data-wishlist-id="${wishlistId}"]`).closest('.col-lg-4').remove();
                    
                    // Show success message
                    showToast('Item removed from wishlist', 'success');
                    
                    // Reload page if no items left
                    if (document.querySelectorAll('.wishlist-item').length === 0) {
                        location.reload();
                    }
                } else {
                    showToast('Error removing item from wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred', 'error');
            });
        }
    }

    function editNotes(wishlistId, currentNotes) {
        currentWishlistId = wishlistId;
        document.getElementById('notes').value = currentNotes || '';
        new bootstrap.Modal(document.getElementById('notesModal')).show();
    }

    function saveNotes() {
        const notes = document.getElementById('notes').value;
        
        fetch(`/customer/wishlist/${currentWishlistId}/notes`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                bootstrap.Modal.getInstance(document.getElementById('notesModal')).hide();
                showToast('Notes updated successfully', 'success');
                // Optionally reload the page to show updated notes
                location.reload();
            } else {
                showToast('Error updating notes', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred', 'error');
        });
    }

    function addToCart(productId) {
        // Implementation for adding to cart
        alert('Add to cart functionality coming soon!');
    }

    function clearWishlist() {
        if (confirm('Are you sure you want to clear your entire wishlist? This action cannot be undone.')) {
            fetch('/customer/wishlist/clear', {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    showToast('Error clearing wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('An error occurred', 'error');
            });
        }
    }

    function showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0 position-fixed top-0 end-0 m-3`;
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 5000);
    }
</script>
@endpush
