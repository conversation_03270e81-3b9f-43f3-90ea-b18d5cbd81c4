/**
 * Professional Document Generator
 * Replaces all standard print functionality with professional PDF generation
 */

class DocumentGenerator {
    constructor() {
        this.baseUrl = window.location.origin;
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        this.init();
    }

    init() {
        // Replace all print buttons with document generation
        this.replacePrintButtons();
        
        // Add event listeners for document generation
        this.addEventListeners();
        
        // Override browser print function
        this.overridePrintFunction();
    }

    /**
     * Replace all print buttons with professional document generation buttons
     */
    replacePrintButtons() {
        // Find all print buttons and links
        const printElements = document.querySelectorAll([
            'button[onclick*="print"]',
            'a[onclick*="print"]',
            '.print-btn',
            '.btn-print',
            '[data-action="print"]'
        ].join(','));

        printElements.forEach(element => {
            this.replacePrintElement(element);
        });
    }

    /**
     * Replace individual print element
     */
    replacePrintElement(element) {
        const documentType = this.detectDocumentType(element);
        const entityId = this.extractEntityId(element);
        
        if (documentType && entityId) {
            // Create new professional button
            const newButton = this.createDocumentButton(documentType, entityId);
            element.parentNode.replaceChild(newButton, element);
        }
    }

    /**
     * Detect document type from element context
     */
    detectDocumentType(element) {
        const context = element.closest('[data-document-type]')?.dataset.documentType;
        if (context) return context;

        // Check URL or page context
        const url = window.location.pathname;
        const text = element.textContent.toLowerCase();

        if (url.includes('/invoice') || text.includes('invoice')) return 'invoice';
        if (url.includes('/quote') || text.includes('quote')) return 'quote';
        if (url.includes('/waybill') || text.includes('waybill')) return 'waybill';
        if (url.includes('/parcel') || text.includes('shipping')) return 'shipping_label';
        if (url.includes('/delivery') || text.includes('delivery')) return 'delivery_receipt';

        return 'invoice'; // Default fallback
    }

    /**
     * Extract entity ID from element or page context
     */
    extractEntityId(element) {
        // Check data attributes
        const dataId = element.dataset.id || element.dataset.entityId;
        if (dataId) return dataId;

        // Check closest container
        const container = element.closest('[data-id]');
        if (container) return container.dataset.id;

        // Extract from URL
        const urlMatch = window.location.pathname.match(/\/(\d+)/);
        if (urlMatch) return urlMatch[1];

        // Extract from page content
        const idElement = document.querySelector('[data-entity-id]');
        if (idElement) return idElement.dataset.entityId;

        return null;
    }

    /**
     * Create professional document generation button
     */
    createDocumentButton(documentType, entityId) {
        const button = document.createElement('button');
        button.className = 'btn btn-primary document-generate-btn';
        button.innerHTML = `
            <i class="fas fa-file-pdf mr-2"></i>
            Generate ${this.formatDocumentType(documentType)}
        `;
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            this.generateDocument(documentType, entityId);
        });

        return button;
    }

    /**
     * Format document type for display
     */
    formatDocumentType(type) {
        const types = {
            'invoice': 'Invoice PDF',
            'quote': 'Quote PDF',
            'waybill': 'Waybill PDF',
            'shipping_label': 'Shipping Label',
            'delivery_receipt': 'Delivery Receipt'
        };
        return types[type] || 'Document PDF';
    }

    /**
     * Add event listeners for document generation
     */
    addEventListeners() {
        // Listen for dynamically added print buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.document-generate-btn') || e.target.closest('.document-generate-btn')) {
                e.preventDefault();
                const button = e.target.closest('.document-generate-btn');
                const documentType = button.dataset.documentType;
                const entityId = button.dataset.entityId;
                
                if (documentType && entityId) {
                    this.generateDocument(documentType, entityId);
                }
            }
        });

        // Listen for bulk generation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.bulk-generate-btn') || e.target.closest('.bulk-generate-btn')) {
                e.preventDefault();
                this.handleBulkGeneration();
            }
        });
    }

    /**
     * Override browser print function
     */
    overridePrintFunction() {
        const originalPrint = window.print;
        window.print = () => {
            // Show modal asking user to use professional document generation instead
            this.showPrintAlternativeModal();
        };
    }

    /**
     * Generate professional document
     */
    async generateDocument(documentType, entityId, preview = false) {
        try {
            this.showLoading();

            const action = preview ? 'preview' : 'generate';
            const url = `${this.baseUrl}/documents/${documentType}/${entityId}/${action}`;

            if (preview) {
                // Open preview in new tab
                window.open(url, '_blank');
            } else {
                // Download PDF
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': this.csrfToken,
                        'Accept': 'application/pdf'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `${documentType}_${entityId}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(downloadUrl);
                document.body.removeChild(a);

                this.showSuccess(`${this.formatDocumentType(documentType)} generated successfully!`);
            }
        } catch (error) {
            console.error('Document generation failed:', error);
            this.showError('Failed to generate document. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle bulk document generation
     */
    async handleBulkGeneration() {
        const selectedItems = this.getSelectedItems();
        
        if (selectedItems.length === 0) {
            this.showError('Please select items to generate documents for.');
            return;
        }

        const documentType = this.detectBulkDocumentType();
        
        try {
            this.showLoading();

            const response = await fetch(`${this.baseUrl}/documents/bulk-generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                },
                body: JSON.stringify({
                    type: documentType,
                    ids: selectedItems
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `bulk_${documentType}_${new Date().toISOString().split('T')[0]}.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(a);

            this.showSuccess(`Bulk ${documentType} documents generated successfully!`);
        } catch (error) {
            console.error('Bulk generation failed:', error);
            this.showError('Failed to generate bulk documents. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Get selected items for bulk generation
     */
    getSelectedItems() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked[data-id]');
        return Array.from(checkboxes).map(cb => cb.dataset.id);
    }

    /**
     * Detect document type for bulk generation
     */
    detectBulkDocumentType() {
        const url = window.location.pathname;
        if (url.includes('/invoice')) return 'invoice';
        if (url.includes('/quote')) return 'quote';
        if (url.includes('/parcel')) return 'waybill';
        return 'invoice'; // Default
    }

    /**
     * Show print alternative modal
     */
    showPrintAlternativeModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Professional Document Generation</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>For a professional appearance, we recommend using our document generation feature instead of browser printing.</p>
                        <p>This will create a high-quality PDF with your company branding and professional formatting.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="documentGenerator.generateCurrentDocument()">
                            Generate Professional PDF
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        $(modal).modal('show');
        
        $(modal).on('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    /**
     * Generate document for current page
     */
    generateCurrentDocument() {
        const documentType = this.detectDocumentType(document.body);
        const entityId = this.extractEntityId(document.body);
        
        if (documentType && entityId) {
            this.generateDocument(documentType, entityId);
        } else {
            this.showError('Unable to detect document type. Please use the specific generate button.');
        }
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        const loading = document.createElement('div');
        loading.id = 'document-loading';
        loading.className = 'fixed-top d-flex justify-content-center align-items-center';
        loading.style.cssText = 'background: rgba(0,0,0,0.5); z-index: 9999; height: 100vh;';
        loading.innerHTML = `
            <div class="bg-white rounded p-4 text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <div>Generating professional document...</div>
            </div>
        `;
        document.body.appendChild(loading);
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        const loading = document.getElementById('document-loading');
        if (loading) {
            document.body.removeChild(loading);
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
        toast.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                document.body.removeChild(toast);
            }
        }, 5000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.documentGenerator = new DocumentGenerator();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DocumentGenerator;
}
