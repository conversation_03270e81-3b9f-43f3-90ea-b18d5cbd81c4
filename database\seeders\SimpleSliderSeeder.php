<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SimpleSliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing sliders
        DB::table('sliders')->truncate();

        // Insert sample sliders
        DB::table('sliders')->insert([
            [
                'title' => 'Fast & Reliable Shipping',
                'subtitle' => 'Worldwide Delivery Solutions',
                'description' => 'Experience lightning-fast delivery with our global network. We ensure your packages reach their destination safely and on time, every time.',
                'image' => 'assets/images/banner/banner-16.jpg',
                'button_text' => 'Get Quote',
                'button_url' => '#',
                'sort_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Track Your Package',
                'subtitle' => 'Real-time Tracking Technology',
                'description' => 'Stay informed with our advanced tracking system. Monitor your package journey from pickup to delivery with real-time updates.',
                'image' => 'assets/images/banner/banner-17.jpg',
                'button_text' => 'Track Now',
                'button_url' => '/tracking',
                'sort_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Professional Logistics',
                'subtitle' => 'Trusted by Thousands',
                'description' => 'Join thousands of satisfied customers who trust Atrix Logistics for their shipping needs. Professional service, competitive rates.',
                'image' => 'assets/images/banner/banner-18.jpg',
                'button_text' => 'Learn More',
                'button_url' => '/about',
                'sort_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        $this->command->info('Sliders seeded successfully!');
    }
}
