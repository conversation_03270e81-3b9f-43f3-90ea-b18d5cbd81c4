# 🔌 API Documentation - Atrix Logistics

## 📋 Overview
This document outlines all API endpoints for the Atrix Logistics application, including request/response formats, authentication requirements, and usage examples.

## 🔐 Authentication

### API Token Authentication
```http
Authorization: Bearer {api_token}
Content-Type: application/json
Accept: application/json
```

### Getting API Token
```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}

Response:
{
    "token": "1|abc123...",
    "user": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>",
        "role": "admin"
    }
}
```

## 📦 Parcel Tracking API

### Track Parcel
```http
GET /api/track/{tracking_number}

Response:
{
    "success": true,
    "data": {
        "tracking_number": "ATX-2024-12345678",
        "status": "in_transit",
        "current_location": "Chicago, IL",
        "estimated_delivery": "2024-01-25",
        "carrier": {
            "id": 1,
            "name": "DHL Express",
            "code": "DHL"
        },
        "events": [
            {
                "id": 1,
                "status": "picked_up",
                "location": "New York, NY",
                "description": "Package picked up",
                "event_date": "2024-01-20T10:00:00Z",
                "is_public": true
            }
        ]
    }
}
```

### Create Parcel (Admin Only)
```http
POST /api/parcels
Authorization: Bearer {admin_token}

{
    "carrier_id": 1,
    "sender_name": "John Doe",
    "sender_email": "<EMAIL>",
    "sender_address": "123 Main St",
    "sender_city": "New York",
    "sender_state": "NY",
    "sender_postal_code": "10001",
    "recipient_name": "Jane Smith",
    "recipient_email": "<EMAIL>",
    "recipient_address": "456 Oak Ave",
    "recipient_city": "Los Angeles",
    "recipient_state": "CA",
    "recipient_postal_code": "90210",
    "description": "Electronics",
    "weight": 2.5,
    "service_type": "express"
}

Response:
{
    "success": true,
    "data": {
        "id": 123,
        "tracking_number": "ATX-2024-12345678",
        "status": "pending",
        "created_at": "2024-01-20T10:00:00Z"
    }
}
```

## 🛒 Products API

### Get Products
```http
GET /api/products?page=1&per_page=20&category=shipping-boxes

Response:
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "Large Shipping Box",
            "slug": "large-shipping-box",
            "description": "Durable cardboard shipping box",
            "price": 25.99,
            "sale_price": null,
            "stock_quantity": 100,
            "in_stock": true,
            "images": [
                {
                    "id": 1,
                    "image_path": "/storage/products/box-large.jpg",
                    "alt_text": "Large shipping box",
                    "is_primary": true
                }
            ],
            "categories": [
                {
                    "id": 1,
                    "name": "Shipping Boxes",
                    "slug": "shipping-boxes"
                }
            ]
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 20,
        "total": 50,
        "last_page": 3
    }
}
```

### Get Single Product
```http
GET /api/products/{id}

Response:
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Large Shipping Box",
        "slug": "large-shipping-box",
        "description": "Durable cardboard shipping box for all your shipping needs.",
        "short_description": "Durable cardboard shipping box",
        "sku": "SB-001",
        "price": 25.99,
        "sale_price": null,
        "stock_quantity": 100,
        "in_stock": true,
        "weight": 0.5,
        "dimensions": {
            "length": 30,
            "width": 20,
            "height": 15
        },
        "images": [...],
        "categories": [...]
    }
}
```

## 💬 Quote Requests API

### Submit Quote Request
```http
POST /api/quotes

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "company": "ABC Corp",
    "origin_address": "123 Main St, New York, NY 10001",
    "destination_address": "456 Oak Ave, Los Angeles, CA 90210",
    "service_type": "standard",
    "estimated_weight": 10.5,
    "estimated_dimensions": "30x20x15",
    "message": "Need quote for regular shipments",
    "items": [
        {
            "product_id": 1,
            "product_name": "Large Shipping Box",
            "quantity": 5,
            "special_notes": "Bulk discount requested"
        }
    ]
}

Response:
{
    "success": true,
    "data": {
        "id": 456,
        "quote_number": "QTE-2024-12345678",
        "status": "pending",
        "created_at": "2024-01-20T10:00:00Z"
    },
    "message": "Quote request submitted successfully. You will receive a response within 24 hours."
}
```

### Get Quote Status
```http
GET /api/quotes/{quote_number}

Response:
{
    "success": true,
    "data": {
        "quote_number": "QTE-2024-12345678",
        "status": "quoted",
        "quoted_amount": 150.00,
        "quoted_at": "2024-01-21T14:30:00Z",
        "expires_at": "2024-02-20T14:30:00Z",
        "items": [
            {
                "product_name": "Large Shipping Box",
                "quantity": 5,
                "unit_price": 25.99,
                "total": 129.95
            }
        ]
    }
}
```

## 🛍️ Shopping Cart API

### Add to Cart
```http
POST /api/cart/add
Authorization: Bearer {user_token}

{
    "product_id": 1,
    "quantity": 2
}

Response:
{
    "success": true,
    "data": {
        "cart_item_id": 123,
        "product": {
            "id": 1,
            "name": "Large Shipping Box",
            "price": 25.99
        },
        "quantity": 2,
        "subtotal": 51.98
    }
}
```

### Get Cart
```http
GET /api/cart
Authorization: Bearer {user_token}

Response:
{
    "success": true,
    "data": {
        "items": [
            {
                "id": 123,
                "product": {
                    "id": 1,
                    "name": "Large Shipping Box",
                    "price": 25.99,
                    "image": "/storage/products/box-large.jpg"
                },
                "quantity": 2,
                "subtotal": 51.98
            }
        ],
        "totals": {
            "subtotal": 51.98,
            "tax": 4.16,
            "shipping": 9.99,
            "total": 66.13
        }
    }
}
```

## 📞 Contact API

### Submit Contact Form
```http
POST /api/contact

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "subject": "General Inquiry",
    "message": "I need information about your services",
    "type": "contact"
}

Response:
{
    "success": true,
    "message": "Thank you for your message. We will respond within 24 hours.",
    "data": {
        "submission_id": 789,
        "created_at": "2024-01-20T10:00:00Z"
    }
}
```

## ⚙️ Site Settings API (Public)

### Get Public Settings
```http
GET /api/settings/public

Response:
{
    "success": true,
    "data": {
        "site_name": "Atrix Logistics",
        "site_tagline": "We ship anything, anywhere, anytime",
        "contact_email": "<EMAIL>",
        "contact_phone": "+****************",
        "contact_address": "123 Logistics Ave, Transport City, TC 12345",
        "business_hours": "Monday - Friday: 8:00 AM - 6:00 PM",
        "social_media": {
            "facebook": "https://facebook.com/atrixlogistics",
            "twitter": "https://twitter.com/atrixlogistics",
            "linkedin": "https://linkedin.com/company/atrixlogistics"
        }
    }
}
```

## 📊 Error Handling

### Standard Error Response
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "email": ["The email field is required."],
        "phone": ["The phone format is invalid."]
    },
    "code": 422
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Server Error

## 🔄 Rate Limiting

### Limits
- **Public endpoints**: 60 requests per minute
- **Authenticated endpoints**: 120 requests per minute
- **Admin endpoints**: 300 requests per minute

### Headers
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

## 📝 Pagination

### Standard Pagination
```json
{
    "data": [...],
    "meta": {
        "current_page": 1,
        "per_page": 20,
        "total": 100,
        "last_page": 5,
        "from": 1,
        "to": 20
    },
    "links": {
        "first": "/api/products?page=1",
        "last": "/api/products?page=5",
        "prev": null,
        "next": "/api/products?page=2"
    }
}
```

## 🧪 Testing Examples

### Using cURL
```bash
# Track parcel
curl -X GET "https://api.atrixlogistics.com/api/track/ATX-2024-12345678" \
     -H "Accept: application/json"

# Submit quote (with authentication)
curl -X POST "https://api.atrixlogistics.com/api/quotes" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-token-here" \
     -d '{"name":"John Doe","email":"<EMAIL>",...}'
```

### Using JavaScript (Fetch)
```javascript
// Track parcel
const response = await fetch('/api/track/ATX-2024-12345678', {
    headers: {
        'Accept': 'application/json'
    }
});
const data = await response.json();

// Submit quote
const quoteResponse = await fetch('/api/quotes', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({
        name: 'John Doe',
        email: '<EMAIL>',
        // ... other fields
    })
});
```

This API documentation provides complete reference for all endpoints, making it easy for any developer to integrate with or continue development of the Atrix Logistics application.
