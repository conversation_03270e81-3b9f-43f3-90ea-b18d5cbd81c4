<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add category relationship if it doesn't exist
            if (!Schema::hasColumn('products', 'category_id')) {
                $table->unsignedBigInteger('category_id')->nullable()->after('id');
            }

            // Add missing fields that might be referenced
            if (!Schema::hasColumn('products', 'cost_price')) {
                $table->decimal('cost_price', 10, 2)->nullable()->after('sale_price');
            }
            if (!Schema::hasColumn('products', 'min_stock_level')) {
                $table->integer('min_stock_level')->default(0)->after('stock_quantity');
            }
            if (!Schema::hasColumn('products', 'stock_status')) {
                $table->enum('stock_status', ['in_stock', 'out_of_stock', 'on_backorder'])->default('in_stock')->after('min_stock_level');
            }

            // Update status fields - drop indexes first, then columns
            $table->dropIndex(['status', 'featured']);
            $table->dropIndex(['in_stock', 'status']);
            $table->dropColumn(['status', 'featured']);
            $table->boolean('is_active')->default(true)->after('attributes');
            $table->boolean('is_featured')->default(false)->after('is_active');
            $table->boolean('is_digital')->default(false)->after('is_featured');
            $table->boolean('is_virtual')->default(false)->after('is_digital');
            $table->integer('sort_order')->default(0)->after('is_virtual');

            // Add SEO fields
            $table->string('meta_title')->nullable()->after('sort_order');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');

            // Add additional data fields
            $table->json('tags')->nullable()->after('meta_keywords');
            $table->json('variations')->nullable()->after('attributes');

            // Add shipping and tax
            $table->string('shipping_class')->nullable()->after('variations');
            $table->string('tax_class')->nullable()->after('shipping_class');

            // Add reviews and ratings
            $table->boolean('reviews_allowed')->default(true)->after('tax_class');
            $table->decimal('average_rating', 3, 1)->default(0)->after('reviews_allowed');
            $table->integer('review_count')->default(0)->after('average_rating');

            // Add sales tracking
            $table->integer('total_sales')->default(0)->after('review_count');

            // Add sale dates
            $table->datetime('date_on_sale_from')->nullable()->after('total_sales');
            $table->datetime('date_on_sale_to')->nullable()->after('date_on_sale_from');

            $table->softDeletes()->after('updated_at');

            // Add indexes
            $table->index(['category_id', 'is_active']);
            $table->index(['is_active', 'is_featured']);
            $table->index(['price', 'sale_price']);
            $table->index('stock_status');
            $table->index('sort_order');
            $table->index(['date_on_sale_from', 'date_on_sale_to']);

            // Add foreign key
            $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            if (Schema::hasColumn('products', 'category_id')) {
                $table->dropForeign(['category_id']);
            }
            $table->dropColumn([
                'category_id',
                'cost_price',
                'min_stock_level',
                'stock_status',
                'is_active',
                'is_featured',
                'is_digital',
                'is_virtual',
                'sort_order',
                'meta_title',
                'meta_description',
                'meta_keywords',
                'tags',
                'variations',
                'shipping_class',
                'tax_class',
                'reviews_allowed',
                'average_rating',
                'review_count',
                'total_sales',
                'date_on_sale_from',
                'date_on_sale_to'
            ]);
            $table->dropSoftDeletes();
            $table->string('status')->default('active');
            $table->boolean('featured')->default(false);
        });
    }
};
