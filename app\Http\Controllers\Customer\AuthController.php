<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class AuthController extends Controller
{
    /**
     * Show customer registration form
     */
    public function showRegister(): View
    {
        return view('customer.auth.register');
    }

    /**
     * Handle customer registration
     */
    public function register(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'company_name' => 'nullable|string|max:255',
            'terms' => 'required|accepted',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => 'customer',
            'phone' => $validated['phone'],
            'address' => $validated['address'],
            'city' => $validated['city'],
            'state' => $validated['state'],
            'postal_code' => $validated['postal_code'],
            'country' => $validated['country'],
            'company_name' => $validated['company_name'] ?? null,
            'is_active' => true,
        ]);

        Auth::login($user);

        return redirect()->route('customer.dashboard')
                        ->with('success', 'Welcome to Atrix Logistics! Your account has been created successfully.');
    }

    /**
     * Show customer login form
     */
    public function showLogin(): View
    {
        return view('customer.auth.login');
    }

    /**
     * Handle customer login
     */
    public function login(Request $request): RedirectResponse
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // Check if user is a customer
            if (Auth::user()->role !== 'customer') {
                Auth::logout();
                return back()->withErrors([
                    'email' => 'These credentials do not match our customer records.',
                ]);
            }

            return redirect()->intended(route('customer.dashboard'));
        }

        return back()->withErrors([
            'email' => 'The provided credentials do not match our records.',
        ])->onlyInput('email');
    }

    /**
     * Handle customer phone login (redirect to OTP)
     */
    public function loginWithPhone(Request $request): RedirectResponse
    {
        $request->validate([
            'phone' => 'required|string|min:10|max:15',
        ]);

        $phone = $request->phone;

        // Check if customer exists with this phone number
        $customer = User::where('phone', $phone)
                       ->where('role', 'customer')
                       ->first();

        if (!$customer) {
            return back()->withErrors([
                'phone' => 'No customer account found with this phone number.',
            ])->onlyInput('phone');
        }

        if (!$customer->is_active) {
            return back()->withErrors([
                'phone' => 'Your account has been deactivated. Please contact support.',
            ])->onlyInput('phone');
        }

        // Store phone in session for OTP verification
        $request->session()->put('otp_phone', $phone);

        return back()->with('status', 'Please check your phone for the OTP code.');
    }

    /**
     * Handle customer logout
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('customer.login')
                        ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show forgot password form
     */
    public function showForgotPassword(): View
    {
        return view('customer.auth.forgot-password');
    }

    /**
     * Handle forgot password
     */
    public function forgotPassword(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email|exists:users,email',
        ]);

        // Here you would typically send a password reset email
        // For now, we'll just show a success message

        return back()->with('success', 'Password reset instructions have been sent to your email address.');
    }
}
