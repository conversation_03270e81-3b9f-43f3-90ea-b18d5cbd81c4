<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Lookup - Atrix Logistics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .lookup-card {
            max-width: 500px;
            margin: 0 auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark header-gradient">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shipping-fast me-2"></i>
                Atrix Logistics
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('products.index') }}">Products</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('tracking.index') }}">Track Package</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('quotes.show') }}">Quote Lookup</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card lookup-card">
                    <div class="card-header header-gradient text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            Quote Lookup
                        </h3>
                        <p class="mb-0 mt-2">Enter your quote details to check status</p>
                    </div>
                    <div class="card-body p-4">
                        @if(session('success'))
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                        </div>
                        @endif

                        @if($errors->any())
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @foreach($errors->all() as $error)
                                <div>{{ $error }}</div>
                            @endforeach
                        </div>
                        @endif

                        <form method="GET" action="{{ route('quotes.show') }}">
                            <div class="mb-3">
                                <label for="quote_number" class="form-label">
                                    <i class="fas fa-hashtag me-2"></i>Quote Number
                                </label>
                                <input type="text" class="form-control form-control-lg" id="quote_number" 
                                       name="quote_number" placeholder="e.g., QTE20240115001" 
                                       value="{{ old('quote_number') }}" required>
                                <div class="form-text">
                                    Your quote number was provided in the confirmation email
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email" class="form-control form-control-lg" id="email" 
                                       name="email" placeholder="<EMAIL>" 
                                       value="{{ old('email') }}" required>
                                <div class="form-text">
                                    Enter the email address used when requesting the quote
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-search me-2"></i>
                                    Lookup Quote
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <h6 class="text-muted mb-3">Need help?</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-envelope me-1"></i>
                                        Email Support
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="tel:+1234567890" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-phone me-1"></i>
                                        Call Us
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information Cards -->
                <div class="row mt-5">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-clock fa-2x text-primary mb-3"></i>
                                <h6>Quick Response</h6>
                                <small class="text-muted">We respond to all quote requests within 24 hours</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-calculator fa-2x text-success mb-3"></i>
                                <h6>Competitive Pricing</h6>
                                <small class="text-muted">Get the best rates for your shipping needs</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 text-center">
                            <div class="card-body">
                                <i class="fas fa-shield-alt fa-2x text-info mb-3"></i>
                                <h6>Secure & Reliable</h6>
                                <small class="text-muted">Your quotes and data are protected</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 Atrix Logistics. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
