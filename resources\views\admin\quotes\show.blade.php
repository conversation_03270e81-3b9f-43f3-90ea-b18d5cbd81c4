@extends('layouts.admin')

@section('page-title', 'Quote Details - ' . $quote->quote_number)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.quotes.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Quotes
        </a>
        <a href="{{ route('admin.quotes.edit', $quote) }}" class="btn btn-outline-warning">
            <i class="fas fa-edit me-1"></i> Edit Quote
        </a>
        @if($quote->canBeQuoted())
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#provideQuoteModal">
            <i class="fas fa-dollar-sign me-1"></i> Provide Quote
        </button>
        @endif
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cogs me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li><button class="dropdown-item" onclick="printQuote()">
                    <i class="fas fa-print me-2"></i> Print Quote
                </button></li>
                <li><button class="dropdown-item" onclick="exportQuote()">
                    <i class="fas fa-download me-2"></i> Export PDF
                </button></li>
                <li><hr class="dropdown-divider"></li>
                <li><button class="dropdown-item text-danger" onclick="deleteQuote()">
                    <i class="fas fa-trash me-2"></i> Delete Quote
                </button></li>
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <!-- Quote Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1">
                                <i class="fas fa-file-invoice me-2"></i>
                                Quote #{{ $quote->quote_number }}
                            </h4>
                            <p class="text-muted mb-0">
                                Created on {{ $quote->created_at->format('M j, Y \a\t g:i A') }}
                                @if($quote->assignedTo)
                                    • Assigned to {{ $quote->assignedTo->name }}
                                @endif
                            </p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ $quote->status_badge_color }} fs-6 mb-2">
                                {{ $quote->formatted_status }}
                            </span>
                            <br>
                            <span class="badge bg-{{ $quote->priority_badge_color }}">
                                {{ $quote->formatted_priority }} Priority
                            </span>
                        </div>
                    </div>
                </div>
                
                @if($quote->isQuoted())
                <div class="card-body border-top bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="text-success mb-1">
                                <i class="fas fa-check-circle me-2"></i>
                                Quote Provided
                            </h5>
                            <p class="mb-0">
                                Quoted at ${{ number_format($quote->final_price ?? $quote->quoted_price, 2) }} {{ $quote->currency }}
                                @if($quote->expires_at)
                                    • Expires on {{ $quote->expires_at->format('M j, Y') }}
                                    @if($quote->getDaysUntilExpiry() <= 3)
                                        <span class="text-danger fw-bold">({{ $quote->getDaysUntilExpiry() }} days remaining)</span>
                                    @endif
                                @endif
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#statusModal">
                                    <i class="fas fa-edit me-1"></i> Update Status
                                </button>
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assignModal">
                                    <i class="fas fa-user me-1"></i> Assign
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quote Details -->
        <div class="col-lg-8">
            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Customer Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Name:</td>
                                    <td>{{ $quote->customer_name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Email:</td>
                                    <td><a href="mailto:{{ $quote->customer_email }}">{{ $quote->customer_email }}</a></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Phone:</td>
                                    <td>{{ $quote->customer_phone ?? 'Not provided' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                @if($quote->company_name)
                                <tr>
                                    <td class="fw-bold">Company:</td>
                                    <td>{{ $quote->company_name }}</td>
                                </tr>
                                @endif
                                @if($quote->user)
                                <tr>
                                    <td class="fw-bold">Customer Account:</td>
                                    <td>
                                        <a href="{{ route('admin.customers.show', $quote->user) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-external-link-alt me-1"></i> View Profile
                                        </a>
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Service Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Quote Type:</td>
                                    <td>{{ ucwords($quote->quote_type) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Service Type:</td>
                                    <td>{{ $quote->formatted_service_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Delivery Speed:</td>
                                    <td>{{ ucwords($quote->delivery_speed) }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                @if($quote->preferred_pickup_date)
                                <tr>
                                    <td class="fw-bold">Preferred Pickup:</td>
                                    <td>{{ $quote->preferred_pickup_date->format('M j, Y') }}</td>
                                </tr>
                                @endif
                                @if($quote->required_delivery_date)
                                <tr>
                                    <td class="fw-bold">Required Delivery:</td>
                                    <td>{{ $quote->required_delivery_date->format('M j, Y') }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="fw-bold">Special Services:</td>
                                    <td>
                                        @if($quote->insurance_required)
                                            <span class="badge bg-success me-1">Insurance</span>
                                        @endif
                                        @if($quote->signature_required)
                                            <span class="badge bg-info">Signature</span>
                                        @endif
                                        @if(!$quote->insurance_required && !$quote->signature_required)
                                            <span class="text-muted">None</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($quote->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $quote->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Shipping Details -->
            @if($quote->isShippingQuote())
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Shipping Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-arrow-up me-1"></i>
                                Origin
                            </h6>
                            <address class="mb-0">
                                {{ $quote->origin_address }}<br>
                                {{ $quote->origin_city }}, {{ $quote->origin_state }} {{ $quote->origin_postal_code }}<br>
                                {{ $quote->origin_country }}
                            </address>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-arrow-down me-1"></i>
                                Destination
                            </h6>
                            <address class="mb-0">
                                {{ $quote->destination_address }}<br>
                                {{ $quote->destination_city }}, {{ $quote->destination_state }} {{ $quote->destination_postal_code }}<br>
                                {{ $quote->destination_country }}
                            </address>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Package Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Package Count:</td>
                                    <td>{{ $quote->package_count }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Total Weight:</td>
                                    <td>{{ $quote->total_weight }} {{ $quote->weight_unit }}</td>
                                </tr>
                                @if($quote->dimensions)
                                <tr>
                                    <td class="fw-bold">Dimensions:</td>
                                    <td>
                                        {{ $quote->dimensions['length'] ?? 0 }} × 
                                        {{ $quote->dimensions['width'] ?? 0 }} × 
                                        {{ $quote->dimensions['height'] ?? 0 }} 
                                        {{ $quote->dimensions['unit'] ?? 'cm' }}
                                        @if($quote->total_volume)
                                            <br><small class="text-muted">Volume: {{ number_format($quote->total_volume, 2) }} {{ $quote->dimensions['unit'] ?? 'cm' }}³</small>
                                        @endif
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Package Type:</td>
                                    <td>{{ $quote->package_type ?? 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Declared Value:</td>
                                    <td>${{ number_format($quote->declared_value, 2) }} {{ $quote->currency }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Special Handling:</td>
                                    <td>
                                        @if($quote->fragile)
                                            <span class="badge bg-warning me-1">Fragile</span>
                                        @endif
                                        @if($quote->hazardous)
                                            <span class="badge bg-danger">Hazardous</span>
                                        @endif
                                        @if(!$quote->fragile && !$quote->hazardous)
                                            <span class="text-muted">None</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($quote->package_description)
                    <div class="mt-3">
                        <h6>Package Description:</h6>
                        <p class="text-muted">{{ $quote->package_description }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Product Information (for product quotes) -->
            @if($quote->isProductQuote() && $quote->products)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Products ({{ $quote->getProductsCount() }} items)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($quote->getProductsWithDetails() as $item)
                                <tr>
                                    <td>
                                        <strong>{{ $item['product']->name }}</strong>
                                        @if($item['notes'])
                                            <br><small class="text-muted">{{ $item['notes'] }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $item['quantity'] }}</td>
                                    <td>${{ number_format($item['price_at_time'], 2) }}</td>
                                    <td>${{ number_format($item['total'], 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <th colspan="3">Products Total:</th>
                                    <th>${{ number_format($quote->products_total, 2) }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Pricing Information -->
            @if($quote->isQuoted())
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        Pricing Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <span>Base Price:</span>
                        <span>${{ number_format($quote->quoted_price, 2) }}</span>
                    </div>
                    
                    @if($quote->discount_amount > 0)
                    <div class="d-flex justify-content-between text-success">
                        <span>Discount:</span>
                        <span>-${{ number_format($quote->discount_amount, 2) }}</span>
                    </div>
                    @endif
                    
                    <hr>
                    <div class="d-flex justify-content-between fw-bold fs-5">
                        <span>Final Price:</span>
                        <span class="text-primary">${{ number_format($quote->final_price ?? $quote->quoted_price, 2) }} {{ $quote->currency }}</span>
                    </div>
                    
                    @if($quote->pricing_breakdown)
                    <div class="mt-3">
                        <h6>Breakdown:</h6>
                        <small class="text-muted">{{ $quote->pricing_breakdown }}</small>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Quote Management -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Quote Management
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold">Status:</td>
                            <td>
                                <span class="badge bg-{{ $quote->status_badge_color }}">{{ $quote->formatted_status }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Priority:</td>
                            <td>
                                <span class="badge bg-{{ $quote->priority_badge_color }}">{{ $quote->formatted_priority }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Assigned To:</td>
                            <td>{{ $quote->assignedTo->name ?? 'Unassigned' }}</td>
                        </tr>
                        @if($quote->quoted_at)
                        <tr>
                            <td class="fw-bold">Quoted At:</td>
                            <td>{{ $quote->quoted_at->format('M j, Y g:i A') }}</td>
                        </tr>
                        @endif
                        @if($quote->expires_at)
                        <tr>
                            <td class="fw-bold">Expires At:</td>
                            <td>{{ $quote->expires_at->format('M j, Y g:i A') }}</td>
                        </tr>
                        @endif
                    </table>
                </div>
            </div>

            <!-- Admin Notes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        Notes
                    </h5>
                </div>
                <div class="card-body">
                    @if($quote->admin_notes)
                    <div class="mb-3">
                        <h6>Admin Notes:</h6>
                        <p class="text-muted">{{ $quote->admin_notes }}</p>
                    </div>
                    @endif
                    
                    @if($quote->customer_notes)
                    <div class="mb-3">
                        <h6>Customer Notes:</h6>
                        <p class="text-muted">{{ $quote->customer_notes }}</p>
                    </div>
                    @endif
                    
                    @if(!$quote->admin_notes && !$quote->customer_notes)
                    <p class="text-muted text-center">No notes available</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Provide Quote Modal -->
    @if($quote->canBeQuoted())
    <div class="modal fade" id="provideQuoteModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form id="provideQuoteForm">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title">Provide Quote</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quoted_price" class="form-label">Quoted Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="quoted_price" name="quoted_price"
                                               step="0.01" min="0" required>
                                        <span class="input-group-text">{{ $quote->currency }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discount_amount" class="form-label">Discount Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="discount_amount" name="discount_amount"
                                               step="0.01" min="0" value="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="expires_at" class="form-label">Quote Expires At *</label>
                            <input type="datetime-local" class="form-control" id="expires_at" name="expires_at"
                                   min="{{ now()->addDay()->format('Y-m-d\TH:i') }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="pricing_breakdown" class="form-label">Pricing Breakdown</label>
                            <textarea class="form-control" id="pricing_breakdown" name="pricing_breakdown" rows="3"
                                      placeholder="Optional: Explain how the price was calculated..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Admin Notes</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"
                                      placeholder="Internal notes about this quote...">{{ $quote->admin_notes }}</textarea>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Final Price:</strong> <span id="finalPriceDisplay">$0.00</span>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-dollar-sign me-1"></i> Provide Quote
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endif

    <!-- Update Status Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="statusForm">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title">Update Quote Status</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending" {{ $quote->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="reviewing" {{ $quote->status === 'reviewing' ? 'selected' : '' }}>Reviewing</option>
                                <option value="quoted" {{ $quote->status === 'quoted' ? 'selected' : '' }}>Quoted</option>
                                <option value="accepted" {{ $quote->status === 'accepted' ? 'selected' : '' }}>Accepted</option>
                                <option value="rejected" {{ $quote->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="expired" {{ $quote->status === 'expired' ? 'selected' : '' }}>Expired</option>
                                <option value="converted" {{ $quote->status === 'converted' ? 'selected' : '' }}>Converted</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="status_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="status_notes" name="notes" rows="3"
                                      placeholder="Optional notes about this status change..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Status
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Assign Modal -->
    <div class="modal fade" id="assignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="assignForm">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title">Assign Quote</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="assigned_to" class="form-label">Assign To *</label>
                            <select class="form-select" id="assigned_to" name="assigned_to" required>
                                <option value="">Select Admin...</option>
                                @foreach(\App\Models\User::where('role', 'admin')->get() as $admin)
                                    <option value="{{ $admin->id }}" {{ $quote->assigned_to == $admin->id ? 'selected' : '' }}>
                                        {{ $admin->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user me-1"></i> Assign Quote
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Calculate final price
function calculateFinalPrice() {
    const quotedPrice = parseFloat(document.getElementById('quoted_price').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    const finalPrice = quotedPrice - discountAmount;
    document.getElementById('finalPriceDisplay').textContent = '$' + finalPrice.toFixed(2);
}

// Event listeners for price calculation
document.getElementById('quoted_price')?.addEventListener('input', calculateFinalPrice);
document.getElementById('discount_amount')?.addEventListener('input', calculateFinalPrice);

// Provide Quote Form
document.getElementById('provideQuoteForm')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';

    fetch(`/admin/quotes/{{ $quote->id }}/provide-quote`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to provide quote'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while providing the quote');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Update Status Form
document.getElementById('statusForm')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Updating...';

    fetch(`/admin/quotes/{{ $quote->id }}/update-status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to update status'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the status');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Assign Form
document.getElementById('assignForm')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Assigning...';

    fetch(`/admin/quotes/{{ $quote->id }}/assign`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Failed to assign quote'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while assigning the quote');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// Print Quote
function printQuote() {
    window.print();
}

// Export Quote (placeholder)
function exportQuote() {
    alert('Export functionality coming soon!');
}

// Delete Quote
function deleteQuote() {
    if (confirm('Are you sure you want to delete this quote? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/quotes/{{ $quote->id }}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Set default expiry date
document.addEventListener('DOMContentLoaded', function() {
    const expiryInput = document.getElementById('expires_at');
    if (expiryInput && !expiryInput.value) {
        const defaultExpiry = new Date();
        defaultExpiry.setDate(defaultExpiry.getDate() + 7); // 7 days from now
        expiryInput.value = defaultExpiry.toISOString().slice(0, 16);
    }
});
</script>
@endpush
