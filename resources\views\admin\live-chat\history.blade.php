@extends('layouts.admin')

@section('page-title', 'Live Chat History')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.communications.live-chat.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Active Chats
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="exportHistory()">
            <i class="fas fa-download me-1"></i> Export History
        </button>
    </div>
@endsection

@section('content')
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Filter Chat History
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.communications.live-chat.history') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="Name, email, or session ID">
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="assigned_to" class="form-label">Assigned Staff</label>
                                <select class="form-select" id="assigned_to" name="assigned_to">
                                    <option value="">All Staff</option>
                                    @foreach($staffMembers as $staff)
                                        <option value="{{ $staff->id }}" {{ request('assigned_to') == $staff->id ? 'selected' : '' }}>
                                            {{ $staff->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i> Filter
                                </button>
                                <a href="{{ route('admin.communications.live-chat.history') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Closed Chat Sessions ({{ $closedSessions->total() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($closedSessions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Session ID</th>
                                        <th>Visitor</th>
                                        <th>Assigned Staff</th>
                                        <th>Messages</th>
                                        <th>Started</th>
                                        <th>Closed</th>
                                        <th>Duration</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($closedSessions as $session)
                                    <tr>
                                        <td>
                                            <code class="text-primary">{{ $session->session_id }}</code>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ $session->visitor_name ?? 'Anonymous' }}</strong>
                                                @if($session->visitor_email)
                                                    <br><small class="text-muted">{{ $session->visitor_email }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($session->assignedStaff)
                                                <span class="badge bg-info">{{ $session->assignedStaff->name }}</span>
                                            @else
                                                <span class="badge bg-secondary">Unassigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $session->total_messages }}</span>
                                            @if($session->messages->first())
                                                <br><small class="text-muted">
                                                    "{{ Str::limit($session->messages->first()->message, 30) }}"
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <small>
                                                {{ $session->created_at->format('M j, Y') }}<br>
                                                {{ $session->created_at->format('g:i A') }}
                                            </small>
                                        </td>
                                        <td>
                                            <small>
                                                {{ $session->updated_at->format('M j, Y') }}<br>
                                                {{ $session->updated_at->format('g:i A') }}
                                            </small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ $session->created_at->diffInMinutes($session->updated_at) }} min
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ route('admin.communications.live-chat.show', $session) }}"
                                                   class="btn btn-outline-primary" title="View Conversation">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-info"
                                                        onclick="showSessionDetails({{ $session->id }})" title="Session Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success"
                                                        onclick="reopenSession({{ $session->id }})" title="Reopen Chat">
                                                    <i class="fas fa-redo"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <small class="text-muted">
                                    Showing {{ $closedSessions->firstItem() }} to {{ $closedSessions->lastItem() }} 
                                    of {{ $closedSessions->total() }} results
                                </small>
                            </div>
                            <div>
                                {{ $closedSessions->appends(request()->query())->links() }}
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Closed Chat Sessions Found</h5>
                            <p class="text-muted">
                                @if(request()->hasAny(['search', 'date_from', 'date_to', 'assigned_to']))
                                    No sessions match your current filters. Try adjusting your search criteria.
                                @else
                                    Closed chat sessions will appear here once conversations are completed.
                                @endif
                            </p>
                            @if(request()->hasAny(['search', 'date_from', 'date_to', 'assigned_to']))
                                <a href="{{ route('admin.communications.live-chat.history') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-times me-1"></i> Clear Filters
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Session Details Modal -->
    <div class="modal fade" id="sessionDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="sessionDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="viewConversationBtn" style="display: none;">
                        <i class="fas fa-eye me-1"></i> View Full Conversation
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function showSessionDetails(sessionId) {
    // Show loading state
    document.getElementById('sessionDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-3 text-primary"></i>
            <p class="text-muted">Loading session details...</p>
        </div>
    `;

    // Hide view conversation button initially
    document.getElementById('viewConversationBtn').style.display = 'none';

    const modal = new bootstrap.Modal(document.getElementById('sessionDetailsModal'));
    modal.show();

    // Fetch session details
    fetch(`/admin/communications/live-chat/sessions/${sessionId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySessionDetails(data.data);
            } else {
                showError('Failed to load session details');
            }
        })
        .catch(error => {
            console.error('Error fetching session details:', error);
            showError('Error loading session details');
        });
}

function displaySessionDetails(data) {
    const session = data.session;
    const stats = data.statistics;
    const browserInfo = data.browser_info;

    // Show view conversation button
    const viewBtn = document.getElementById('viewConversationBtn');
    viewBtn.style.display = 'inline-block';
    viewBtn.onclick = () => {
        window.open(`/admin/communications/live-chat/sessions/${session.id}`, '_blank');
    };

    const content = `
        <div class="row">
            <!-- Session Information -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Session Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">Session ID:</td>
                                <td><code>${session.session_id}</code></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Visitor:</td>
                                <td>${session.visitor_name}</td>
                            </tr>
                            ${session.visitor_email ? `
                            <tr>
                                <td class="fw-bold">Email:</td>
                                <td><a href="mailto:${session.visitor_email}">${session.visitor_email}</a></td>
                            </tr>
                            ` : ''}
                            <tr>
                                <td class="fw-bold">Status:</td>
                                <td><span class="badge bg-secondary">${session.status.charAt(0).toUpperCase() + session.status.slice(1)}</span></td>
                            </tr>
                            ${session.assigned_staff ? `
                            <tr>
                                <td class="fw-bold">Assigned Staff:</td>
                                <td><span class="badge bg-info">${session.assigned_staff}</span></td>
                            </tr>
                            ` : ''}
                            <tr>
                                <td class="fw-bold">Started:</td>
                                <td>${session.created_at}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Ended:</td>
                                <td>${session.updated_at}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Duration:</td>
                                <td><strong>${session.duration}</strong></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">IP Address:</td>
                                <td><code>${session.visitor_ip || 'Not recorded'}</code></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Chat Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="border-end">
                                    <h4 class="text-primary mb-0">${stats.total_messages}</h4>
                                    <small class="text-muted">Total Messages</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h4 class="text-success mb-0">${stats.visitor_messages}</h4>
                                    <small class="text-muted">Visitor</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h4 class="text-info mb-0">${stats.staff_messages}</h4>
                                <small class="text-muted">Staff</small>
                            </div>
                        </div>

                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">Avg Response Time:</td>
                                <td><span class="badge bg-primary">${stats.avg_response_time} min</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">First Response:</td>
                                <td><span class="badge bg-success">${stats.first_response_time} min</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Longest Response:</td>
                                <td><span class="badge bg-warning">${stats.longest_response_time} min</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Browser Information -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-globe me-2"></i>
                            Browser & Device Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Browser:</strong><br>
                                <span class="badge bg-light text-dark">${browserInfo.browser}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Platform:</strong><br>
                                <span class="badge bg-light text-dark">${browserInfo.platform}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Device:</strong><br>
                                <span class="badge bg-light text-dark">${browserInfo.device}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>User Agent:</strong><br>
                                <small class="text-muted" title="${browserInfo.full_user_agent}">
                                    ${browserInfo.full_user_agent ? browserInfo.full_user_agent.substring(0, 50) + '...' : 'Not available'}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Timeline Preview -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Message Timeline (Last 5 messages)
                        </h6>
                        <small class="text-muted">${stats.total_messages} total messages</small>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            ${data.timeline.slice(-5).map(message => `
                                <div class="timeline-item ${message.sender_type === 'visitor' ? 'visitor' : 'staff'}">
                                    <div class="timeline-marker ${message.sender_type === 'visitor' ? 'bg-primary' : 'bg-success'}">
                                        <i class="fas ${message.sender_type === 'visitor' ? 'fa-user' : 'fa-headset'}"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <strong>${message.sender_name}</strong>
                                            <small class="text-muted">${message.time_ago}</small>
                                        </div>
                                        <p class="mb-1">${message.message}</p>
                                        <small class="text-muted">${message.created_at}</small>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        ${stats.total_messages > 5 ? `
                            <div class="text-center mt-3">
                                <small class="text-muted">Showing last 5 of ${stats.total_messages} messages</small>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('sessionDetailsContent').innerHTML = content;
}

function showError(message) {
    document.getElementById('sessionDetailsContent').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

function exportHistory() {
    // TODO: Implement export functionality
    alert('Export functionality coming soon!');
}

// Reopen closed session
function reopenSession(sessionId) {
    if (confirm('Are you sure you want to reopen this chat session? This will make it active again and assign it to you.')) {
        fetch(`/admin/communications/live-chat/sessions/${sessionId}/reopen`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert alert at the top of the page
                const container = document.querySelector('.container-fluid');
                container.insertBefore(alertDiv, container.firstChild);

                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);

                // Optionally redirect to active chats or refresh the page
                setTimeout(() => {
                    window.location.href = '{{ route("admin.communications.live-chat.index") }}';
                }, 2000);
            } else {
                alert('Error: ' + (data.message || 'Failed to reopen chat session'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reopening chat session');
        });
    }
}

// Auto-submit form when date inputs change
document.getElementById('date_from').addEventListener('change', function() {
    if (this.value && document.getElementById('date_to').value) {
        this.form.submit();
    }
});

document.getElementById('date_to').addEventListener('change', function() {
    if (this.value && document.getElementById('date_from').value) {
        this.form.submit();
    }
});
</script>
@endpush

@push('styles')
<style>
/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
    margin-left: 15px;
}

.timeline-item.visitor .timeline-content {
    border-left-color: #007bff;
}

.timeline-item.staff .timeline-content {
    border-left-color: #28a745;
}

/* Modal Enhancements */
.modal-lg {
    max-width: 900px;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.table-borderless td {
    padding: 0.5rem 0;
    border: none;
}

.badge {
    font-size: 0.75em;
}

/* Statistics Cards */
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 10px auto;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -15px;
        width: 24px;
        height: 24px;
        font-size: 10px;
    }

    .timeline-content {
        margin-left: 10px;
        padding: 10px;
    }
}
</style>
@endpush
