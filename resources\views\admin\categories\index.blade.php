@extends('layouts.admin')

@section('title', 'Categories')
@section('page-title', 'Product Categories')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Category
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i> Actions
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="toggleSortMode()">
                <i class="fas fa-sort me-2"></i> Reorder Categories
            </a></li>
            <li><a class="dropdown-item" href="{{ route('admin.categories.tree') }}">
                <i class="fas fa-sitemap me-2"></i> Category Tree
            </a></li>
        </ul>
    </div>
@endsection

@section('content')
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $categories->total() }}</h4>
                            <p class="mb-0">Total Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $categories->where('is_active', true)->count() }}</h4>
                            <p class="mb-0">Active Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $categories->where('parent_id', null)->count() }}</h4>
                            <p class="mb-0">Root Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ $categories->sum('products_count') }}</h4>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    Categories
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                        <i class="fas fa-expand-arrows-alt me-1"></i> Expand All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                        <i class="fas fa-compress-arrows-alt me-1"></i> Collapse All
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            @if($categories->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover" id="categoriesTable">
                        <thead>
                            <tr>
                                <th width="40%">Category</th>
                                <th width="15%">Products</th>
                                <th width="10%">Status</th>
                                <th width="10%">Featured</th>
                                <th width="10%">Order</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-categories">
                            @foreach($categories as $category)
                                <tr data-id="{{ $category->id }}" class="category-row">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($category->children->count() > 0)
                                                <button class="btn btn-sm btn-link p-0 me-2 toggle-children" 
                                                        data-category="{{ $category->id }}">
                                                    <i class="fas fa-chevron-right"></i>
                                                </button>
                                            @else
                                                <span class="me-4"></span>
                                            @endif
                                            
                                            @if($category->image)
                                                <img src="{{ Storage::url($category->image) }}" 
                                                     alt="{{ $category->name }}" 
                                                     class="rounded me-2" 
                                                     style="width: 32px; height: 32px; object-fit: cover;">
                                            @elseif($category->icon)
                                                <i class="{{ $category->icon }} fa-lg me-2 text-primary"></i>
                                            @else
                                                <i class="fas fa-folder fa-lg me-2 text-muted"></i>
                                            @endif
                                            
                                            <div>
                                                <strong>{{ $category->name }}</strong>
                                                @if($category->description)
                                                    <br><small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                                @endif
                                                <br><small class="text-info">{{ $category->slug }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $category->products_count }}</span>
                                        @if($category->products_count > 0)
                                            <a href="{{ route('admin.products.index', ['category' => $category->id]) }}" 
                                               class="btn btn-sm btn-link p-0 ms-1">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        @endif
                                    </td>
                                    <td>
                                        <form method="POST" action="/admin/ecommerce/categories/{{ $category->id }}/toggle-status" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                @if($category->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        @if($category->is_featured)
                                            <span class="badge bg-warning">Featured</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $category->sort_order }}</span>
                                        <i class="fas fa-grip-vertical text-muted ms-2 sort-handle" style="cursor: move;"></i>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.categories.show', $category) }}" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.categories.edit', $category) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCategory({{ $category->id }}, '{{ $category->name }}')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                @foreach($category->children as $child)
                                    <tr data-id="{{ $child->id }}" class="category-row child-category" 
                                        data-parent="{{ $category->id }}" style="display: none;">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2 ms-4">└─</span>
                                                
                                                @if($child->image)
                                                    <img src="{{ Storage::url($child->image) }}" 
                                                         alt="{{ $child->name }}" 
                                                         class="rounded me-2" 
                                                         style="width: 24px; height: 24px; object-fit: cover;">
                                                @elseif($child->icon)
                                                    <i class="{{ $child->icon }} me-2 text-primary"></i>
                                                @else
                                                    <i class="fas fa-folder me-2 text-muted"></i>
                                                @endif
                                                
                                                <div>
                                                    <strong>{{ $child->name }}</strong>
                                                    @if($child->description)
                                                        <br><small class="text-muted">{{ Str::limit($child->description, 40) }}</small>
                                                    @endif
                                                    <br><small class="text-info">{{ $child->slug }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $child->products_count }}</span>
                                        </td>
                                        <td>
                                            @if($child->is_active)
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-secondary">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($child->is_featured)
                                                <span class="badge bg-warning">Featured</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $child->sort_order }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.categories.show', $child) }}" 
                                                   class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.categories.edit', $child) }}" 
                                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteCategory({{ $child->id }}, '{{ $child->name }}')" 
                                                        title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    {{ $categories->links('pagination.admin') }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No categories found</h5>
                    <p class="text-muted">Start by creating your first product category.</p>
                    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Category
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Category
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the category <strong id="deleteCategoryName"></strong>?</p>
                    <p class="text-muted">This action cannot be undone. All products in this category will need to be reassigned.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" id="deleteForm" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    // Toggle child categories
    document.querySelectorAll('.toggle-children').forEach(button => {
        button.addEventListener('click', function() {
            const categoryId = this.dataset.category;
            const childRows = document.querySelectorAll(`[data-parent="${categoryId}"]`);
            const icon = this.querySelector('i');
            
            childRows.forEach(row => {
                if (row.style.display === 'none') {
                    row.style.display = '';
                    icon.classList.remove('fa-chevron-right');
                    icon.classList.add('fa-chevron-down');
                } else {
                    row.style.display = 'none';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            });
        });
    });

    // Expand all categories
    function expandAll() {
        document.querySelectorAll('.child-category').forEach(row => {
            row.style.display = '';
        });
        document.querySelectorAll('.toggle-children i').forEach(icon => {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-down');
        });
    }

    // Collapse all categories
    function collapseAll() {
        document.querySelectorAll('.child-category').forEach(row => {
            row.style.display = 'none';
        });
        document.querySelectorAll('.toggle-children i').forEach(icon => {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
        });
    }

    // Delete category
    function deleteCategory(categoryId, categoryName) {
        document.getElementById('deleteCategoryName').textContent = categoryName;
        document.getElementById('deleteForm').action = `/admin/ecommerce/categories/${categoryId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Sortable functionality
    let sortable;
    function toggleSortMode() {
        if (sortable) {
            sortable.destroy();
            sortable = null;
            document.querySelectorAll('.sort-handle').forEach(handle => {
                handle.style.display = 'none';
            });
        } else {
            sortable = Sortable.create(document.getElementById('sortable-categories'), {
                handle: '.sort-handle',
                animation: 150,
                onEnd: function(evt) {
                    const orders = [];
                    document.querySelectorAll('.category-row').forEach((row, index) => {
                        orders.push(row.dataset.id);
                    });
                    
                    // Send AJAX request to update order
                    fetch('/admin/ecommerce/categories/update-order', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ orders: orders })
                    });
                }
            });
            
            document.querySelectorAll('.sort-handle').forEach(handle => {
                handle.style.display = 'inline';
            });
        }
    }
</script>
@endpush
