<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Slider;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;

class SliderController extends Controller
{
    /**
     * Display a listing of sliders
     */
    public function index(): View
    {
        $sliders = Slider::orderBy('sort_order')->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.sliders.index', compact('sliders'));
    }

    /**
     * Show the form for creating a new slider
     */
    public function create(): View
    {
        return view('admin.sliders.create');
    }

    /**
     * Store a newly created slider
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url|max:500',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'mobile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
        ]);

        $validated['is_active'] = $request->has('is_active');

        // Handle main image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('uploads/sliders', 'public');
        }

        // Handle mobile image upload
        if ($request->hasFile('mobile_image')) {
            $validated['mobile_image'] = $request->file('mobile_image')->store('uploads/sliders', 'public');
        }

        // Set sort order if not provided
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = Slider::max('sort_order') + 1;
        }

        Slider::create($validated);

        return redirect()->route('admin.cms.sliders.index')
                        ->with('success', 'Slider created successfully.');
    }

    /**
     * Display the specified slider
     */
    public function show(Slider $slider): View
    {
        return view('admin.sliders.show', compact('slider'));
    }

    /**
     * Show the form for editing the specified slider
     */
    public function edit(Slider $slider): View
    {
        return view('admin.sliders.edit', compact('slider'));
    }

    /**
     * Update the specified slider
     */
    public function update(Request $request, Slider $slider): RedirectResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'subtitle' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url|max:500',
            'display_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'mobile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
        ]);

        $validated['is_active'] = $request->has('is_active');

        // Handle main image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($slider->image && Storage::disk('public')->exists($slider->image)) {
                Storage::disk('public')->delete($slider->image);
            }

            $validated['image'] = $request->file('image')->store('uploads/sliders', 'public');
        }

        // Handle mobile image upload
        if ($request->hasFile('mobile_image')) {
            // Delete old mobile image
            if ($slider->mobile_image && Storage::disk('public')->exists($slider->mobile_image)) {
                Storage::disk('public')->delete($slider->mobile_image);
            }

            $validated['mobile_image'] = $request->file('mobile_image')->store('uploads/sliders', 'public');
        }

        $slider->update($validated);

        return redirect()->route('admin.cms.sliders.show', $slider)
                        ->with('success', 'Slider updated successfully.');
    }

    /**
     * Remove the specified slider
     */
    public function destroy(Slider $slider): RedirectResponse
    {
        // Delete images if they exist
        if ($slider->image && Storage::disk('public')->exists($slider->image)) {
            Storage::disk('public')->delete($slider->image);
        }

        if ($slider->mobile_image && Storage::disk('public')->exists($slider->mobile_image)) {
            Storage::disk('public')->delete($slider->mobile_image);
        }

        $slider->delete();

        return redirect()->route('admin.cms.sliders.index')
                        ->with('success', 'Slider deleted successfully.');
    }

    /**
     * Update sliders display order
     */
    public function updateOrder(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'orders' => 'required|array',
            'orders.*' => 'integer|exists:sliders,id',
        ]);

        foreach ($validated['orders'] as $order => $id) {
            Slider::where('id', $id)->update(['sort_order' => $order + 1]);
        }

        return redirect()->route('admin.cms.sliders.index')
                        ->with('success', 'Sliders order updated successfully.');
    }

    /**
     * Toggle slider active status
     */
    public function toggleStatus(Slider $slider): RedirectResponse
    {
        $slider->update(['is_active' => !$slider->is_active]);

        $status = $slider->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Slider {$status} successfully.");
    }

    /**
     * Duplicate a slider
     */
    public function duplicate(Slider $slider): RedirectResponse
    {
        $newSlider = $slider->replicate();
        $newSlider->title = $slider->title . ' (Copy)';
        $newSlider->is_active = false;
        $newSlider->sort_order = Slider::max('sort_order') + 1;
        $newSlider->save();

        return redirect()->route('admin.cms.sliders.edit', $newSlider)
                        ->with('success', 'Slider duplicated successfully. Please update the images.');
    }
}
