<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main categories
        $categories = $this->createCategories();
        
        // Create products for each category
        $this->createShippingContainers($categories['shipping_containers']);
        $this->createSpareParts($categories['spare_parts']);
        $this->createBoxes($categories['boxes']);
        $this->createSteel($categories['steel']);
    }

    /**
     * Create product categories
     */
    private function createCategories(): array
    {
        $categories = [];

        // Shipping Containers
        $categories['shipping_containers'] = Category::firstOrCreate(
            ['slug' => 'shipping-containers'],
            [
            'name' => 'Shipping Containers',
            'slug' => 'shipping-containers',
            'description' => 'New and used shipping containers for storage and transportation',
            'icon' => 'fas fa-shipping-fast',
            'sort_order' => 1,
            'is_active' => true,
            'is_featured' => true,
            'meta_title' => 'Shipping Containers - Storage & Transportation Solutions',
            'meta_description' => 'High-quality shipping containers for all your storage and transportation needs.',
        ]);

        // Container subcategories
        Category::create([
            'name' => 'Standard Containers',
            'slug' => 'standard-containers',
            'description' => '20ft and 40ft standard shipping containers',
            'parent_id' => $categories['shipping_containers']->id,
            'sort_order' => 1,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Refrigerated Containers',
            'slug' => 'refrigerated-containers',
            'description' => 'Temperature-controlled reefer containers',
            'parent_id' => $categories['shipping_containers']->id,
            'sort_order' => 2,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Specialized Containers',
            'slug' => 'specialized-containers',
            'description' => 'Open-top, flat-rack, and custom containers',
            'parent_id' => $categories['shipping_containers']->id,
            'sort_order' => 3,
            'is_active' => true,
        ]);

        // Spare Parts
        $categories['spare_parts'] = Category::create([
            'name' => 'Spare Parts',
            'slug' => 'spare-parts',
            'description' => 'Container and logistics equipment spare parts',
            'icon' => 'fas fa-cogs',
            'sort_order' => 2,
            'is_active' => true,
            'is_featured' => true,
            'meta_title' => 'Spare Parts - Container & Logistics Equipment',
            'meta_description' => 'Quality spare parts for containers, trucks, and logistics equipment.',
        ]);

        // Spare parts subcategories
        Category::create([
            'name' => 'Container Parts',
            'slug' => 'container-parts',
            'description' => 'Door seals, locks, hinges, and container hardware',
            'parent_id' => $categories['spare_parts']->id,
            'sort_order' => 1,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Truck Parts',
            'slug' => 'truck-parts',
            'description' => 'Truck and trailer spare parts',
            'parent_id' => $categories['spare_parts']->id,
            'sort_order' => 2,
            'is_active' => true,
        ]);

        // Boxes & Packaging
        $categories['boxes'] = Category::create([
            'name' => 'Boxes & Packaging',
            'slug' => 'boxes-packaging',
            'description' => 'Cardboard boxes, crates, and packaging materials',
            'icon' => 'fas fa-box',
            'sort_order' => 3,
            'is_active' => true,
            'is_featured' => true,
            'meta_title' => 'Boxes & Packaging Materials - Shipping Supplies',
            'meta_description' => 'Durable boxes and packaging materials for safe shipping and storage.',
        ]);

        // Box subcategories
        Category::create([
            'name' => 'Cardboard Boxes',
            'slug' => 'cardboard-boxes',
            'description' => 'Various sizes of corrugated cardboard boxes',
            'parent_id' => $categories['boxes']->id,
            'sort_order' => 1,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Wooden Crates',
            'slug' => 'wooden-crates',
            'description' => 'Heavy-duty wooden shipping crates',
            'parent_id' => $categories['boxes']->id,
            'sort_order' => 2,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Plastic Containers',
            'slug' => 'plastic-containers',
            'description' => 'Reusable plastic storage and shipping containers',
            'parent_id' => $categories['boxes']->id,
            'sort_order' => 3,
            'is_active' => true,
        ]);

        // Steel Products
        $categories['steel'] = Category::create([
            'name' => 'Steel Products',
            'slug' => 'steel-products',
            'description' => 'Steel beams, plates, and structural materials',
            'icon' => 'fas fa-industry',
            'sort_order' => 4,
            'is_active' => true,
            'is_featured' => true,
            'meta_title' => 'Steel Products - Structural Steel & Materials',
            'meta_description' => 'High-grade steel products for construction and industrial applications.',
        ]);

        // Steel subcategories
        Category::create([
            'name' => 'Steel Beams',
            'slug' => 'steel-beams',
            'description' => 'I-beams, H-beams, and structural steel beams',
            'parent_id' => $categories['steel']->id,
            'sort_order' => 1,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Steel Plates',
            'slug' => 'steel-plates',
            'description' => 'Flat steel plates in various thicknesses',
            'parent_id' => $categories['steel']->id,
            'sort_order' => 2,
            'is_active' => true,
        ]);

        Category::create([
            'name' => 'Steel Pipes',
            'slug' => 'steel-pipes',
            'description' => 'Round and square steel pipes and tubes',
            'parent_id' => $categories['steel']->id,
            'sort_order' => 3,
            'is_active' => true,
        ]);

        return $categories;
    }

    /**
     * Create shipping container products
     */
    private function createShippingContainers($category): void
    {
        $containers = [
            [
                'name' => '20ft Standard Dry Container - New',
                'sku' => 'CONT-20-STD-NEW',
                'description' => 'Brand new 20-foot standard dry cargo container. Perfect for storage and shipping. Features weatherproof construction, secure locking mechanisms, and standard ISO dimensions.',
                'short_description' => 'New 20ft standard shipping container for storage and cargo transport.',
                'price' => 3500.00,
                'sale_price' => 3200.00,
                'cost_price' => 2800.00,
                'weight' => 2300.00,
                'dimensions' => [
                    'length' => 6058, // mm
                    'width' => 2438,
                    'height' => 2591,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 15,
                'min_stock_level' => 3,
                'is_featured' => true,
                'attributes' => [
                    'condition' => 'New',
                    'size' => '20ft',
                    'type' => 'Dry Cargo',
                    'material' => 'Corten Steel',
                    'door_type' => 'Standard Swing',
                    'payload' => '28,230 kg',
                    'cubic_capacity' => '33.2 m³'
                ],

            ],
            [
                'name' => '40ft Standard Dry Container - New',
                'sku' => 'CONT-40-STD-NEW',
                'description' => 'Brand new 40-foot standard dry cargo container. Ideal for large shipments and storage applications. Built to ISO standards with superior durability.',
                'short_description' => 'New 40ft standard shipping container for large cargo and storage needs.',
                'price' => 4500.00,
                'cost_price' => 3800.00,
                'weight' => 3740.00,
                'dimensions' => [
                    'length' => 12192,
                    'width' => 2438,
                    'height' => 2591,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 12,
                'min_stock_level' => 2,
                'is_featured' => true,
                'attributes' => [
                    'condition' => 'New',
                    'size' => '40ft',
                    'type' => 'Dry Cargo',
                    'material' => 'Corten Steel',
                    'door_type' => 'Standard Swing',
                    'payload' => '26,740 kg',
                    'cubic_capacity' => '67.7 m³'
                ]
            ],
            [
                'name' => '20ft Refrigerated Container (Reefer)',
                'sku' => 'CONT-20-REEF-NEW',
                'description' => 'Temperature-controlled 20ft refrigerated container perfect for perishable goods. Features advanced cooling system with precise temperature control.',
                'short_description' => 'Temperature-controlled 20ft reefer container for perishable cargo.',
                'price' => 12500.00,
                'sale_price' => 11800.00,
                'cost_price' => 9500.00,
                'weight' => 3080.00,
                'dimensions' => [
                    'length' => 6058,
                    'width' => 2438,
                    'height' => 2591,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 5,
                'min_stock_level' => 1,
                'is_featured' => true,
                'attributes' => [
                    'condition' => 'New',
                    'size' => '20ft',
                    'type' => 'Refrigerated',
                    'temperature_range' => '-25°C to +25°C',
                    'power_supply' => '380-460V/50-60Hz',
                    'refrigerant' => 'R134a',
                    'cubic_capacity' => '28.1 m³'
                ]
            ],
            [
                'name' => '20ft Open Top Container',
                'sku' => 'CONT-20-OPEN-NEW',
                'description' => 'Open-top container for oversized cargo that cannot fit through standard doors. Features removable tarpaulin roof and swing doors.',
                'short_description' => 'Open-top 20ft container for oversized cargo loading.',
                'price' => 4200.00,
                'cost_price' => 3400.00,
                'weight' => 2360.00,
                'dimensions' => [
                    'length' => 6058,
                    'width' => 2438,
                    'height' => 2591,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 8,
                'min_stock_level' => 2,
                'attributes' => [
                    'condition' => 'New',
                    'size' => '20ft',
                    'type' => 'Open Top',
                    'roof_type' => 'Removable Tarpaulin',
                    'loading_method' => 'Top & End',
                    'cubic_capacity' => '32.4 m³'
                ]
            ],
            [
                'name' => '20ft Standard Container - Used (Grade A)',
                'sku' => 'CONT-20-STD-USED-A',
                'description' => 'High-quality used 20ft container in excellent condition. Thoroughly inspected and certified cargo-worthy. Great value for storage applications.',
                'short_description' => 'Grade A used 20ft container in excellent condition.',
                'price' => 2200.00,
                'sale_price' => 1950.00,
                'cost_price' => 1600.00,
                'weight' => 2300.00,
                'dimensions' => [
                    'length' => 6058,
                    'width' => 2438,
                    'height' => 2591,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 25,
                'min_stock_level' => 5,
                'attributes' => [
                    'condition' => 'Used - Grade A',
                    'size' => '20ft',
                    'type' => 'Dry Cargo',
                    'certification' => 'Cargo Worthy',
                    'age' => '8-12 years',
                    'cubic_capacity' => '33.2 m³'
                ]
            ]
        ];

        foreach ($containers as $containerData) {
            Product::create(array_merge($containerData, [
                'category_id' => $category->id,
                'slug' => Str::slug($containerData['name']),
                'manage_stock' => true,
                'stock_status' => 'in_stock',
                'is_active' => true,
                'shipping_class' => 'heavy',
                'reviews_allowed' => true,
                'meta_title' => $containerData['name'] . ' - Atrix Logistics',
                'meta_description' => $containerData['short_description'],
            ]));
        }
    }

    /**
     * Create spare parts products
     */
    private function createSpareParts($category): void
    {
        $spareParts = [
            [
                'name' => 'Container Door Seal Kit',
                'sku' => 'SPARE-DOOR-SEAL-KIT',
                'description' => 'Complete door seal replacement kit for shipping containers. Includes rubber seals, gaskets, and installation hardware. Weather-resistant and durable.',
                'short_description' => 'Complete door seal kit for container weatherproofing.',
                'price' => 85.00,
                'sale_price' => 75.00,
                'cost_price' => 45.00,
                'weight' => 2.5,
                'dimensions' => [
                    'length' => 300,
                    'width' => 200,
                    'height' => 100,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 150,
                'min_stock_level' => 25,
                'is_featured' => true,
                'attributes' => [
                    'material' => 'EPDM Rubber',
                    'color' => 'Black',
                    'temperature_range' => '-40°C to +120°C',
                    'compatibility' => 'All standard containers',
                    'installation_time' => '2-3 hours'
                ]
            ],
            [
                'name' => 'Container Door Lock Set',
                'sku' => 'SPARE-DOOR-LOCK-SET',
                'description' => 'Heavy-duty container door lock mechanism. Includes cam locks, handles, and security hardware. Marine-grade steel construction.',
                'short_description' => 'Heavy-duty door lock set for container security.',
                'price' => 125.00,
                'cost_price' => 75.00,
                'weight' => 8.5,
                'dimensions' => [
                    'length' => 400,
                    'width' => 300,
                    'height' => 150,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 80,
                'min_stock_level' => 15,
                'attributes' => [
                    'material' => 'Marine Grade Steel',
                    'finish' => 'Galvanized',
                    'lock_type' => 'Cam Lock',
                    'security_rating' => 'High',
                    'keys_included' => '2'
                ]
            ],
            [
                'name' => 'Container Corner Casting',
                'sku' => 'SPARE-CORNER-CAST',
                'description' => 'ISO standard corner casting for container repairs. High-strength steel construction meeting international shipping standards.',
                'short_description' => 'ISO standard corner casting for container structural repair.',
                'price' => 45.00,
                'cost_price' => 28.00,
                'weight' => 12.0,
                'dimensions' => [
                    'length' => 178,
                    'width' => 162,
                    'height' => 118,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 200,
                'min_stock_level' => 40,
                'attributes' => [
                    'material' => 'Cast Steel',
                    'standard' => 'ISO 1161',
                    'strength' => '45 tons',
                    'finish' => 'Shot Blasted',
                    'certification' => 'Lloyd\'s Register'
                ]
            ],
            [
                'name' => 'Truck Trailer Brake Pad Set',
                'sku' => 'SPARE-BRAKE-PAD-SET',
                'description' => 'High-performance brake pad set for heavy-duty trucks and trailers. Excellent stopping power and heat dissipation.',
                'short_description' => 'Heavy-duty brake pad set for trucks and trailers.',
                'price' => 180.00,
                'sale_price' => 165.00,
                'cost_price' => 110.00,
                'weight' => 6.8,
                'dimensions' => [
                    'length' => 250,
                    'width' => 200,
                    'height' => 80,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 60,
                'min_stock_level' => 12,
                'attributes' => [
                    'material' => 'Ceramic Composite',
                    'application' => 'Heavy Duty Trucks',
                    'temperature_rating' => '600°C',
                    'noise_level' => 'Low',
                    'warranty' => '12 months'
                ]
            ],
            [
                'name' => 'Container Hinge Pin Set',
                'sku' => 'SPARE-HINGE-PIN-SET',
                'description' => 'Stainless steel hinge pin set for container doors. Corrosion-resistant and designed for marine environments.',
                'short_description' => 'Stainless steel hinge pins for container door maintenance.',
                'price' => 35.00,
                'cost_price' => 20.00,
                'weight' => 1.2,
                'dimensions' => [
                    'length' => 200,
                    'width' => 50,
                    'height' => 50,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 300,
                'min_stock_level' => 50,
                'attributes' => [
                    'material' => 'Stainless Steel 316',
                    'diameter' => '16mm',
                    'length' => '180mm',
                    'corrosion_resistance' => 'Marine Grade',
                    'quantity_per_set' => '4 pins'
                ]
            ]
        ];

        foreach ($spareParts as $partData) {
            Product::create(array_merge($partData, [
                'category_id' => $category->id,
                'slug' => Str::slug($partData['name']),
                'manage_stock' => true,
                'stock_status' => 'in_stock',
                'is_active' => true,
                'shipping_class' => 'standard',
                'reviews_allowed' => true,
                'meta_title' => $partData['name'] . ' - Atrix Logistics',
                'meta_description' => $partData['short_description'],
            ]));
        }
    }

    /**
     * Create boxes and packaging products
     */
    private function createBoxes($category): void
    {
        $boxes = [
            [
                'name' => 'Small Cardboard Box (30x20x15cm)',
                'sku' => 'BOX-CARD-SMALL',
                'description' => 'High-quality corrugated cardboard box perfect for small items shipping. Single wall construction with excellent crush resistance.',
                'short_description' => 'Small corrugated cardboard box for shipping small items.',
                'price' => 2.50,
                'sale_price' => 2.20,
                'cost_price' => 1.20,
                'weight' => 0.15,
                'dimensions' => [
                    'length' => 300,
                    'width' => 200,
                    'height' => 150,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 5000,
                'min_stock_level' => 500,
                'is_featured' => true,
                'attributes' => [
                    'material' => 'Corrugated Cardboard',
                    'wall_type' => 'Single Wall',
                    'edge_crush_test' => '4.0 kN/m',
                    'color' => 'Brown',
                    'recyclable' => 'Yes'
                ]
            ],
            [
                'name' => 'Medium Cardboard Box (40x30x25cm)',
                'sku' => 'BOX-CARD-MEDIUM',
                'description' => 'Versatile medium-sized cardboard box suitable for a wide range of products. Double wall construction for extra strength.',
                'short_description' => 'Medium double-wall cardboard box for general shipping.',
                'price' => 4.20,
                'sale_price' => 3.80,
                'cost_price' => 2.10,
                'weight' => 0.28,
                'dimensions' => [
                    'length' => 400,
                    'width' => 300,
                    'height' => 250,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 3000,
                'min_stock_level' => 300,
                'is_featured' => true,
                'attributes' => [
                    'material' => 'Corrugated Cardboard',
                    'wall_type' => 'Double Wall',
                    'edge_crush_test' => '5.5 kN/m',
                    'color' => 'Brown',
                    'max_weight' => '15 kg'
                ]
            ],
            [
                'name' => 'Large Cardboard Box (60x40x40cm)',
                'sku' => 'BOX-CARD-LARGE',
                'description' => 'Large heavy-duty cardboard box for bulky items. Triple wall construction provides maximum protection for valuable goods.',
                'short_description' => 'Large triple-wall cardboard box for heavy items.',
                'price' => 8.50,
                'cost_price' => 4.20,
                'weight' => 0.65,
                'dimensions' => [
                    'length' => 600,
                    'width' => 400,
                    'height' => 400,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 1500,
                'min_stock_level' => 150,
                'attributes' => [
                    'material' => 'Corrugated Cardboard',
                    'wall_type' => 'Triple Wall',
                    'edge_crush_test' => '7.0 kN/m',
                    'color' => 'Brown',
                    'max_weight' => '30 kg'
                ]
            ],
            [
                'name' => 'Wooden Export Crate (100x80x60cm)',
                'sku' => 'BOX-WOOD-EXPORT',
                'description' => 'Heavy-duty wooden export crate for international shipping. ISPM-15 certified heat-treated lumber. Ideal for machinery and heavy equipment.',
                'short_description' => 'ISPM-15 certified wooden export crate for heavy items.',
                'price' => 85.00,
                'sale_price' => 78.00,
                'cost_price' => 45.00,
                'weight' => 25.0,
                'dimensions' => [
                    'length' => 1000,
                    'width' => 800,
                    'height' => 600,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 200,
                'min_stock_level' => 20,
                'is_featured' => true,
                'attributes' => [
                    'material' => 'Heat-Treated Pine',
                    'certification' => 'ISPM-15',
                    'construction' => 'Nailed & Screwed',
                    'max_weight' => '500 kg',
                    'fumigation_free' => 'Yes'
                ]
            ],
            [
                'name' => 'Plastic Storage Container with Lid (50L)',
                'sku' => 'BOX-PLASTIC-50L',
                'description' => 'Durable plastic storage container with secure lid. Stackable design, weather-resistant, and reusable. Perfect for long-term storage.',
                'short_description' => 'Stackable 50L plastic storage container with lid.',
                'price' => 25.00,
                'cost_price' => 15.00,
                'weight' => 2.8,
                'dimensions' => [
                    'length' => 600,
                    'width' => 400,
                    'height' => 300,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 800,
                'min_stock_level' => 80,
                'attributes' => [
                    'material' => 'High-Density Polyethylene',
                    'capacity' => '50 Liters',
                    'stackable' => 'Yes',
                    'temperature_range' => '-20°C to +60°C',
                    'food_grade' => 'Yes'
                ]
            ]
        ];

        foreach ($boxes as $boxData) {
            Product::create(array_merge($boxData, [
                'category_id' => $category->id,
                'slug' => Str::slug($boxData['name']),
                'manage_stock' => true,
                'stock_status' => 'in_stock',
                'is_active' => true,
                'shipping_class' => 'standard',
                'reviews_allowed' => true,
                'meta_title' => $boxData['name'] . ' - Atrix Logistics',
                'meta_description' => $boxData['short_description'],
            ]));
        }
    }

    /**
     * Create steel products
     */
    private function createSteel($category): void
    {
        $steelProducts = [
            [
                'name' => 'Steel I-Beam 200x100x5.7mm (6m length)',
                'sku' => 'STEEL-IBEAM-200-6M',
                'description' => 'High-strength structural steel I-beam suitable for construction and industrial applications. Hot-rolled steel with excellent load-bearing capacity.',
                'short_description' => 'Structural steel I-beam for construction applications.',
                'price' => 285.00,
                'sale_price' => 265.00,
                'cost_price' => 195.00,
                'weight' => 67.2,
                'dimensions' => [
                    'length' => 6000,
                    'width' => 100,
                    'height' => 200,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 150,
                'min_stock_level' => 20,
                'is_featured' => true,
                'attributes' => [
                    'material' => 'Hot-Rolled Steel',
                    'grade' => 'S275JR',
                    'section_modulus' => '142.4 cm³',
                    'moment_of_inertia' => '1423 cm⁴',
                    'weight_per_meter' => '11.2 kg/m',
                    'standard' => 'EN 10025'
                ]
            ],
            [
                'name' => 'Steel Plate 10mm x 1500mm x 3000mm',
                'sku' => 'STEEL-PLATE-10-1500-3000',
                'description' => 'High-quality steel plate for fabrication and construction. Precision cut with smooth edges. Suitable for welding and machining.',
                'short_description' => '10mm thick steel plate for fabrication work.',
                'price' => 420.00,
                'cost_price' => 285.00,
                'weight' => 353.25,
                'dimensions' => [
                    'length' => 3000,
                    'width' => 1500,
                    'height' => 10,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 80,
                'min_stock_level' => 10,
                'is_featured' => true,
                'attributes' => [
                    'material' => 'Mild Steel',
                    'grade' => 'S235JR',
                    'thickness' => '10mm',
                    'surface_finish' => 'Hot Rolled',
                    'tolerance' => '±0.5mm',
                    'cutting_method' => 'Plasma Cut'
                ]
            ],
            [
                'name' => 'Steel Round Pipe 48.3mm x 3.2mm (6m length)',
                'sku' => 'STEEL-PIPE-48-3.2-6M',
                'description' => 'Seamless steel round pipe for structural and mechanical applications. Precision manufactured with consistent wall thickness.',
                'short_description' => 'Seamless steel round pipe for structural use.',
                'price' => 45.00,
                'cost_price' => 28.00,
                'weight' => 14.2,
                'dimensions' => [
                    'length' => 6000,
                    'width' => 48.3,
                    'height' => 48.3,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 300,
                'min_stock_level' => 50,
                'attributes' => [
                    'material' => 'Carbon Steel',
                    'grade' => 'S275JOH',
                    'outer_diameter' => '48.3mm',
                    'wall_thickness' => '3.2mm',
                    'inner_diameter' => '41.9mm',
                    'manufacturing' => 'Seamless'
                ]
            ],
            [
                'name' => 'Steel Square Tube 50x50x3mm (6m length)',
                'sku' => 'STEEL-SQUARE-50-3-6M',
                'description' => 'Hollow structural steel square tube perfect for framework and construction. Uniform wall thickness and precise dimensions.',
                'short_description' => 'Steel square tube for framework construction.',
                'price' => 38.00,
                'cost_price' => 24.00,
                'weight' => 13.8,
                'dimensions' => [
                    'length' => 6000,
                    'width' => 50,
                    'height' => 50,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 250,
                'min_stock_level' => 40,
                'attributes' => [
                    'material' => 'Structural Steel',
                    'grade' => 'S355JOH',
                    'external_size' => '50x50mm',
                    'wall_thickness' => '3mm',
                    'internal_size' => '44x44mm',
                    'manufacturing' => 'Cold Formed'
                ]
            ],
            [
                'name' => 'Steel Angle Bar 50x50x5mm (6m length)',
                'sku' => 'STEEL-ANGLE-50-5-6M',
                'description' => 'Equal angle steel bar for structural reinforcement and framework. Hot-rolled with consistent dimensions throughout length.',
                'short_description' => 'Equal angle steel bar for structural reinforcement.',
                'price' => 32.00,
                'cost_price' => 20.00,
                'weight' => 22.7,
                'dimensions' => [
                    'length' => 6000,
                    'width' => 50,
                    'height' => 50,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 200,
                'min_stock_level' => 30,
                'attributes' => [
                    'material' => 'Hot-Rolled Steel',
                    'grade' => 'S275JR',
                    'leg_length' => '50mm',
                    'thickness' => '5mm',
                    'weight_per_meter' => '3.78 kg/m',
                    'type' => 'Equal Angle'
                ]
            ],
            [
                'name' => 'Steel Rebar 12mm (12m length)',
                'sku' => 'STEEL-REBAR-12-12M',
                'description' => 'High-tensile steel reinforcement bar for concrete construction. Ribbed surface for excellent concrete bonding.',
                'short_description' => 'Steel reinforcement bar for concrete construction.',
                'price' => 18.50,
                'cost_price' => 12.00,
                'weight' => 10.7,
                'dimensions' => [
                    'length' => 12000,
                    'width' => 12,
                    'height' => 12,
                    'unit' => 'mm'
                ],
                'stock_quantity' => 500,
                'min_stock_level' => 100,
                'attributes' => [
                    'material' => 'Reinforcement Steel',
                    'grade' => 'B500B',
                    'diameter' => '12mm',
                    'surface' => 'Ribbed',
                    'tensile_strength' => '500-650 MPa',
                    'standard' => 'BS 4449'
                ]
            ]
        ];

        foreach ($steelProducts as $steelData) {
            Product::create(array_merge($steelData, [
                'category_id' => $category->id,
                'slug' => Str::slug($steelData['name']),
                'manage_stock' => true,
                'stock_status' => 'in_stock',
                'is_active' => true,
                'shipping_class' => 'heavy',
                'reviews_allowed' => true,
                'meta_title' => $steelData['name'] . ' - Atrix Logistics',
                'meta_description' => $steelData['short_description'],
            ]));
        }
    }
}
