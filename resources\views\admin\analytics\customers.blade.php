@extends('layouts.admin')

@section('title', 'Customer Analytics')
@section('page-title', 'Customer Analytics')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <button type="button" class="btn btn-outline-primary" onclick="exportAnalytics()">
            <i class="fas fa-download me-1"></i> Export Report
        </button>
        <button type="button" class="btn btn-primary" onclick="refreshAnalytics()">
            <i class="fas fa-sync me-1"></i> Refresh Data
        </button>
    </div>
@endsection

@section('content')
    <!-- Customer Overview Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        Customer Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Total Customers -->
                        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($customerStats['total_customers']) }}</h4>
                                            <p class="mb-0">Total Customers</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-users fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Active Customers -->
                        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($customerStats['active_customers']) }}</h4>
                                            <p class="mb-0">Active Customers</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- New This Month -->
                        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($customerStats['new_customers_this_month']) }}</h4>
                                            <p class="mb-0">New This Month</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-user-plus fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customers with Orders -->
                        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($customerStats['customers_with_orders']) }}</h4>
                                            <p class="mb-0">With Orders</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Conversion Rate -->
                        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                            <div class="card bg-dark text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ $customerStats['conversion_rate'] }}%</h4>
                                            <p class="mb-0">Conversion Rate</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-percentage fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Growth Rate -->
                        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
                            <div class="card bg-{{ $acquisitionMetrics['growth_rate'] >= 0 ? 'success' : 'danger' }} text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ $acquisitionMetrics['growth_rate'] }}%</h4>
                                            <p class="mb-0">Growth Rate</p>
                                        </div>
                                        <div>
                                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Acquisition & Lifetime Value -->
    <div class="row mb-4">
        <!-- Customer Acquisition -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Customer Acquisition (Last 12 Months)
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="acquisitionChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Acquisition Sources -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Acquisition Sources
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="acquisitionSourcesChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Lifetime Value & Purchase Behavior -->
    <div class="row mb-4">
        <!-- Lifetime Value Metrics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        Lifetime Value Metrics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Average LTV:</span>
                            <strong>@currency($lifetimeValueMetrics['average_ltv'])</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Revenue:</span>
                            <strong>@currency($lifetimeValueMetrics['total_revenue'])</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Avg Order Value:</span>
                            <strong>@currency($lifetimeValueMetrics['average_order_value'])</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Paying Customers:</span>
                            <strong>{{ number_format($lifetimeValueMetrics['paying_customers']) }}</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Retention Rate:</span>
                            <strong>{{ $lifetimeValueMetrics['retention_metrics']['retention_rate'] }}%</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Value Segments -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Customer Value Segments
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="valueSegmentsChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Order Frequency -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-doughnut me-2"></i>
                        Order Frequency Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="orderFrequencyChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Segmentation -->
    <div class="row mb-4">
        <!-- RFM Segmentation -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-users-cog me-2"></i>
                        Customer Segmentation (RFM Analysis)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($customerSegmentation['rfm_segments'] as $segment => $count)
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">{{ number_format($count) }}</h5>
                                        <p class="card-text">{{ ucwords(str_replace('_', ' ', $segment)) }}</p>
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" style="width: {{ $customerSegmentation['total_analyzed'] > 0 ? ($count / $customerSegmentation['total_analyzed']) * 100 : 0 }}%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Geographic Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-map-marked-alt me-2"></i>
                        Geographic Distribution
                    </h6>
                </div>
                <div class="card-body">
                    @if($customerSegmentation['geographic_distribution']->count() > 0)
                        @foreach($customerSegmentation['geographic_distribution'] as $state => $count)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ $state }}</span>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 100px; height: 10px;">
                                        <div class="progress-bar" style="width: {{ ($count / $customerSegmentation['geographic_distribution']->max()) * 100 }}%"></div>
                                    </div>
                                    <span class="badge bg-primary">{{ $count }}</span>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted">No geographic data available</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers & Recent Activity -->
    <div class="row mb-4">
        <!-- Top Customers by Revenue -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-crown me-2"></i>
                        Top Customers by Revenue
                    </h6>
                </div>
                <div class="card-body">
                    @if($topCustomers['top_by_revenue']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Orders</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($topCustomers['top_by_revenue'] as $customer)
                                        <tr>
                                            <td>
                                                <strong>{{ $customer->name }}</strong>
                                                <br><small class="text-muted">{{ $customer->email }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $customer->total_orders }}</span>
                                            </td>
                                            <td>
                                                <strong>@currency($customer->total_revenue ?? 0)</strong>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">No customer data available</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Customer Activity -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Recent Customer Activity
                    </h6>
                </div>
                <div class="card-body">
                    <div class="nav nav-tabs" id="activityTab" role="tablist">
                        <button class="nav-link active" id="registrations-tab" data-bs-toggle="tab" data-bs-target="#registrations">
                            Registrations
                        </button>
                        <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders">
                            Orders
                        </button>
                        <button class="nav-link" id="support-tab" data-bs-toggle="tab" data-bs-target="#support">
                            Support
                        </button>
                    </div>
                    
                    <div class="tab-content mt-3" id="activityTabContent">
                        <div class="tab-pane fade show active" id="registrations">
                            @foreach($recentActivity['recent_registrations'] as $registration)
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong>{{ $registration->name }}</strong>
                                        <br><small class="text-muted">{{ $registration->email }}</small>
                                    </div>
                                    <small class="text-muted">{{ $registration->created_at->diffForHumans() }}</small>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="tab-pane fade" id="orders">
                            @foreach($recentActivity['recent_orders'] as $order)
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong>{{ $order->order_number }}</strong>
                                        <br><small class="text-muted">{{ $order->customer->name ?? 'Unknown' }}</small>
                                    </div>
                                    <div class="text-end">
                                        <strong>@currency($order->total_amount)</strong>
                                        <br><small class="text-muted">{{ $order->created_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="tab-pane fade" id="support">
                            @foreach($recentActivity['recent_support_tickets'] as $ticket)
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong>{{ $ticket->ticket_number }}</strong>
                                        <br><small class="text-muted">{{ $ticket->user->name ?? 'Unknown' }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ $ticket->status_badge_color }}">{{ $ticket->formatted_status }}</span>
                                        <br><small class="text-muted">{{ $ticket->created_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Customer Acquisition Chart
    const acquisitionCtx = document.getElementById('acquisitionChart').getContext('2d');
    const acquisitionChart = new Chart(acquisitionCtx, {
        type: 'line',
        data: {
            labels: [
                @foreach($acquisitionMetrics['monthly_acquisition'] as $month)
                    '{{ $month["period"] }}',
                @endforeach
            ],
            datasets: [{
                label: 'New Customers',
                data: [
                    @foreach($acquisitionMetrics['monthly_acquisition'] as $month)
                        {{ $month["count"] }},
                    @endforeach
                ],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Acquisition Sources Chart
    const acquisitionSourcesCtx = document.getElementById('acquisitionSourcesChart').getContext('2d');
    const acquisitionSourcesChart = new Chart(acquisitionSourcesCtx, {
        type: 'doughnut',
        data: {
            labels: [
                @foreach($acquisitionMetrics['acquisition_sources'] as $source => $count)
                    '{{ $source }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($acquisitionMetrics['acquisition_sources'] as $source => $count)
                        {{ $count }},
                    @endforeach
                ],
                backgroundColor: [
                    '#2563eb',
                    '#059669',
                    '#dc2626',
                    '#d97706',
                    '#7c3aed'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Customer Value Segments Chart
    const valueSegmentsCtx = document.getElementById('valueSegmentsChart').getContext('2d');
    const valueSegmentsChart = new Chart(valueSegmentsCtx, {
        type: 'bar',
        data: {
            labels: ['High Value', 'Medium Value', 'Low Value', 'Minimal Value'],
            datasets: [{
                label: 'Customers',
                data: [
                    {{ $lifetimeValueMetrics['value_segments']['high_value'] }},
                    {{ $lifetimeValueMetrics['value_segments']['medium_value'] }},
                    {{ $lifetimeValueMetrics['value_segments']['low_value'] }},
                    {{ $lifetimeValueMetrics['value_segments']['minimal_value'] }}
                ],
                backgroundColor: [
                    '#059669',
                    '#2563eb',
                    '#d97706',
                    '#dc2626'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Order Frequency Chart
    const orderFrequencyCtx = document.getElementById('orderFrequencyChart').getContext('2d');
    const orderFrequencyChart = new Chart(orderFrequencyCtx, {
        type: 'doughnut',
        data: {
            labels: ['No Orders', 'One Time', 'Occasional', 'Regular', 'Frequent'],
            datasets: [{
                data: [
                    {{ $purchaseBehavior['order_frequency']['no_orders'] ?? 0 }},
                    {{ $purchaseBehavior['order_frequency']['one_time'] ?? 0 }},
                    {{ $purchaseBehavior['order_frequency']['occasional'] ?? 0 }},
                    {{ $purchaseBehavior['order_frequency']['regular'] ?? 0 }},
                    {{ $purchaseBehavior['order_frequency']['frequent'] ?? 0 }}
                ],
                backgroundColor: [
                    '#6b7280',
                    '#dc2626',
                    '#d97706',
                    '#2563eb',
                    '#059669'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Utility functions
    function refreshAnalytics() {
        location.reload();
    }

    function exportAnalytics() {
        alert('Export functionality coming soon!');
    }
</script>
@endpush
