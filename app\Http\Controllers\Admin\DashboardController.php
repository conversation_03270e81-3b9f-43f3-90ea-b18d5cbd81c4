<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\User;
use App\Models\Carrier;
use App\Models\QuoteRequest;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\Contact;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index(): View
    {
        // Get logistics statistics
        $logisticsStats = [
            'total_parcels' => Parcel::count(),
            'pending_parcels' => Parcel::where('status', 'pending')->count(),
            'in_transit_parcels' => Parcel::whereIn('status', ['picked_up', 'in_transit', 'out_for_delivery'])->count(),
            'delivered_parcels' => Parcel::where('status', 'delivered')->count(),
            'total_customers' => User::where('role', 'customer')->count(),
            'total_carriers' => Carrier::where('is_active', true)->count(),
            'pending_quotes' => QuoteRequest::where('status', 'pending')->count(),
            'revenue_this_month' => Parcel::whereMonth('created_at', now()->month)
                                         ->whereYear('created_at', now()->year)
                                         ->where('is_paid', true)
                                         ->sum('total_cost'),
        ];

        // Get e-commerce statistics
        $ecommerceStats = $this->getEcommerceStats();

        // Get order statistics
        $orderStats = $this->getOrderStats();

        // Get recent parcels
        $recent_parcels = Parcel::with(['carrier', 'user'])
                               ->orderBy('created_at', 'desc')
                               ->limit(10)
                               ->get();

        // Get recent customers
        $recent_customers = User::where('role', 'customer')
                              ->orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();

        // Get parcel status distribution for chart
        $status_distribution = Parcel::selectRaw('status, COUNT(*) as count')
                                   ->groupBy('status')
                                   ->pluck('count', 'status')
                                   ->toArray();

        // Get e-commerce analytics
        $ecommerceAnalytics = $this->getEcommerceAnalytics();

        // Get communication statistics
        $communicationStats = [
            'total_contacts' => Contact::count(),
            'new_contacts' => Contact::where('status', 'new')->count(),
            'total_subscribers' => NewsletterSubscriber::count(),
            'active_subscribers' => NewsletterSubscriber::active()->count(),
            'recent_contacts' => Contact::latest()->take(5)->get(),
            'live_chat_active' => \App\Models\LiveChatSession::active()->count(),
            'live_chat_waiting' => \App\Models\LiveChatSession::waiting()->count(),
            'live_chat_unread' => \App\Models\LiveChatMessage::fromVisitor()->unread()->count(),
        ];

        return view('admin.dashboard', compact(
            'logisticsStats',
            'ecommerceStats',
            'orderStats',
            'recent_parcels',
            'recent_customers',
            'status_distribution',
            'ecommerceAnalytics',
            'communicationStats'
        ));
    }

    /**
     * Get e-commerce statistics
     */
    private function getEcommerceStats(): array
    {
        return [
            'total_products' => Product::count(),
            'active_products' => Product::where('is_active', true)->count(),
            'featured_products' => Product::where('is_featured', true)->count(),
            'out_of_stock' => Product::where('manage_stock', true)->where('stock_quantity', 0)->count(),
            'low_stock' => Product::where('manage_stock', true)
                                 ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                 ->where('stock_quantity', '>', 0)
                                 ->count(),
            'total_categories' => Category::count(),
            'active_categories' => Category::where('is_active', true)->count(),
            'total_inventory_value' => Product::where('is_active', true)
                                             ->where('manage_stock', true)
                                             ->selectRaw('SUM(stock_quantity * cost_price) as total')
                                             ->value('total') ?? 0,
        ];
    }

    /**
     * Get order statistics
     */
    private function getOrderStats(): array
    {
        return [
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'processing_orders' => Order::whereIn('status', ['confirmed', 'processing'])->count(),
            'shipped_orders' => Order::where('status', 'shipped')->count(),
            'delivered_orders' => Order::where('status', 'delivered')->count(),
            'cancelled_orders' => Order::where('status', 'cancelled')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_revenue' => Order::where('payment_status', 'pending')->sum('total_amount'),
            'average_order_value' => Order::where('payment_status', 'paid')->avg('total_amount') ?? 0,
            'orders_today' => Order::whereDate('created_at', today())->count(),
            'orders_this_week' => Order::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'orders_this_month' => Order::whereMonth('created_at', now()->month)->count(),
        ];
    }

    /**
     * Get comprehensive e-commerce analytics
     */
    private function getEcommerceAnalytics(): array
    {
        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();
        $thisYear = Carbon::now()->startOfYear();

        return [
            // Revenue analytics (placeholder for future orders)
            'revenue' => [
                'today' => 0, // Will be updated when orders are implemented
                'week' => 0,
                'month' => 0,
                'year' => 0,
            ],

            // Product performance
            'top_products' => Product::where('is_active', true)
                                   ->orderBy('created_at', 'desc')
                                   ->limit(5)
                                   ->get(['id', 'name', 'price', 'stock_quantity']),

            // Recently added products
            'recent_products' => Product::orderBy('created_at', 'desc')
                                      ->limit(5)
                                      ->get(['id', 'name', 'price', 'is_active', 'created_at']),

            // Category performance
            'category_stats' => Category::withCount('products')
                                      ->where('is_active', true)
                                      ->orderBy('products_count', 'desc')
                                      ->limit(5)
                                      ->get(['id', 'name', 'products_count']),

            // Inventory alerts
            'inventory_alerts' => [
                'low_stock_products' => Product::where('manage_stock', true)
                                              ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                              ->where('stock_quantity', '>', 0)
                                              ->with('category')
                                              ->get(['id', 'name', 'stock_quantity', 'min_stock_level', 'category_id']),

                'out_of_stock_products' => Product::where('manage_stock', true)
                                                 ->where('stock_quantity', 0)
                                                 ->with('category')
                                                 ->get(['id', 'name', 'category_id']),
            ],

            // Stock movement trends (last 30 days)
            'stock_trends' => Product::where('manage_stock', true)
                                   ->where('created_at', '>=', Carbon::now()->subDays(30))
                                   ->selectRaw('DATE(created_at) as date, COUNT(*) as products_added')
                                   ->groupBy('date')
                                   ->orderBy('date')
                                   ->get()
                                   ->pluck('products_added', 'date'),
        ];
    }

    /**
     * Get dashboard analytics data for AJAX requests
     */
    public function analyticsData(): \Illuminate\Http\JsonResponse
    {
        $ecommerceStats = $this->getEcommerceStats();
        $ecommerceAnalytics = $this->getEcommerceAnalytics();

        return response()->json([
            'ecommerce_stats' => $ecommerceStats,
            'low_stock_count' => $ecommerceAnalytics['inventory_alerts']['low_stock_products']->count(),
            'out_of_stock_count' => $ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->count(),
            'recent_products_count' => $ecommerceAnalytics['recent_products']->count(),
            'category_performance' => $ecommerceAnalytics['category_stats']->map(function($category) {
                return [
                    'name' => $category->name,
                    'products_count' => $category->products_count
                ];
            }),
            'timestamp' => now()->toISOString()
        ]);
    }
}
