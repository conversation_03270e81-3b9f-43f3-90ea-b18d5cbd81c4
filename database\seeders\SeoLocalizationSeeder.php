<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use Illuminate\Database\Seeder;

class SeoLocalizationSeeder extends Seeder
{
    /**
     * Run the database seeder for SEO localization settings.
     */
    public function run(): void
    {
        $seoLocalizationSettings = [
            // Primary Language & Region
            [
                'key_name' => 'primary_language',
                'value' => 'en',
                'type' => 'string',
                'group_name' => 'seo_localization',
                'label' => 'Primary Language',
                'description' => 'Primary language code (ISO 639-1 format, e.g., en, es, fr)',
                'is_public' => true
            ],
            [
                'key_name' => 'primary_region',
                'value' => 'US',
                'type' => 'string',
                'group_name' => 'seo_localization',
                'label' => 'Primary Region',
                'description' => 'Primary region/country code (ISO 3166-1 Alpha 2 format, e.g., US, GB, CA)',
                'is_public' => true
            ],
            [
                'key_name' => 'default_locale',
                'value' => 'en-US',
                'type' => 'string',
                'group_name' => 'seo_localization',
                'label' => 'Default Locale',
                'description' => 'Default locale combining language and region (e.g., en-US, en-GB, es-ES)',
                'is_public' => true
            ],

            // Hreflang Configuration
            [
                'key_name' => 'enable_hreflang',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Enable Hreflang Tags',
                'description' => 'Enable automatic hreflang tag generation for international SEO',
                'is_public' => false
            ],
            [
                'key_name' => 'hreflang_x_default',
                'value' => '',
                'type' => 'string',
                'group_name' => 'seo_localization',
                'label' => 'X-Default URL',
                'description' => 'URL for x-default hreflang (fallback for unmatched languages). Leave empty to use homepage.',
                'is_public' => false
            ],
            [
                'key_name' => 'supported_locales',
                'value' => '{"en-US": {"name": "English (United States)", "url": "", "enabled": true}, "en-GB": {"name": "English (United Kingdom)", "url": "", "enabled": false}, "es-ES": {"name": "Spanish (Spain)", "url": "", "enabled": false}, "fr-FR": {"name": "French (France)", "url": "", "enabled": false}, "de-DE": {"name": "German (Germany)", "url": "", "enabled": false}}',
                'type' => 'json',
                'group_name' => 'seo_localization',
                'label' => 'Supported Locales',
                'description' => 'JSON configuration of supported locales with their URLs and status',
                'is_public' => false
            ],

            // Structured Data Localization
            [
                'key_name' => 'localize_structured_data',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Localize Structured Data',
                'description' => 'Enable localization of structured data (Schema.org) based on user locale',
                'is_public' => false
            ],
            [
                'key_name' => 'organization_name_localized',
                'value' => '{"en": "Atrix Logistics", "es": "Atrix Logística", "fr": "Atrix Logistique", "de": "Atrix Logistik"}',
                'type' => 'json',
                'group_name' => 'seo_localization',
                'label' => 'Localized Organization Names',
                'description' => 'Organization name translations for different languages',
                'is_public' => true
            ],

            // URL Structure
            [
                'key_name' => 'url_structure_type',
                'value' => 'subdirectory',
                'type' => 'string',
                'group_name' => 'seo_localization',
                'label' => 'URL Structure Type',
                'description' => 'How to structure multilingual URLs: subdirectory (/en/), subdomain (en.), domain (example.com), parameter (?lang=en)',
                'is_public' => false
            ],
            [
                'key_name' => 'hide_default_locale_in_url',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Hide Default Locale in URL',
                'description' => 'Hide the default locale from URLs (e.g., /about instead of /en/about)',
                'is_public' => false
            ],

            // Content Localization
            [
                'key_name' => 'auto_detect_language',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Auto-Detect User Language',
                'description' => 'Automatically detect and redirect users to their preferred language based on browser settings',
                'is_public' => false
            ],
            [
                'key_name' => 'fallback_to_primary',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Fallback to Primary Language',
                'description' => 'Fallback to primary language when content is not available in user\'s preferred language',
                'is_public' => false
            ],

            // Meta Tags Localization
            [
                'key_name' => 'localized_meta_titles',
                'value' => '{"en": "Professional Logistics Solutions | Atrix Logistics", "es": "Soluciones Logísticas Profesionales | Atrix Logística", "fr": "Solutions Logistiques Professionnelles | Atrix Logistique", "de": "Professionelle Logistiklösungen | Atrix Logistik"}',
                'type' => 'json',
                'group_name' => 'seo_localization',
                'label' => 'Localized Meta Titles',
                'description' => 'Default meta title translations for different languages',
                'is_public' => true
            ],
            [
                'key_name' => 'localized_meta_descriptions',
                'value' => '{"en": "Leading logistics company providing shipping, warehousing, and supply chain solutions worldwide. Fast, reliable, and cost-effective services.", "es": "Empresa líder en logística que ofrece soluciones de envío, almacenamiento y cadena de suministro en todo el mundo.", "fr": "Entreprise de logistique leader offrant des solutions d\'expédition, d\'entreposage et de chaîne d\'approvisionnement dans le monde entier.", "de": "Führendes Logistikunternehmen mit Versand-, Lager- und Lieferkettenlösungen weltweit."}',
                'type' => 'json',
                'group_name' => 'seo_localization',
                'label' => 'Localized Meta Descriptions',
                'description' => 'Default meta description translations for different languages',
                'is_public' => true
            ],

            // Currency & Formatting
            [
                'key_name' => 'locale_based_currency',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Locale-Based Currency',
                'description' => 'Display currency based on user locale (USD for US, EUR for EU, etc.)',
                'is_public' => false
            ],
            [
                'key_name' => 'currency_by_locale',
                'value' => '{"en-US": "USD", "en-GB": "GBP", "en-CA": "CAD", "en-AU": "AUD", "es-ES": "EUR", "fr-FR": "EUR", "de-DE": "EUR", "ja-JP": "JPY"}',
                'type' => 'json',
                'group_name' => 'seo_localization',
                'label' => 'Currency by Locale',
                'description' => 'Currency codes mapped to specific locales',
                'is_public' => false
            ],

            // Sitemap Configuration
            [
                'key_name' => 'generate_localized_sitemaps',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Generate Localized Sitemaps',
                'description' => 'Generate separate sitemaps for each locale with proper hreflang annotations',
                'is_public' => false
            ],
            [
                'key_name' => 'sitemap_include_alternates',
                'value' => '1',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Include Alternates in Sitemap',
                'description' => 'Include alternate language versions in XML sitemaps using xhtml:link',
                'is_public' => false
            ],

            // Search Console Integration
            [
                'key_name' => 'google_search_console_properties',
                'value' => '{"en-US": "", "en-GB": "", "es-ES": "", "fr-FR": "", "de-DE": ""}',
                'type' => 'json',
                'group_name' => 'seo_localization',
                'label' => 'Search Console Properties',
                'description' => 'Google Search Console property URLs for different locales',
                'is_public' => false
            ],

            // Advanced Settings
            [
                'key_name' => 'canonical_url_strategy',
                'value' => 'locale_specific',
                'type' => 'string',
                'group_name' => 'seo_localization',
                'label' => 'Canonical URL Strategy',
                'description' => 'How to handle canonical URLs: locale_specific, primary_language, or absolute',
                'is_public' => false
            ],
            [
                'key_name' => 'robots_txt_localized',
                'value' => '0',
                'type' => 'boolean',
                'group_name' => 'seo_localization',
                'label' => 'Localized Robots.txt',
                'description' => 'Generate separate robots.txt files for each locale',
                'is_public' => false
            ]
        ];

        foreach ($seoLocalizationSettings as $setting) {
            SiteSetting::updateOrCreate(
                ['key_name' => $setting['key_name']],
                $setting
            );
        }
    }
}
