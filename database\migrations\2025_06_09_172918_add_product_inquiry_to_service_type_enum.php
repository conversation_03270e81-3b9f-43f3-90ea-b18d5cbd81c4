<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'product_inquiry' to the service_type enum
        DB::statement("ALTER TABLE quotes MODIFY COLUMN service_type ENUM('domestic_shipping', 'international_shipping', 'express_delivery', 'freight_shipping', 'warehousing', 'custom_logistics', 'bulk_shipping', 'specialized_transport', 'product_inquiry')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'product_inquiry' from the service_type enum
        DB::statement("ALTER TABLE quotes MODIFY COLUMN service_type ENUM('domestic_shipping', 'international_shipping', 'express_delivery', 'freight_shipping', 'warehousing', 'custom_logistics', 'bulk_shipping', 'specialized_transport')");
    }
};
