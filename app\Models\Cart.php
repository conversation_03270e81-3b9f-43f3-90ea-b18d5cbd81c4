<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Auth;

class Cart extends Model
{
    protected $fillable = [
        'user_id',
        'session_id',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'metadata',
        'expires_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'metadata' => 'array',
        'expires_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get or create cart for current user/session
     */
    public static function getCurrent(): self
    {
        if (Auth::check()) {
            return static::firstOrCreate(['user_id' => Auth::id()]);
        }

        $sessionId = session()->getId();
        return static::firstOrCreate(
            ['session_id' => $sessionId],
            ['expires_at' => now()->addDays(7)]
        );
    }

    /**
     * Add product to cart
     */
    public function addProduct(Product $product, int $quantity = 1, array $options = []): CartItem
    {
        $unitPrice = $product->getCurrentPrice();

        $cartItem = $this->items()->updateOrCreate(
            ['product_id' => $product->id],
            [
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $unitPrice * $quantity,
                'product_options' => $options,
            ]
        );

        $this->recalculateTotal();

        return $cartItem;
    }

    /**
     * Update product quantity in cart
     */
    public function updateQuantity(Product $product, int $quantity): bool
    {
        $cartItem = $this->items()->where('product_id', $product->id)->first();

        if (!$cartItem) {
            return false;
        }

        if ($quantity <= 0) {
            $cartItem->delete();
        } else {
            $cartItem->update([
                'quantity' => $quantity,
                'total_price' => $cartItem->unit_price * $quantity,
            ]);
        }

        $this->recalculateTotal();

        return true;
    }

    /**
     * Remove product from cart
     */
    public function removeProduct(Product $product): bool
    {
        $removed = $this->items()->where('product_id', $product->id)->delete();

        if ($removed) {
            $this->recalculateTotal();
        }

        return $removed > 0;
    }

    /**
     * Clear all items from cart
     */
    public function clear(): void
    {
        $this->items()->delete();
        $this->recalculateTotal();
    }

    /**
     * Recalculate cart totals
     */
    public function recalculateTotal(): void
    {
        $subtotal = $this->items()->sum('total_price');

        // Calculate tax (you can implement tax logic here)
        $taxRate = 0; // 0% for now
        $taxAmount = $subtotal * $taxRate;

        // Calculate shipping (you can implement shipping logic here)
        $shippingAmount = 0; // Free shipping for now

        $total = $subtotal + $taxAmount + $shippingAmount - $this->discount_amount;

        $this->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'shipping_amount' => $shippingAmount,
            'total_amount' => max(0, $total),
        ]);
    }

    /**
     * Get cart item count
     */
    public function getItemCountAttribute(): int
    {
        return $this->items()->sum('quantity');
    }

    /**
     * Check if cart is empty
     */
    public function isEmpty(): bool
    {
        return $this->items()->count() === 0;
    }

    /**
     * Merge guest cart with user cart on login
     */
    public static function mergeGuestCart(string $sessionId, int $userId): void
    {
        $guestCart = static::where('session_id', $sessionId)->first();

        if (!$guestCart || $guestCart->isEmpty()) {
            return;
        }

        $userCart = static::firstOrCreate(['user_id' => $userId]);

        foreach ($guestCart->items as $guestItem) {
            $userCart->addProduct(
                $guestItem->product,
                $guestItem->quantity,
                $guestItem->product_options ?? []
            );
        }

        $guestCart->delete();
    }
}
