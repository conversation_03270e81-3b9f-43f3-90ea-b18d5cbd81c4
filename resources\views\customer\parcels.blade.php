@extends('layouts.customer')

@section('title', 'My Parcels')
@section('page-title', 'My Parcels')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <a href="{{ route('customer.track') }}" class="btn btn-outline-primary">
            <i class="fas fa-search-location me-1"></i> Track Package
        </a>
        <a href="#" class="btn btn-primary" onclick="alert('Coming soon!')">
            <i class="fas fa-plus me-1"></i> New Shipment
        </a>
    </div>
@endsection

@section('content')
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('customer.parcels') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Tracking number, description...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="carrier" class="form-label">Carrier</label>
                    <select class="form-select" id="carrier" name="carrier">
                        <option value="">All Carriers</option>
                        @foreach($carriers as $carrier)
                            <option value="{{ $carrier->id }}" {{ request('carrier') == $carrier->id ? 'selected' : '' }}>
                                {{ $carrier->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>
                
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request('date_to') }}">
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
            
            @if(request()->hasAny(['search', 'status', 'carrier', 'date_from', 'date_to']))
                <div class="mt-3">
                    <a href="{{ route('customer.parcels') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i> Clear Filters
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Parcels List -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Parcels ({{ $parcels->total() }})
                </h5>
                <div class="text-muted">
                    Showing {{ $parcels->firstItem() ?? 0 }} to {{ $parcels->lastItem() ?? 0 }} of {{ $parcels->total() }} results
                </div>
            </div>
        </div>
        <div class="card-body">
            @if($parcels->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tracking Number</th>
                                <th>Description</th>
                                <th>Recipient</th>
                                <th>Carrier</th>
                                <th>Status</th>
                                <th>Cost</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($parcels as $parcel)
                                <tr>
                                    <td>
                                        <strong>{{ $parcel->tracking_number }}</strong>
                                        @if($parcel->trackingEvents->count() > 0)
                                            <br><small class="text-muted">
                                                Last update: {{ $parcel->trackingEvents->first()->event_date->diffForHumans() }}
                                            </small>
                                        @endif
                                    </td>
                                    <td>
                                        {{ Str::limit($parcel->description, 40) }}
                                        @if($parcel->weight)
                                            <br><small class="text-muted">{{ $parcel->weight }}kg</small>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $parcel->recipient_name }}
                                        @if($parcel->recipient_city)
                                            <br><small class="text-muted">{{ $parcel->recipient_city }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        @if($parcel->carrier)
                                            <span class="badge bg-info">{{ $parcel->carrier->name }}</span>
                                        @else
                                            <span class="text-muted">Not assigned</span>
                                        @endif
                                    </td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'pending' => 'secondary',
                                                'picked_up' => 'info',
                                                'in_transit' => 'warning',
                                                'out_for_delivery' => 'primary',
                                                'delivered' => 'success',
                                                'exception' => 'danger',
                                                'returned' => 'dark'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $statusColors[$parcel->status] ?? 'secondary' }}">
                                            {{ ucwords(str_replace('_', ' ', $parcel->status)) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($parcel->total_cost)
                                            @currency($parcel->total_cost)
                                            @if(!$parcel->is_paid)
                                                <br><small class="text-danger">Unpaid</small>
                                            @endif
                                        @else
                                            <span class="text-muted">TBD</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ $parcel->created_at->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $parcel->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('customer.parcels.show', $parcel) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if(!$parcel->is_paid && $parcel->total_cost)
                                                <button class="btn btn-sm btn-outline-success" 
                                                        onclick="alert('Payment feature coming soon!')" title="Pay Now">
                                                    <i class="fas fa-credit-card"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $parcels->withQueryString()->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No parcels found</h5>
                    @if(request()->hasAny(['search', 'status', 'carrier', 'date_from', 'date_to']))
                        <p class="text-muted">Try adjusting your filters or search terms.</p>
                        <a href="{{ route('customer.parcels') }}" class="btn btn-outline-primary">
                            <i class="fas fa-times me-2"></i>
                            Clear Filters
                        </a>
                    @else
                        <p class="text-muted">Your shipping history will appear here once you start using our services.</p>
                        <a href="#" class="btn btn-primary" onclick="alert('Coming soon!')">
                            <i class="fas fa-plus me-2"></i>
                            Create Your First Shipment
                        </a>
                    @endif
                </div>
            @endif
        </div>
    </div>

    <!-- Summary Cards -->
    @if($parcels->count() > 0)
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary">{{ $parcels->total() }}</h5>
                        <p class="card-text text-muted">Total Parcels</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning">
                            {{ $parcels->where('status', 'in_transit')->count() + $parcels->where('status', 'picked_up')->count() }}
                        </h5>
                        <p class="card-text text-muted">In Transit</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">{{ $parcels->where('status', 'delivered')->count() }}</h5>
                        <p class="card-text text-muted">Delivered</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">
                            @currency($parcels->where('is_paid', true)->sum('total_cost'))
                        </h5>
                        <p class="card-text text-muted">Total Paid</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('scripts')
<script>
    // Auto-submit form when filters change
    document.querySelectorAll('#status, #carrier').forEach(function(element) {
        element.addEventListener('change', function() {
            this.form.submit();
        });
    });

    // Clear individual filters
    function clearFilter(filterName) {
        const input = document.querySelector(`[name="${filterName}"]`);
        if (input) {
            input.value = '';
            input.form.submit();
        }
    }
</script>
@endpush
