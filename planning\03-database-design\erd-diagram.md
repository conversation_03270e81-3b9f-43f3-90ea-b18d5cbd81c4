# 🗄️ Entity Relationship Diagram - Atrix Logistics

## 📊 Database Overview

The Atrix Logistics database is designed with the following core modules:

1. **Authentication & User Management**
2. **Logistics & Shipping Core**
3. **E-commerce System**
4. **Order Management**
5. **Content Management System**
6. **System Configuration**

## 🔗 Entity Relationships

### Core Authentication Module
```
users (1) ←→ (M) personal_access_tokens
users (1) ←→ (M) activity_logs
users (1) ←→ (M) blog_posts [author]
users (1) ←→ (M) contact_submissions [replied_by]
users (1) ←→ (M) orders
users (1) ←→ (M) parcels [optional customer]
```

### Logistics Core Module
```
carriers (1) ←→ (M) parcels
parcels (1) ←→ (M) tracking_events
users (1) ←→ (M) tracking_events [created_by]
```

### E-commerce Module
```
categories (1) ←→ (M) categories [self-referencing parent/child]
categories (M) ←→ (M) products [through product_categories]
products (1) ←→ (M) product_images
products (1) ←→ (M) order_items
```

### Order Management Module
```
users (1) ←→ (M) orders
orders (1) ←→ (M) order_items
products (1) ←→ (M) order_items
```

## 📋 Detailed Entity Descriptions

### 👤 Users Table
**Purpose:** Central user management for admins, staff, and customers
**Key Features:**
- Role-based access (admin, staff, customer)
- Complete profile information
- Address management for shipping
- Account status control

### 📦 Parcels Table
**Purpose:** Core parcel/package tracking system
**Key Features:**
- Unique tracking numbers
- Complete sender/recipient information
- Package dimensions and weight
- Status tracking workflow
- Multi-carrier support

### 🚚 Carriers Table
**Purpose:** Shipping company management
**Key Features:**
- Support for multiple carriers (DHL, USPS, Custom)
- API integration capabilities
- Tracking URL templates
- Active/inactive status

### 📍 Tracking Events Table
**Purpose:** Detailed parcel tracking history
**Key Features:**
- Chronological event tracking
- Location-based updates
- Public/private event visibility
- Admin-created events

### 🛍️ Products Table
**Purpose:** E-commerce product catalog
**Key Features:**
- Complete product information
- Inventory management
- SEO optimization fields
- Multiple pricing options

### 📂 Categories Table
**Purpose:** Hierarchical product categorization
**Key Features:**
- Parent/child relationships
- Unlimited nesting levels
- SEO-friendly slugs
- Sort ordering

### 🛒 Orders Table
**Purpose:** Customer order management
**Key Features:**
- Complete billing/shipping information
- Order status workflow
- Financial calculations
- Order tracking integration

### 📝 CMS Content Table
**Purpose:** Dynamic website content management
**Key Features:**
- Section-based content organization
- Rich content support
- Image management
- Call-to-action buttons

## 🔑 Key Relationships Explained

### User → Parcel Relationship
- **Type:** One-to-Many (Optional)
- **Description:** Users can have multiple parcels, but parcels can exist without registered users
- **Use Case:** Registered customers can track their orders, anonymous tracking also supported

### Carrier → Parcel Relationship
- **Type:** One-to-Many (Required)
- **Description:** Each parcel must be assigned to a carrier
- **Use Case:** Supports multiple shipping companies with different tracking systems

### Product → Category Relationship
- **Type:** Many-to-Many
- **Description:** Products can belong to multiple categories
- **Use Case:** Flexible product organization and filtering

### Order → User Relationship
- **Type:** Many-to-One (Required)
- **Description:** Orders must belong to a registered user
- **Use Case:** Customer account management and order history

## 📊 Database Indexes Strategy

### Primary Indexes
- All tables have auto-incrementing primary keys
- Unique constraints on business-critical fields (email, tracking_number, slug)

### Performance Indexes
- **Tracking queries:** `idx_tracking` on parcels table
- **User lookups:** `idx_email` on users table
- **Product searches:** `idx_slug`, `idx_status` on products table
- **Order management:** `idx_order_number`, `idx_status` on orders table

### Foreign Key Indexes
- All foreign key relationships have corresponding indexes
- Cascade/restrict rules defined based on business logic

## 🔒 Data Integrity Rules

### Cascade Deletes
- `tracking_events` → `parcels` (CASCADE)
- `order_items` → `orders` (CASCADE)
- `product_images` → `products` (CASCADE)

### Restrict Deletes
- `parcels` → `carriers` (RESTRICT)
- `orders` → `users` (RESTRICT)
- `order_items` → `products` (RESTRICT)

### Set Null
- `parcels` → `users` (SET NULL) - allows anonymous parcels
- `tracking_events` → `users` (SET NULL) - preserves history if admin deleted

## 📈 Scalability Considerations

### Partitioning Strategy
- **tracking_events:** Consider partitioning by date for large volumes
- **activity_logs:** Archive old logs to separate tables
- **orders:** Partition by year for historical data management

### Indexing Optimization
- Composite indexes for common query patterns
- Regular index usage analysis and optimization
- Consider full-text search for product descriptions

### Data Archival
- Implement soft deletes for critical business data
- Archive completed orders after specified period
- Maintain tracking history for compliance requirements

## 🔧 Migration Strategy

### Phase 1: Core Tables
1. Users and authentication
2. Carriers and basic logistics
3. Basic CMS content

### Phase 2: E-commerce
1. Categories and products
2. Product relationships
3. Inventory management

### Phase 3: Advanced Features
1. Orders and order items
2. Advanced tracking
3. Reporting and analytics

### Phase 4: Optimization
1. Performance indexes
2. Data archival setup
3. Monitoring and maintenance
