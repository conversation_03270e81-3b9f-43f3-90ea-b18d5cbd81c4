<?php

namespace App\Http\Controllers;

use App\Services\MonitoringService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class HealthController extends Controller
{
    protected MonitoringService $monitoringService;

    public function __construct(MonitoringService $monitoringService)
    {
        $this->monitoringService = $monitoringService;
    }

    /**
     * Basic health check endpoint
     */
    public function check(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'service' => config('app.name'),
            'version' => app()->version(),
        ]);
    }

    /**
     * Comprehensive health check
     */
    public function detailed(): JsonResponse
    {
        $healthCheck = $this->monitoringService->healthCheck();
        
        $statusCode = $healthCheck['status'] === 'ok' ? 200 : 503;
        
        return response()->json($healthCheck, $statusCode);
    }

    /**
     * System information endpoint
     */
    public function system(): JsonResponse
    {
        // Only allow in development or for authenticated admin users
        if (!app()->environment('local') && !$this->isAdminUser()) {
            abort(403, 'Access denied');
        }

        $systemInfo = $this->monitoringService->getSystemInfo();
        
        return response()->json([
            'system_info' => $systemInfo,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Performance metrics endpoint
     */
    public function metrics(): JsonResponse
    {
        // Only allow for authenticated admin users
        if (!$this->isAdminUser()) {
            abort(403, 'Access denied');
        }

        $this->monitoringService->logPerformanceMetrics();
        
        return response()->json([
            'message' => 'Performance metrics logged',
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Check if current user is admin
     */
    private function isAdminUser(): bool
    {
        return auth()->check() && 
               in_array(auth()->user()->role, ['admin', 'staff']);
    }
}
