<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class OtpController extends Controller
{
    protected SmsService $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * Send OTP to phone number
     */
    public function sendOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|min:10|max:15',
        ]);

        $phone = $request->phone;
        $rateLimitKey = 'otp-send:' . $request->ip() . ':' . $phone;

        // Rate limiting: 3 attempts per 5 minutes
        if (RateLimiter::tooManyAttempts($rateLimitKey, 3)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => "Too many attempts. Please try again in {$seconds} seconds.",
                'retry_after' => $seconds
            ], 429);
        }

        // Check if phone number can receive OTP
        if (!$this->smsService->canSendOtp($phone)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many OTP requests for this phone number. Please try again later.',
            ], 429);
        }

        // Check if user exists with this phone number
        $user = User::where('phone', $phone)->first();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'No account found with this phone number.',
            ], 404);
        }

        // Send OTP
        $otp = $this->smsService->sendOtp($phone);
        
        if ($otp) {
            RateLimiter::hit($rateLimitKey, 300); // 5 minutes
            
            return response()->json([
                'success' => true,
                'message' => 'OTP sent successfully to your phone number.',
                'remaining_attempts' => $this->smsService->getRemainingAttempts($phone),
                'expires_in' => 300, // 5 minutes
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to send OTP. Please try again.',
        ], 500);
    }

    /**
     * Verify OTP and login user
     */
    public function verifyOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'otp' => 'required|string|size:6',
        ]);

        $phone = $request->phone;
        $otp = $request->otp;
        $rateLimitKey = 'otp-verify:' . $request->ip() . ':' . $phone;

        // Rate limiting: 5 attempts per 5 minutes
        if (RateLimiter::tooManyAttempts($rateLimitKey, 5)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => "Too many verification attempts. Please try again in {$seconds} seconds.",
                'retry_after' => $seconds
            ], 429);
        }

        // Find user
        $user = User::where('phone', $phone)->first();
        if (!$user) {
            RateLimiter::hit($rateLimitKey, 300);
            return response()->json([
                'success' => false,
                'message' => 'Invalid phone number.',
            ], 404);
        }

        // Verify OTP
        if (!$this->smsService->verifyOtp($phone, $otp)) {
            RateLimiter::hit($rateLimitKey, 300);
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired OTP.',
            ], 400);
        }

        // Check if account is active
        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Your account has been deactivated. Please contact support.',
            ], 403);
        }

        // Login user
        Auth::login($user, true); // Remember user
        
        // Clear rate limiting
        RateLimiter::clear($rateLimitKey);
        
        // Update last login
        $user->update([
            'last_login_at' => now(),
            'last_login_ip' => $request->ip(),
        ]);

        // Determine redirect URL based on user role
        $redirectUrl = $this->getRedirectUrl($user);

        return response()->json([
            'success' => true,
            'message' => 'Login successful!',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
            ],
            'redirect_url' => $redirectUrl,
        ]);
    }

    /**
     * Resend OTP
     */
    public function resendOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
        ]);

        $phone = $request->phone;
        $rateLimitKey = 'otp-resend:' . $request->ip() . ':' . $phone;

        // Rate limiting: 2 resends per 10 minutes
        if (RateLimiter::tooManyAttempts($rateLimitKey, 2)) {
            $seconds = RateLimiter::availableIn($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => "Too many resend attempts. Please try again in {$seconds} seconds.",
                'retry_after' => $seconds
            ], 429);
        }

        // Check if phone number can receive OTP
        if (!$this->smsService->canSendOtp($phone)) {
            return response()->json([
                'success' => false,
                'message' => 'Too many OTP requests for this phone number. Please try again later.',
            ], 429);
        }

        // Check if user exists
        $user = User::where('phone', $phone)->first();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'No account found with this phone number.',
            ], 404);
        }

        // Send new OTP
        $otp = $this->smsService->sendOtp($phone);
        
        if ($otp) {
            RateLimiter::hit($rateLimitKey, 600); // 10 minutes
            
            return response()->json([
                'success' => true,
                'message' => 'New OTP sent successfully.',
                'remaining_attempts' => $this->smsService->getRemainingAttempts($phone),
                'expires_in' => 300, // 5 minutes
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to resend OTP. Please try again.',
        ], 500);
    }

    /**
     * Get redirect URL based on user role
     */
    protected function getRedirectUrl(User $user): string
    {
        switch ($user->role) {
            case 'admin':
            case 'staff':
                return route('admin.dashboard');
            case 'customer':
                return route('customer.dashboard');
            default:
                return route('dashboard');
        }
    }

    /**
     * Check OTP status
     */
    public function checkOtpStatus(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
        ]);

        $phone = $request->phone;
        
        return response()->json([
            'success' => true,
            'remaining_attempts' => $this->smsService->getRemainingAttempts($phone),
            'can_send' => $this->smsService->canSendOtp($phone),
        ]);
    }
}
