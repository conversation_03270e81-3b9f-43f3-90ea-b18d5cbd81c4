@extends('layouts.admin')

@section('title', 'Edit Team Member')
@section('page-title', 'Edit Team Member: ' . $team->name)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.team.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Team
        </a>
        <a href="{{ route('admin.cms.team.show', $team) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Details
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.cms.team.update', $team) }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name', $team->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position/Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('position') is-invalid @enderror"
                                       id="position" name="position" value="{{ old('position', $team->position) }}" required>
                                @error('position')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control @error('department') is-invalid @enderror"
                                       id="department" name="department" value="{{ old('department', $team->department) }}">
                                @error('department')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sort_order" class="form-label">Display Order</label>
                                <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                       id="sort_order" name="sort_order" value="{{ old('sort_order', $team->sort_order) }}"
                                       min="0" step="1">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bio" class="form-label">Biography</label>
                            <textarea class="form-control @error('bio') is-invalid @enderror"
                                      id="bio" name="bio" rows="4"
                                      placeholder="Brief description about the team member...">{{ old('bio', $team->bio) }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Maximum 1000 characters</small>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror"
                                       id="email" name="email" value="{{ old('email', $team->email) }}">
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                       id="phone" name="phone" value="{{ old('phone', $team->phone) }}">
                                @error('phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Social Media Links</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="linkedin_url" class="form-label">
                                    <i class="fab fa-linkedin text-info me-1"></i> LinkedIn URL
                                </label>
                                <input type="url" class="form-control @error('social_links.linkedin') is-invalid @enderror"
                                       id="linkedin_url" name="social_links[linkedin]"
                                       value="{{ old('social_links.linkedin', $team->social_links['linkedin'] ?? '') }}"
                                       placeholder="https://linkedin.com/in/username">
                                @error('social_links.linkedin')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="twitter_url" class="form-label">
                                    <i class="fab fa-twitter text-primary me-1"></i> Twitter URL
                                </label>
                                <input type="url" class="form-control @error('social_links.twitter') is-invalid @enderror"
                                       id="twitter_url" name="social_links[twitter]"
                                       value="{{ old('social_links.twitter', $team->social_links['twitter'] ?? '') }}"
                                       placeholder="https://twitter.com/username">
                                @error('social_links.twitter')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="facebook_url" class="form-label">
                                    <i class="fab fa-facebook text-primary me-1"></i> Facebook URL
                                </label>
                                <input type="url" class="form-control @error('social_links.facebook') is-invalid @enderror"
                                       id="facebook_url" name="social_links[facebook]"
                                       value="{{ old('social_links.facebook', $team->social_links['facebook'] ?? '') }}"
                                       placeholder="https://facebook.com/username">
                                @error('social_links.facebook')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="instagram_url" class="form-label">
                                    <i class="fab fa-instagram text-danger me-1"></i> Instagram URL
                                </label>
                                <input type="url" class="form-control @error('social_links.instagram') is-invalid @enderror"
                                       id="instagram_url" name="social_links[instagram]"
                                       value="{{ old('social_links.instagram', $team->social_links['instagram'] ?? '') }}"
                                       placeholder="https://instagram.com/username">
                                @error('social_links.instagram')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Display Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" {{ old('is_active', $team->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <strong>Active Status</strong>
                                <br><small class="text-muted">Show this team member on the website</small>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="{{ route('admin.cms.team.show', $team) }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update Team Member
                    </button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Photo</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div id="photo-preview" class="mb-3">
                            @if($team->photo)
                                <img src="{{ Storage::url($team->photo) }}"
                                     alt="{{ $team->name }}"
                                     class="rounded-circle img-thumbnail mx-auto"
                                     style="width: 150px; height: 150px; object-fit: cover;" id="current-photo">
                                <div class="text-muted small mt-2">Current: {{ basename($team->photo) }}</div>
                            @else
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto"
                                     style="width: 150px; height: 150px;" id="default-avatar">
                                    <i class="fas fa-user fa-4x text-muted"></i>
                                </div>
                            @endif
                            <img id="preview-image" src="#" alt="Preview"
                                 class="rounded-circle img-thumbnail mx-auto"
                                 style="width: 150px; height: 150px; object-fit: cover; display: none;">
                        </div>
                        
                        <input type="file" class="form-control @error('photo') is-invalid @enderror" 
                               id="photo" name="photo" accept="image/*" onchange="previewImage(this)">
                        @error('photo')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted d-block mt-2">
                            Leave empty to keep current photo<br>
                            Supported formats: JPG, PNG, GIF<br>
                            Maximum size: 2MB<br>
                            Recommended: 300x300px
                        </small>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Member Info</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Created:</strong>
                        <div class="text-muted">{{ $team->created_at->format('M d, Y h:i A') }}</div>
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong>
                        <div class="text-muted">{{ $team->updated_at->format('M d, Y h:i A') }}</div>
                    </div>
                    <div class="mb-2">
                        <strong>Current Status:</strong>
                        <div>
                            <span class="badge bg-{{ $team->is_active ? 'success' : 'secondary' }}">
                                {{ $team->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use a professional headshot for the best results</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Keep the biography concise and engaging</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Social media links help visitors connect</small>
                        </li>
                        <li>
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use display order to control team member sequence</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function previewImage(input) {
        const preview = document.getElementById('preview-image');
        const currentPhoto = document.getElementById('current-photo');
        const defaultAvatar = document.getElementById('default-avatar');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
                if (currentPhoto) currentPhoto.style.display = 'none';
                if (defaultAvatar) defaultAvatar.style.display = 'none';
            };
            
            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
            if (currentPhoto) {
                currentPhoto.style.display = 'block';
            } else if (defaultAvatar) {
                defaultAvatar.style.display = 'flex';
            }
        }
    }

    // Character counter for bio
    document.getElementById('bio').addEventListener('input', function() {
        const maxLength = 1000;
        const currentLength = this.value.length;
        const remaining = maxLength - currentLength;
        
        // Find or create counter element
        let counter = document.getElementById('bio-counter');
        if (!counter) {
            counter = document.createElement('small');
            counter.id = 'bio-counter';
            counter.className = 'text-muted';
            this.parentNode.appendChild(counter);
        }
        
        counter.textContent = `${currentLength}/${maxLength} characters`;
        counter.className = remaining < 100 ? 'text-warning' : (remaining < 50 ? 'text-danger' : 'text-muted');
    });
</script>
@endpush
