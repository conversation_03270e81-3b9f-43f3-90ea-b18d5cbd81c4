<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\TrackingEvent;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class TrackingEventController extends Controller
{
    /**
     * Store a newly created tracking event
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        $validated = $request->validate([
            'parcel_id' => 'required|exists:parcels,id',
            'status' => 'required|string|in:pending,picked_up,in_transit,out_for_delivery,delivered,exception,returned',
            'location' => 'required|string|max:255',
            'description' => 'required|string',
            'event_date' => 'required|date',
            'is_public' => 'boolean',
        ]);

        $validated['is_public'] = $request->has('is_public');

        $trackingEvent = TrackingEvent::create($validated);

        // Update parcel status if this is the latest event
        $parcel = Parcel::find($validated['parcel_id']);
        $latestEvent = $parcel->trackingEvents()->orderBy('event_date', 'desc')->first();

        if ($latestEvent->id === $trackingEvent->id) {
            $parcel->update(['status' => $validated['status']]);

            // Set delivered_at if status is delivered
            if ($validated['status'] === 'delivered') {
                $parcel->update(['delivered_at' => $validated['event_date']]);
            }
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Tracking event added successfully',
                'event' => $trackingEvent->load('parcel'),
            ]);
        }

        return redirect()->back()->with('success', 'Tracking event added successfully.');
    }

    /**
     * Update the specified tracking event
     */
    public function update(Request $request, TrackingEvent $trackingEvent): RedirectResponse|JsonResponse
    {
        $validated = $request->validate([
            'status' => 'required|string|in:pending,picked_up,in_transit,out_for_delivery,delivered,exception,returned',
            'location' => 'required|string|max:255',
            'description' => 'required|string',
            'event_date' => 'required|date',
            'is_public' => 'boolean',
        ]);

        $validated['is_public'] = $request->has('is_public');

        $trackingEvent->update($validated);

        // Update parcel status if this is the latest event
        $parcel = $trackingEvent->parcel;
        $latestEvent = $parcel->trackingEvents()->orderBy('event_date', 'desc')->first();

        if ($latestEvent->id === $trackingEvent->id) {
            $parcel->update(['status' => $validated['status']]);

            // Set delivered_at if status is delivered
            if ($validated['status'] === 'delivered') {
                $parcel->update(['delivered_at' => $validated['event_date']]);
            } elseif ($parcel->status === 'delivered' && $validated['status'] !== 'delivered') {
                // Clear delivered_at if status changed from delivered
                $parcel->update(['delivered_at' => null]);
            }
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Tracking event updated successfully',
                'event' => $trackingEvent->fresh(),
            ]);
        }

        return redirect()->back()->with('success', 'Tracking event updated successfully.');
    }

    /**
     * Remove the specified tracking event
     */
    public function destroy(TrackingEvent $trackingEvent): RedirectResponse|JsonResponse
    {
        $parcel = $trackingEvent->parcel;
        $trackingEvent->delete();

        // Update parcel status to the latest remaining event
        $latestEvent = $parcel->trackingEvents()->orderBy('event_date', 'desc')->first();

        if ($latestEvent) {
            $parcel->update(['status' => $latestEvent->status]);

            // Update delivered_at based on latest event
            if ($latestEvent->status === 'delivered') {
                $parcel->update(['delivered_at' => $latestEvent->event_date]);
            } else {
                $parcel->update(['delivered_at' => null]);
            }
        } else {
            // No events left, set to pending
            $parcel->update([
                'status' => 'pending',
                'delivered_at' => null,
            ]);
        }

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Tracking event deleted successfully',
            ]);
        }

        return redirect()->back()->with('success', 'Tracking event deleted successfully.');
    }

    /**
     * Get tracking events for a parcel (AJAX)
     */
    public function getEvents(Parcel $parcel): JsonResponse
    {
        $events = $parcel->trackingEvents()
                        ->orderBy('event_date', 'desc')
                        ->get()
                        ->map(function ($event) {
                            return [
                                'id' => $event->id,
                                'status' => $event->status,
                                'formatted_status' => $event->getFormattedStatus(),
                                'location' => $event->location,
                                'description' => $event->description,
                                'event_date' => $event->event_date->format('Y-m-d H:i:s'),
                                'formatted_date' => $event->event_date->format('M d, Y h:i A'),
                                'is_public' => $event->is_public,
                                'icon' => $event->getStatusIcon(),
                                'color' => $event->getStatusColor(),
                            ];
                        });

        return response()->json([
            'success' => true,
            'events' => $events,
        ]);
    }
}
