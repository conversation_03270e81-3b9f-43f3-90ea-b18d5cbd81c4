<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LiveChatSession;
use App\Models\LiveChatMessage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Validator;

class LiveChatController extends Controller
{
    /**
     * Display live chat dashboard
     */
    public function index(): View
    {
        $activeSessions = LiveChatSession::active()
            ->with(['messages' => function ($query) {
                $query->latest()->limit(1);
            }, 'assignedStaff'])
            ->withCount(['messages as unread_count' => function ($query) {
                $query->where('sender_type', 'visitor')->where('is_read', false);
            }])
            ->orderBy('last_activity', 'desc')
            ->get();

        $waitingSessions = LiveChatSession::waiting()
            ->with(['messages' => function ($query) {
                $query->latest()->limit(1);
            }])
            ->withCount(['messages as unread_count' => function ($query) {
                $query->where('sender_type', 'visitor')->where('is_read', false);
            }])
            ->orderBy('created_at', 'desc')
            ->get();

        $stats = [
            'total_active' => LiveChatSession::active()->count(),
            'total_waiting' => LiveChatSession::waiting()->count(),
            'total_closed' => LiveChatSession::closed()->count(),
            'total_unread' => LiveChatMessage::fromVisitor()->unread()->count(),
            'my_assigned' => LiveChatSession::where('assigned_to', auth()->id())->active()->count(),
        ];

        return view('admin.live-chat.index', compact('activeSessions', 'waitingSessions', 'stats'));
    }

    /**
     * Display chat history (closed sessions)
     */
    public function history(Request $request): View
    {
        $query = LiveChatSession::closed()
            ->with(['messages' => function ($query) {
                $query->latest()->limit(1);
            }, 'assignedStaff'])
            ->withCount(['messages as total_messages']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('visitor_name', 'like', "%{$search}%")
                  ->orWhere('visitor_email', 'like', "%{$search}%")
                  ->orWhere('session_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        $closedSessions = $query->orderBy('updated_at', 'desc')->paginate(20);

        // Get staff members for filter
        $staffMembers = \App\Models\User::whereIn('role', ['admin', 'staff'])
            ->orderBy('name')
            ->get();

        return view('admin.live-chat.history', compact('closedSessions', 'staffMembers'));
    }

    /**
     * Get detailed session information
     */
    public function getSessionDetails(LiveChatSession $session): JsonResponse
    {
        $session->load(['messages.staff', 'assignedStaff']);

        // Calculate session statistics
        $totalMessages = $session->messages->count();
        $visitorMessages = $session->messages->where('sender_type', 'visitor')->count();
        $staffMessages = $session->messages->where('sender_type', 'staff')->count();

        // Calculate response times
        $responseTimeStats = $this->calculateResponseTimes($session);

        // Get message timeline
        $messageTimeline = $session->messages->map(function ($message) {
            return [
                'id' => $message->id,
                'sender_type' => $message->sender_type,
                'sender_name' => $message->sender_name,
                'message' => $message->message,
                'created_at' => $message->created_at->format('M j, Y g:i A'),
                'time_ago' => $message->created_at->diffForHumans(),
                'is_read' => $message->is_read,
                'read_at' => $message->read_at?->format('M j, Y g:i A'),
            ];
        });

        // Session duration
        $duration = $session->created_at->diffInMinutes($session->updated_at);
        $durationFormatted = $this->formatDuration($duration);

        $sessionDetails = [
            'session' => [
                'id' => $session->id,
                'session_id' => $session->session_id,
                'status' => $session->status,
                'visitor_name' => $session->visitor_name ?? 'Anonymous',
                'visitor_email' => $session->visitor_email,
                'visitor_ip' => $session->visitor_ip,
                'user_agent' => $session->user_agent,
                'assigned_staff' => $session->assignedStaff?->name,
                'created_at' => $session->created_at->format('M j, Y g:i A'),
                'updated_at' => $session->updated_at->format('M j, Y g:i A'),
                'last_activity' => $session->last_activity?->format('M j, Y g:i A'),
                'duration' => $durationFormatted,
                'duration_minutes' => $duration,
            ],
            'statistics' => [
                'total_messages' => $totalMessages,
                'visitor_messages' => $visitorMessages,
                'staff_messages' => $staffMessages,
                'avg_response_time' => $responseTimeStats['avg_response_time'],
                'first_response_time' => $responseTimeStats['first_response_time'],
                'longest_response_time' => $responseTimeStats['longest_response_time'],
            ],
            'timeline' => $messageTimeline,
            'browser_info' => $this->parseBrowserInfo($session->user_agent),
        ];

        return response()->json([
            'success' => true,
            'data' => $sessionDetails
        ]);
    }

    /**
     * Calculate response time statistics
     */
    private function calculateResponseTimes(LiveChatSession $session): array
    {
        $messages = $session->messages->sortBy('created_at');
        $responseTimes = [];
        $firstResponseTime = null;
        $firstVisitorMessage = null;

        foreach ($messages as $index => $message) {
            if ($message->sender_type === 'visitor' && !$firstVisitorMessage) {
                $firstVisitorMessage = $message;
            }

            if ($message->sender_type === 'staff') {
                // Find the previous visitor message
                $previousVisitorMessage = null;
                for ($i = $index - 1; $i >= 0; $i--) {
                    if ($messages->values()[$i]->sender_type === 'visitor') {
                        $previousVisitorMessage = $messages->values()[$i];
                        break;
                    }
                }

                if ($previousVisitorMessage) {
                    $responseTime = $previousVisitorMessage->created_at->diffInMinutes($message->created_at);
                    $responseTimes[] = $responseTime;

                    // Track first response time
                    if (!$firstResponseTime && $firstVisitorMessage && $previousVisitorMessage->id === $firstVisitorMessage->id) {
                        $firstResponseTime = $responseTime;
                    }
                }
            }
        }

        return [
            'avg_response_time' => count($responseTimes) > 0 ? round(array_sum($responseTimes) / count($responseTimes), 1) : 0,
            'first_response_time' => $firstResponseTime ?? 0,
            'longest_response_time' => count($responseTimes) > 0 ? max($responseTimes) : 0,
        ];
    }

    /**
     * Format duration in human readable format
     */
    private function formatDuration(int $minutes): string
    {
        if ($minutes < 60) {
            return $minutes . ' minute' . ($minutes !== 1 ? 's' : '');
        }

        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;

        $formatted = $hours . ' hour' . ($hours !== 1 ? 's' : '');
        if ($remainingMinutes > 0) {
            $formatted .= ' ' . $remainingMinutes . ' minute' . ($remainingMinutes !== 1 ? 's' : '');
        }

        return $formatted;
    }

    /**
     * Parse browser information from user agent
     */
    private function parseBrowserInfo(?string $userAgent): array
    {
        if (!$userAgent) {
            return [
                'browser' => 'Unknown',
                'platform' => 'Unknown',
                'device' => 'Unknown'
            ];
        }

        // Simple browser detection
        $browser = 'Unknown';
        $platform = 'Unknown';
        $device = 'Desktop';

        // Browser detection
        if (strpos($userAgent, 'Chrome') !== false) {
            $browser = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $browser = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $browser = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $browser = 'Edge';
        }

        // Platform detection
        if (strpos($userAgent, 'Windows') !== false) {
            $platform = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $platform = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $platform = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $platform = 'Android';
            $device = 'Mobile';
        } elseif (strpos($userAgent, 'iOS') !== false || strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
            $platform = 'iOS';
            $device = strpos($userAgent, 'iPad') !== false ? 'Tablet' : 'Mobile';
        }

        // Mobile detection
        if (strpos($userAgent, 'Mobile') !== false || strpos($userAgent, 'Android') !== false) {
            $device = 'Mobile';
        } elseif (strpos($userAgent, 'Tablet') !== false || strpos($userAgent, 'iPad') !== false) {
            $device = 'Tablet';
        }

        return [
            'browser' => $browser,
            'platform' => $platform,
            'device' => $device,
            'full_user_agent' => $userAgent
        ];
    }

    /**
     * Show specific chat session
     */
    public function show(LiveChatSession $session): View
    {
        $session->load(['messages.staff', 'assignedStaff']);
        
        // Mark visitor messages as read
        $session->markVisitorMessagesAsRead();

        return view('admin.live-chat.show', compact('session'));
    }

    /**
     * Assign session to staff member
     */
    public function assign(Request $request, LiveChatSession $session): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'staff_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $staffId = $request->staff_id ?: auth()->id();

        $session->update([
            'assigned_to' => $staffId,
            'status' => 'active',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Session assigned successfully'
        ]);
    }

    /**
     * Send message from staff
     */
    public function sendMessage(Request $request, LiveChatSession $session): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $message = LiveChatMessage::create([
            'session_id' => $session->id,
            'sender_type' => 'staff',
            'staff_id' => auth()->id(),
            'message' => $request->message,
        ]);

        // Update session activity
        $session->updateActivity();

        // TODO: Broadcast the message to visitor in real-time
        // broadcast(new NewChatMessage($message));

        return response()->json([
            'success' => true,
            'message' => 'Message sent successfully',
            'data' => [
                'id' => $message->id,
                'message' => $message->message,
                'sender_name' => auth()->user()->name,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Get new messages for staff (polling)
     */
    public function getNewMessages(Request $request, LiveChatSession $session): JsonResponse
    {
        $lastMessageId = $request->query('last_message_id', 0);
        
        $newMessages = $session->messages()
            ->where('id', '>', $lastMessageId)
            ->where('sender_type', 'visitor') // Only get visitor messages
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender_type' => $message->sender_type,
                    'sender_name' => $message->sender_name,
                    'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                ];
            });

        // Mark new visitor messages as read
        if ($newMessages->isNotEmpty()) {
            $session->markVisitorMessagesAsRead();
        }

        return response()->json([
            'success' => true,
            'new_messages' => $newMessages,
        ]);
    }

    /**
     * Close chat session
     */
    public function close(LiveChatSession $session): JsonResponse
    {
        $session->update(['status' => 'closed']);

        return response()->json([
            'success' => true,
            'message' => 'Chat session closed successfully'
        ]);
    }

    /**
     * Reopen closed chat session
     */
    public function reopen(LiveChatSession $session): JsonResponse
    {
        // Only allow reopening closed sessions
        if ($session->status !== 'closed') {
            return response()->json([
                'success' => false,
                'message' => 'Only closed sessions can be reopened'
            ], 422);
        }

        // Reopen the session and assign to current staff member
        $session->update([
            'status' => 'active',
            'assigned_to' => auth()->id(),
            'last_activity' => now(),
        ]);

        // Add a system message to indicate the session was reopened
        LiveChatMessage::create([
            'session_id' => $session->id,
            'sender_type' => 'system',
            'staff_id' => auth()->id(),
            'message' => 'Chat session reopened by ' . auth()->user()->name,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Chat session reopened successfully'
        ]);
    }

    /**
     * Get live chat statistics for dashboard
     */
    public function getStats(): JsonResponse
    {
        $stats = [
            'total_active' => LiveChatSession::active()->count(),
            'total_waiting' => LiveChatSession::waiting()->count(),
            'total_closed' => LiveChatSession::closed()->count(),
            'total_unread' => LiveChatMessage::fromVisitor()->unread()->count(),
            'my_assigned' => LiveChatSession::where('assigned_to', auth()->id())->active()->count(),
            'recent_sessions' => LiveChatSession::with(['messages' => function ($query) {
                $query->latest()->limit(1);
            }])
            ->withCount(['messages as unread_count' => function ($query) {
                $query->where('sender_type', 'visitor')->where('is_read', false);
            }])
            ->orderBy('last_activity', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($session) {
                return [
                    'id' => $session->id,
                    'session_id' => $session->session_id,
                    'visitor_name' => $session->visitor_name ?? 'Anonymous',
                    'status' => $session->status,
                    'unread_count' => $session->unread_count,
                    'last_activity' => $session->last_activity?->diffForHumans(),
                    'latest_message' => $session->messages->first()?->message,
                ];
            }),
        ];

        return response()->json($stats);
    }
}
