<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JobApplication;
use App\Models\Career;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class JobApplicationController extends Controller
{
    /**
     * Display a listing of job applications
     */
    public function index(): View
    {
        $applications = JobApplication::with(['career', 'reviewer'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $statusCounts = [
            'pending' => JobApplication::pending()->count(),
            'reviewing' => JobApplication::reviewing()->count(),
            'shortlisted' => JobApplication::shortlisted()->count(),
            'interviewed' => JobApplication::where('status', 'interviewed')->count(),
            'rejected' => JobApplication::where('status', 'rejected')->count(),
            'hired' => JobApplication::where('status', 'hired')->count(),
        ];

        return view('admin.job-applications.index', compact('applications', 'statusCounts'));
    }

    /**
     * Display the specified job application
     */
    public function show(JobApplication $jobApplication): View
    {
        $jobApplication->load(['career', 'reviewer']);

        return view('admin.job-applications.show', compact('jobApplication'));
    }

    /**
     * Update the specified job application status
     */
    public function updateStatus(Request $request, JobApplication $jobApplication): RedirectResponse
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,reviewing,shortlisted,interviewed,rejected,hired',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $jobApplication->update([
            'status' => $validated['status'],
            'admin_notes' => $validated['admin_notes'],
            'reviewed_at' => now(),
            'reviewed_by' => auth()->id(),
        ]);

        return redirect()->back()
                        ->with('success', 'Application status updated successfully.');
    }

    /**
     * Remove the specified job application
     */
    public function destroy(JobApplication $jobApplication): RedirectResponse
    {
        // Delete resume file if exists
        if ($jobApplication->resume_path && \Storage::disk('public')->exists($jobApplication->resume_path)) {
            \Storage::disk('public')->delete($jobApplication->resume_path);
        }

        $jobApplication->delete();

        return redirect()->route('admin.cms.job-applications.index')
                        ->with('success', 'Job application deleted successfully.');
    }

    /**
     * Handle bulk actions on job applications
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:delete,update_status',
            'applications' => 'required|array',
            'applications.*' => 'exists:job_applications,id',
            'status' => 'required_if:action,update_status|in:pending,reviewing,shortlisted,interviewed,rejected,hired',
        ]);

        $applications = JobApplication::whereIn('id', $validated['applications']);

        switch ($validated['action']) {
            case 'delete':
                // Delete resume files
                $applications->get()->each(function ($application) {
                    if ($application->resume_path && \Storage::disk('public')->exists($application->resume_path)) {
                        \Storage::disk('public')->delete($application->resume_path);
                    }
                });

                $count = $applications->delete();
                return redirect()->back()
                                ->with('success', "{$count} applications deleted successfully.");

            case 'update_status':
                $count = $applications->update([
                    'status' => $validated['status'],
                    'reviewed_at' => now(),
                    'reviewed_by' => auth()->id(),
                ]);

                return redirect()->back()
                                ->with('success', "{$count} applications updated successfully.");
        }

        return redirect()->back();
    }
}
