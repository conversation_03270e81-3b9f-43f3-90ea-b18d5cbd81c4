# Payment UI Standards Documentation

## Overview

This document defines the standardized payment UI that must be used across all payment flows in the Atrix Logistics system. This ensures a consistent, professional, and secure payment experience for customers.

## Standard Payment UI Components

### 1. Security Notice
Every payment page must include a security notice at the top:

```html
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-shield-alt fa-2x me-3"></i>
        <div>
            <h6 class="mb-1">🔒 Secure Payment Processing</h6>
            <p class="mb-0">Your payment information is processed securely through industry-standard encryption and PCI-compliant payment gateways. We never store your card details.</p>
        </div>
    </div>
</div>
```

### 2. Order/Item Summary
Display a clear summary of what the customer is paying for:

```html
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-receipt me-2"></i>
            Order Summary
        </h5>
    </div>
    <div class="card-body">
        <!-- Summary content here -->
    </div>
</div>
```

### 3. Payment Method Selection
Use card-based selection with consistent styling:

```html
<div class="row">
    @foreach($paymentMethods as $method => $config)
        @if($config['enabled'])
        <div class="col-md-6 mb-3">
            <div class="payment-method-card">
                <input type="radio" class="btn-check" name="payment_method" id="method_{{ $method }}" value="{{ $method }}" {{ $loop->first ? 'checked' : '' }}>
                <label class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" for="method_{{ $method }}">
                    <i class="fas fa-{{ $config['icon'] }} fa-2x mb-2"></i>
                    <h6 class="mb-1">{{ $config['name'] }}</h6>
                    <small class="text-muted text-center">{{ $config['description'] }}</small>
                    @if($config['supports_3ds'])
                        <span class="badge bg-success mt-2">
                            <i class="fas fa-shield-alt me-1"></i>3D Secure
                        </span>
                    @endif
                </label>
            </div>
        </div>
        @endif
    @endforeach
</div>
```

### 4. Payment Method Details
Each payment method should have detailed information that appears when selected:

```html
<div class="mt-4">
    <div id="stripe-info" class="payment-info" style="display: none;">
        <div class="alert alert-light">
            <h6><i class="fab fa-cc-stripe me-2"></i>Stripe Payment</h6>
            <p class="mb-2">Secure card payment processed by Stripe. Supports all major credit and debit cards with 3D Secure authentication.</p>
        </div>
        <!-- Stripe Elements Container -->
        <div id="stripe-elements-container" style="display: none;">
            <div class="card">
                <div class="card-body">
                    <h6 class="mb-3">Enter Card Details</h6>
                    <div id="card-element" class="form-control" style="height: 40px; padding: 10px;">
                        <!-- Stripe Elements will create form elements here -->
                    </div>
                    <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Other payment method details... -->
</div>
```

### 5. Security Features Display
Show security badges to build trust:

```html
<div class="row mt-4">
    <div class="col-md-4 text-center">
        <i class="fas fa-lock fa-2x text-success mb-2"></i>
        <h6>SSL Encrypted</h6>
        <small class="text-muted">256-bit SSL encryption</small>
    </div>
    <div class="col-md-4 text-center">
        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
        <h6>PCI Compliant</h6>
        <small class="text-muted">Industry standard security</small>
    </div>
    <div class="col-md-4 text-center">
        <i class="fas fa-user-shield fa-2x text-success mb-2"></i>
        <h6>Fraud Protection</h6>
        <small class="text-muted">Advanced fraud detection</small>
    </div>
</div>
```

### 6. Submit Button
Dynamic submit button that changes based on selected payment method:

```html
<div class="d-grid gap-2 mt-4">
    <button type="submit" class="btn btn-primary btn-lg" id="submitPayment">
        <i class="fas fa-credit-card me-2"></i>
        Proceed to Secure Payment
        <span class="ms-2">{{ $currency['symbol'] }}{{ number_format($amount, 2) }}</span>
    </button>
    <a href="{{ $backUrl }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Previous Page
    </a>
</div>
```

### 7. Trust Indicators
Add trust indicators at the bottom:

```html
<div class="row mt-4 text-center">
    <div class="col-12">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            Your payment information is processed securely. We use industry-standard encryption and never store your card details.
        </small>
    </div>
</div>
```

## Required CSS Styles

```css
.payment-method-card {
    height: 100%;
}

.payment-method-card .btn {
    min-height: 150px;
    transition: all 0.3s ease;
}

.payment-method-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.payment-method-card input[type="radio"]:checked + label {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.payment-info {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
```

## Required JavaScript Functions

### Payment Method Switching
```javascript
function showPaymentInfo() {
    // Hide all payment info sections
    paymentInfos.forEach(info => info.style.display = 'none');

    // Show the selected payment method info
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
    const selectedInfo = document.getElementById(selectedMethod + '-info');
    if (selectedInfo) {
        selectedInfo.style.display = 'block';
    }

    // Update submit button text
    updateSubmitButton(selectedMethod);
}
```

### Copy to Clipboard Function
```javascript
function copyToClipboard(text, label = 'Text') {
    navigator.clipboard.writeText(text).then(function() {
        // Show success toast
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${label} copied to clipboard!</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    });
}
```

## Payment Method Configuration

Payment methods must be configured with these required keys:

```php
$methods['method_name'] = [
    'enabled' => true,                    // Required: Whether method is available
    'name' => 'Display Name',            // Required: User-friendly name
    'description' => 'Method description', // Required: Brief description
    'icon' => 'fab fa-cc-stripe',        // Required: FULL FontAwesome class (fab/fas fa-icon-name)
    'color' => 'bootstrap-color',        // Required: Bootstrap color class
    'supports_cards' => true/false,      // Required: Whether it supports cards
    'supports_3ds' => true/false,        // Required: Whether it supports 3D Secure
    'processing_fee' => 0,               // Optional: Processing fee percentage
];
```

### **IMPORTANT: Icon Configuration**

Icons must include the **FULL FontAwesome class**:
- ✅ **Correct**: `'icon' => 'fab fa-cc-stripe'`
- ✅ **Correct**: `'icon' => 'fas fa-university'`
- ✅ **Correct**: `'icon' => 'fab fa-paypal'`
- ❌ **Wrong**: `'icon' => 'cc-stripe'` (missing fab fa-)
- ❌ **Wrong**: `'icon' => 'university'` (missing fas fa-)

**Standard Icons for Payment Methods:**
- **Stripe**: `fab fa-cc-stripe`
- **PayPal**: `fab fa-paypal`
- **Credit Card (Generic)**: `fas fa-credit-card`
- **Bank Transfer**: `fas fa-university`
- **Razorpay**: `fas fa-credit-card`
- **Square**: `fas fa-credit-card`

## Implementation Checklist

When implementing a new payment flow, ensure:

- [ ] Security notice is displayed at the top
- [ ] Order/item summary is clear and detailed
- [ ] Payment methods use the card-based selection UI
- [ ] Each payment method has detailed information
- [ ] Security features are displayed
- [ ] Submit button is dynamic and shows amount
- [ ] Trust indicators are at the bottom
- [ ] CSS styles are included
- [ ] JavaScript functions are implemented
- [ ] Copy-to-clipboard works for account details
- [ ] Payment method configuration includes all required keys
- [ ] Form validation is implemented
- [ ] Error handling is consistent
- [ ] Loading states are shown during processing

## File Locations

### Templates
- **Parcel Payment**: `resources/views/customer/payments/show.blade.php` (Reference implementation)
- **Order Payment**: `resources/views/customer/payments/order-payment.blade.php` (Standardized)

### Controllers
- **Payment Controller**: `app/Http/Controllers/Customer/PaymentController.php`
- **Method**: `getAvailablePaymentMethods()` - Returns standardized payment method configuration

### Routes
- Parcel payment: `/customer/payments/{parcel}`
- Order payment: `/customer/orders/{order}/pay`

## Notes for Developers

1. **Always use the reference implementation** from `show.blade.php` when creating new payment flows
2. **Never hardcode payment details** - always fetch from SiteSetting model
3. **Include all security indicators** to build customer trust
4. **Test all payment methods** before deploying
5. **Ensure responsive design** works on mobile devices
6. **Follow the exact HTML structure** for consistency
7. **Use the standardized JavaScript functions** for common functionality

This standardized UI ensures all payment flows provide a consistent, professional, and secure experience for customers across the entire Atrix Logistics platform.

## Complete Implementation Example

### Controller Method
```php
public function showPayment($item): View|RedirectResponse
{
    // Get available payment methods
    $paymentMethods = $this->getAvailablePaymentMethods();

    // Get currency settings
    $currency = [
        'code' => SiteSetting::getValue('base_currency', 'USD'),
        'symbol' => SiteSetting::getValue('currency_symbol', '$'),
    ];

    return view('customer.payments.standard-payment', compact('item', 'paymentMethods', 'currency'));
}
```

### Blade Template Structure
```blade
@extends('layouts.customer')

@section('title', 'Payment - Item #' . $item->id)
@section('page-title', 'Secure Payment')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- 1. Security Notice -->
        <!-- 2. Item Summary -->
        <!-- 3. Payment Methods Card -->
        <!-- 4. Trust Indicators -->
    </div>
</div>
@endsection

@push('styles')
<!-- Standard payment styles -->
@endpush

@push('scripts')
<!-- Standard payment scripts -->
@endpush
```

### Manual Payment Details Template
For bank transfers and manual payments, always include:

```html
<div id="admin_approval-info" class="payment-info" style="display: none;">
    <div class="alert alert-warning">
        <h6><i class="fas fa-university me-2"></i>Bank Transfer / Manual Payment</h6>
        <p class="mb-3">Choose this option to pay via bank transfer or other offline methods.</p>

        <!-- Bank Account Details -->
        @if(\App\Models\SiteSetting::get('bank_name') || \App\Models\SiteSetting::get('bank_account_number'))
        <div class="card mb-3">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="fas fa-university me-2"></i>Bank Transfer Details</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    @if(\App\Models\SiteSetting::get('bank_name'))
                    <div class="col-md-6 mb-2">
                        <strong>Bank Name:</strong><br>
                        <span class="text-primary">{{ \App\Models\SiteSetting::get('bank_name') }}</span>
                    </div>
                    @endif

                    @if(\App\Models\SiteSetting::getValue('bank_account_number'))
                    <div class="col-md-6 mb-2">
                        <strong>Account Number:</strong><br>
                        <code class="bg-light p-1">{{ \App\Models\SiteSetting::getValue('bank_account_number') }}</code>
                        <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ \App\Models\SiteSetting::getValue('bank_account_number') }}', 'Account number')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif

        <!-- General Instructions -->
        <div class="alert alert-secondary">
            <h6><i class="fas fa-info-circle me-2"></i>Payment Instructions</h6>
            <ol class="mb-2">
                <li>Use the account details above to make your payment</li>
                <li>Include your reference number <strong>{{ $item->reference }}</strong> as reference</li>
                <li>Send payment confirmation to {{ \App\Models\SiteSetting::get('support_email', '<EMAIL>') }}</li>
                <li>Your item will be processed once payment is verified</li>
            </ol>
            <p class="mb-0"><strong>Note:</strong> Your payment will be marked as pending until we receive and verify your payment.</p>
        </div>
    </div>
</div>
```

## Error Handling Standards

### Form Validation Errors
```html
@if ($errors->any())
    <div class="alert alert-danger">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Payment Error</h6>
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
```

### Success Messages
```html
@if (session('success'))
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
    </div>
@endif
```

## Accessibility Requirements

1. **Keyboard Navigation**: All payment methods must be accessible via keyboard
2. **Screen Reader Support**: Use proper ARIA labels and roles
3. **Color Contrast**: Ensure sufficient contrast for all text and buttons
4. **Focus Indicators**: Clear focus indicators for all interactive elements

```html
<!-- Example with accessibility attributes -->
<input type="radio"
       class="btn-check"
       name="payment_method"
       id="method_stripe"
       value="stripe"
       aria-describedby="stripe-description">
<label class="btn btn-outline-primary"
       for="method_stripe"
       role="button"
       tabindex="0">
    <span id="stripe-description" class="sr-only">Secure credit card payment via Stripe</span>
    <!-- Visual content -->
</label>
```

## Testing Checklist

Before deploying any payment UI:

### Functional Testing
- [ ] All payment methods display correctly
- [ ] Payment method switching works smoothly
- [ ] Form validation works for all fields
- [ ] Copy-to-clipboard functions work
- [ ] Submit button updates correctly
- [ ] Loading states display properly
- [ ] Error messages are clear and helpful

### Visual Testing
- [ ] UI matches the standard design exactly
- [ ] Responsive design works on mobile
- [ ] Icons display correctly
- [ ] Colors match the design system
- [ ] Animations are smooth
- [ ] Text is readable and properly sized

### Security Testing
- [ ] No sensitive data in client-side code
- [ ] CSRF tokens are included
- [ ] Form submissions are validated server-side
- [ ] Payment processing is secure
- [ ] Error messages don't leak sensitive information

## Maintenance Guidelines

1. **Regular Updates**: Keep payment method configurations up to date
2. **Security Patches**: Apply security updates promptly
3. **Performance Monitoring**: Monitor payment page load times
4. **User Feedback**: Collect and act on user feedback about payment experience
5. **A/B Testing**: Test improvements to conversion rates

## Support and Troubleshooting

### Common Issues
1. **Payment methods not showing**: Check `getAvailablePaymentMethods()` configuration
2. **Icons not displaying**: Verify FontAwesome is loaded and icon names are correct
3. **JavaScript errors**: Check browser console for errors and ensure all scripts are loaded
4. **Styling issues**: Verify CSS is loaded and Bootstrap classes are available

### Debug Mode
Enable debug mode to see detailed payment method configuration:
```php
// Add to controller for debugging
if (config('app.debug')) {
    dd($paymentMethods);
}
```

This comprehensive documentation ensures all developers can implement consistent, secure, and user-friendly payment interfaces across the Atrix Logistics platform.
