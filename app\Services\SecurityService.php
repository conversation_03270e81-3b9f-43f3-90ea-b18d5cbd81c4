<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class SecurityService
{
    /**
     * Sanitize input to prevent XSS attacks
     */
    public function sanitizeInput(string $input): string
    {
        // Remove potentially dangerous HTML tags and attributes
        $input = strip_tags($input, '<p><br><strong><em><ul><ol><li><a><h1><h2><h3><h4><h5><h6>');
        
        // Remove javascript: and data: protocols
        $input = preg_replace('/javascript:/i', '', $input);
        $input = preg_replace('/data:/i', '', $input);
        
        // Remove on* event handlers
        $input = preg_replace('/on\w+\s*=/i', '', $input);
        
        return trim($input);
    }

    /**
     * Validate and sanitize file uploads
     */
    public function validateFileUpload($file, array $allowedTypes = [], int $maxSize = 2048): array
    {
        $errors = [];
        
        if (!$file || !$file->isValid()) {
            $errors[] = 'Invalid file upload';
            return ['valid' => false, 'errors' => $errors];
        }
        
        // Check file size (in KB)
        if ($file->getSize() > $maxSize * 1024) {
            $errors[] = "File size exceeds {$maxSize}KB limit";
        }
        
        // Check file type
        if (!empty($allowedTypes)) {
            $extension = strtolower($file->getClientOriginalExtension());
            $mimeType = $file->getMimeType();
            
            if (!in_array($extension, $allowedTypes) && !in_array($mimeType, $allowedTypes)) {
                $errors[] = 'File type not allowed';
            }
        }
        
        // Check for executable files
        $dangerousExtensions = ['php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'];
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (in_array($extension, $dangerousExtensions)) {
            $errors[] = 'Executable files are not allowed';
        }
        
        // Scan file content for malicious patterns
        if ($this->containsMaliciousContent($file)) {
            $errors[] = 'File contains potentially malicious content';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'sanitized_name' => $this->sanitizeFileName($file->getClientOriginalName())
        ];
    }

    /**
     * Check if file contains malicious content
     */
    private function containsMaliciousContent($file): bool
    {
        $content = file_get_contents($file->getRealPath());
        
        // Check for common malicious patterns
        $maliciousPatterns = [
            '/<\?php/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
            '/passthru\s*\(/i',
            '/base64_decode\s*\(/i',
            '/<script/i',
            '/javascript:/i',
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Sanitize file name
     */
    public function sanitizeFileName(string $fileName): string
    {
        // Remove path traversal attempts
        $fileName = basename($fileName);
        
        // Remove special characters except dots, hyphens, and underscores
        $fileName = preg_replace('/[^a-zA-Z0-9._-]/', '', $fileName);
        
        // Prevent double extensions
        $fileName = preg_replace('/\.+/', '.', $fileName);
        
        // Ensure filename is not empty
        if (empty($fileName)) {
            $fileName = 'file_' . time();
        }
        
        return $fileName;
    }

    /**
     * Log security events
     */
    public function logSecurityEvent(string $event, array $data = [], string $level = 'warning'): void
    {
        $logData = array_merge([
            'event' => $event,
            'timestamp' => now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'user_id' => auth()->id(),
        ], $data);
        
        Log::channel('security')->{$level}($event, $logData);
    }

    /**
     * Check for suspicious activity patterns
     */
    public function detectSuspiciousActivity(Request $request): bool
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        // Check for rapid requests from same IP
        $requestKey = "requests:{$ip}";
        $requestCount = Cache::get($requestKey, 0);
        
        if ($requestCount > 100) { // More than 100 requests per minute
            $this->logSecurityEvent('Rapid requests detected', [
                'ip' => $ip,
                'request_count' => $requestCount,
            ]);
            return true;
        }
        
        Cache::put($requestKey, $requestCount + 1, now()->addMinute());
        
        // Check for suspicious user agents
        $suspiciousAgents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'nessus',
            'openvas',
            'w3af',
            'dirbuster',
            'gobuster',
        ];
        
        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                $this->logSecurityEvent('Suspicious user agent detected', [
                    'user_agent' => $userAgent,
                    'ip' => $ip,
                ]);
                return true;
            }
        }
        
        // Check for SQL injection patterns in request
        $allInput = array_merge($request->all(), [$request->getRequestUri()]);
        $sqlPatterns = [
            '/union\s+select/i',
            '/select\s+.*\s+from/i',
            '/insert\s+into/i',
            '/delete\s+from/i',
            '/drop\s+table/i',
            '/update\s+.*\s+set/i',
            '/or\s+1\s*=\s*1/i',
            '/and\s+1\s*=\s*1/i',
            '/\'\s+or\s+\'/i',
            '/\'\s+and\s+\'/i',
        ];
        
        foreach ($allInput as $input) {
            if (is_string($input)) {
                foreach ($sqlPatterns as $pattern) {
                    if (preg_match($pattern, $input)) {
                        $this->logSecurityEvent('SQL injection attempt detected', [
                            'input' => $input,
                            'ip' => $ip,
                            'url' => $request->fullUrl(),
                        ]);
                        return true;
                    }
                }
            }
        }
        
        return false;
    }

    /**
     * Generate secure token
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Validate CSRF token manually if needed
     */
    public function validateCsrfToken(string $token): bool
    {
        return hash_equals(session()->token(), $token);
    }

    /**
     * Check if IP is in whitelist
     */
    public function isIpWhitelisted(string $ip): bool
    {
        $whitelist = config('security.ip_whitelist', []);
        return in_array($ip, $whitelist);
    }

    /**
     * Check if IP is blacklisted
     */
    public function isIpBlacklisted(string $ip): bool
    {
        $blacklist = Cache::get('ip_blacklist', []);
        return in_array($ip, $blacklist);
    }

    /**
     * Add IP to temporary blacklist
     */
    public function blacklistIp(string $ip, int $minutes = 60): void
    {
        $blacklist = Cache::get('ip_blacklist', []);
        $blacklist[] = $ip;
        Cache::put('ip_blacklist', array_unique($blacklist), now()->addMinutes($minutes));
        
        $this->logSecurityEvent('IP blacklisted', [
            'ip' => $ip,
            'duration_minutes' => $minutes,
        ]);
    }
}
