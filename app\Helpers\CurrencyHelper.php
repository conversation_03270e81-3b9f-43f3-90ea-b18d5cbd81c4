<?php

namespace App\Helpers;

use App\Models\SiteSetting;

class CurrencyHelper
{
    /**
     * Format amount with currency symbol and settings
     */
    public static function format($amount, $showSymbol = true, $showCode = false): string
    {
        if ($amount === null || $amount === '') {
            return self::formatZero($showSymbol, $showCode);
        }

        $amount = (float) $amount;
        
        // Get currency settings
        $symbol = SiteSetting::getValue('currency_symbol', '$');
        $code = SiteSetting::getValue('base_currency', 'USD');
        $position = SiteSetting::getValue('currency_position', 'before');
        $decimalPlaces = SiteSetting::getValue('decimal_places', 2);
        $thousandsSeparator = SiteSetting::getValue('thousands_separator', ',');
        $decimalSeparator = SiteSetting::getValue('decimal_separator', '.');

        // Format the number
        $formattedAmount = number_format($amount, $decimalPlaces, $decimalSeparator, $thousandsSeparator);

        // Build the final string
        $result = '';
        
        if ($showSymbol && $position === 'before') {
            $result .= $symbol;
        }
        
        $result .= $formattedAmount;
        
        if ($showSymbol && $position === 'after') {
            $result .= ' ' . $symbol;
        }
        
        if ($showCode) {
            $result .= ' ' . $code;
        }

        return $result;
    }

    /**
     * Format zero amount
     */
    public static function formatZero($showSymbol = true, $showCode = false): string
    {
        return self::format(0, $showSymbol, $showCode);
    }

    /**
     * Get currency symbol
     */
    public static function getSymbol(): string
    {
        return SiteSetting::getValue('currency_symbol', '$');
    }

    /**
     * Get currency code
     */
    public static function getCode(): string
    {
        return SiteSetting::getValue('base_currency', 'USD');
    }

    /**
     * Get currency position
     */
    public static function getPosition(): string
    {
        return SiteSetting::getValue('currency_position', 'before');
    }

    /**
     * Get decimal places
     */
    public static function getDecimalPlaces(): int
    {
        return (int) SiteSetting::getValue('decimal_places', 2);
    }

    /**
     * Get thousands separator
     */
    public static function getThousandsSeparator(): string
    {
        return SiteSetting::getValue('thousands_separator', ',');
    }

    /**
     * Get decimal separator
     */
    public static function getDecimalSeparator(): string
    {
        return SiteSetting::getValue('decimal_separator', '.');
    }

    /**
     * Parse currency string to float
     */
    public static function parse($currencyString): float
    {
        if (empty($currencyString)) {
            return 0.0;
        }

        // Remove currency symbol and code
        $symbol = self::getSymbol();
        $code = self::getCode();
        $thousandsSeparator = self::getThousandsSeparator();
        $decimalSeparator = self::getDecimalSeparator();

        // Clean the string
        $cleaned = str_replace([$symbol, $code, ' '], '', $currencyString);
        
        // Replace thousands separator with empty string
        $cleaned = str_replace($thousandsSeparator, '', $cleaned);
        
        // Replace decimal separator with dot for float conversion
        if ($decimalSeparator !== '.') {
            $cleaned = str_replace($decimalSeparator, '.', $cleaned);
        }

        return (float) $cleaned;
    }

    /**
     * Get all supported currencies
     */
    public static function getSupportedCurrencies(): array
    {
        return [
            'USD' => ['name' => 'US Dollar', 'symbol' => '$', 'position' => 'before'],
            'EUR' => ['name' => 'Euro', 'symbol' => '€', 'position' => 'after'],
            'GBP' => ['name' => 'British Pound', 'symbol' => '£', 'position' => 'before'],
            'CAD' => ['name' => 'Canadian Dollar', 'symbol' => 'C$', 'position' => 'before'],
            'AUD' => ['name' => 'Australian Dollar', 'symbol' => 'A$', 'position' => 'before'],
            'JPY' => ['name' => 'Japanese Yen', 'symbol' => '¥', 'position' => 'before'],
            'CHF' => ['name' => 'Swiss Franc', 'symbol' => 'CHF', 'position' => 'after'],
            'CNY' => ['name' => 'Chinese Yuan', 'symbol' => '¥', 'position' => 'before'],
            'INR' => ['name' => 'Indian Rupee', 'symbol' => '₹', 'position' => 'before'],
            'BRL' => ['name' => 'Brazilian Real', 'symbol' => 'R$', 'position' => 'before'],
            'MXN' => ['name' => 'Mexican Peso', 'symbol' => '$', 'position' => 'before'],
            'ZAR' => ['name' => 'South African Rand', 'symbol' => 'R', 'position' => 'before'],
            'SGD' => ['name' => 'Singapore Dollar', 'symbol' => 'S$', 'position' => 'before'],
            'HKD' => ['name' => 'Hong Kong Dollar', 'symbol' => 'HK$', 'position' => 'before'],
            'NZD' => ['name' => 'New Zealand Dollar', 'symbol' => 'NZ$', 'position' => 'before'],
            'SEK' => ['name' => 'Swedish Krona', 'symbol' => 'kr', 'position' => 'after'],
            'NOK' => ['name' => 'Norwegian Krone', 'symbol' => 'kr', 'position' => 'after'],
            'DKK' => ['name' => 'Danish Krone', 'symbol' => 'kr', 'position' => 'after'],
            'PLN' => ['name' => 'Polish Zloty', 'symbol' => 'zł', 'position' => 'after'],
            'CZK' => ['name' => 'Czech Koruna', 'symbol' => 'Kč', 'position' => 'after'],
            'HUF' => ['name' => 'Hungarian Forint', 'symbol' => 'Ft', 'position' => 'after'],
            'RUB' => ['name' => 'Russian Ruble', 'symbol' => '₽', 'position' => 'after'],
            'TRY' => ['name' => 'Turkish Lira', 'symbol' => '₺', 'position' => 'before'],
            'KRW' => ['name' => 'South Korean Won', 'symbol' => '₩', 'position' => 'before'],
            'THB' => ['name' => 'Thai Baht', 'symbol' => '฿', 'position' => 'before'],
            'MYR' => ['name' => 'Malaysian Ringgit', 'symbol' => 'RM', 'position' => 'before'],
            'IDR' => ['name' => 'Indonesian Rupiah', 'symbol' => 'Rp', 'position' => 'before'],
            'PHP' => ['name' => 'Philippine Peso', 'symbol' => '₱', 'position' => 'before'],
            'VND' => ['name' => 'Vietnamese Dong', 'symbol' => '₫', 'position' => 'after'],
            'AED' => ['name' => 'UAE Dirham', 'symbol' => 'د.إ', 'position' => 'before'],
            'SAR' => ['name' => 'Saudi Riyal', 'symbol' => '﷼', 'position' => 'before'],
            'EGP' => ['name' => 'Egyptian Pound', 'symbol' => '£', 'position' => 'before'],
            'NGN' => ['name' => 'Nigerian Naira', 'symbol' => '₦', 'position' => 'before'],
            'KES' => ['name' => 'Kenyan Shilling', 'symbol' => 'KSh', 'position' => 'before'],
            'GHS' => ['name' => 'Ghanaian Cedi', 'symbol' => '₵', 'position' => 'before'],
        ];
    }

    /**
     * Get currency info by code
     */
    public static function getCurrencyInfo($code): ?array
    {
        $currencies = self::getSupportedCurrencies();
        return $currencies[$code] ?? null;
    }

    /**
     * Format amount for input fields (without symbol)
     */
    public static function formatForInput($amount): string
    {
        if ($amount === null || $amount === '') {
            return '';
        }

        $amount = (float) $amount;
        $decimalPlaces = self::getDecimalPlaces();
        $decimalSeparator = self::getDecimalSeparator();

        return number_format($amount, $decimalPlaces, $decimalSeparator, '');
    }

    /**
     * Get currency settings as array
     */
    public static function getSettings(): array
    {
        return [
            'code' => self::getCode(),
            'symbol' => self::getSymbol(),
            'position' => self::getPosition(),
            'decimal_places' => self::getDecimalPlaces(),
            'thousands_separator' => self::getThousandsSeparator(),
            'decimal_separator' => self::getDecimalSeparator(),
        ];
    }

    /**
     * Format amount with custom settings
     */
    public static function formatWithSettings($amount, array $settings): string
    {
        if ($amount === null || $amount === '') {
            $amount = 0;
        }

        $amount = (float) $amount;
        
        $symbol = $settings['symbol'] ?? '$';
        $position = $settings['position'] ?? 'before';
        $decimalPlaces = $settings['decimal_places'] ?? 2;
        $thousandsSeparator = $settings['thousands_separator'] ?? ',';
        $decimalSeparator = $settings['decimal_separator'] ?? '.';

        // Format the number
        $formattedAmount = number_format($amount, $decimalPlaces, $decimalSeparator, $thousandsSeparator);

        // Build the final string
        $result = '';
        
        if ($position === 'before') {
            $result .= $symbol;
        }
        
        $result .= $formattedAmount;
        
        if ($position === 'after') {
            $result .= ' ' . $symbol;
        }

        return $result;
    }
}
