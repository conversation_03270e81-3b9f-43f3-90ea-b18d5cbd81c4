<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class CartController extends Controller
{
    /**
     * Display the cart
     */
    public function index(): View
    {
        $cart = Cart::getCurrent();
        $cart->load(['items.product']);

        return view('frontend.cart.index', compact('cart'));
    }

    /**
     * Add product to cart
     */
    public function add(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'integer|min:1|max:100',
        ]);

        /** @var Product $product */
        $product = Product::findOrFail($request->product_id);
        $quantity = $request->quantity ?? 1;

        // Check if product price should be shown and has a valid price
        if (!$product->shouldShowPrice()) {
            return response()->json([
                'success' => false,
                'message' => 'This product requires a quote. Please use the "Request Quote" button instead.',
            ], 400);
        }

        // Check stock if managed
        if ($product->manage_stock && $product->stock_quantity < $quantity) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock. Only ' . $product->stock_quantity . ' items available.',
            ], 400);
        }

        // Check if user is logged in
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to add items to cart.',
                'redirect' => route('customer.login'),
            ], 401);
        }

        $cart = Cart::getCurrent();
        $cartItem = $cart->addProduct($product, $quantity);

        return response()->json([
            'success' => true,
            'message' => 'Product added to cart successfully.',
            'cart_count' => $cart->item_count,
            'cart_total' => number_format($cart->total_amount, 2),
        ]);
    }

    /**
     * Update cart item quantity
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:0|max:100',
        ]);

        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to update cart.',
            ], 401);
        }

        /** @var Product $product */
        $product = Product::findOrFail($request->product_id);
        $cart = Cart::getCurrent();

        if ($request->quantity == 0) {
            $cart->removeProduct($product);
            $message = 'Product removed from cart.';
        } else {
            // Check stock if managed
            if ($product->manage_stock && $product->stock_quantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock. Only ' . $product->stock_quantity . ' items available.',
                ], 400);
            }

            $cart->updateQuantity($product, $request->quantity);
            $message = 'Cart updated successfully.';
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'cart_count' => $cart->item_count,
            'cart_total' => number_format($cart->total_amount, 2),
        ]);
    }

    /**
     * Remove product from cart
     */
    public function remove(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to update cart.',
            ], 401);
        }

        /** @var Product $product */
        $product = Product::findOrFail($request->product_id);
        $cart = Cart::getCurrent();
        $cart->removeProduct($product);

        return response()->json([
            'success' => true,
            'message' => 'Product removed from cart.',
            'cart_count' => $cart->item_count,
            'cart_total' => number_format($cart->total_amount, 2),
        ]);
    }

    /**
     * Clear entire cart
     */
    public function clear(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to clear cart.',
            ], 401);
        }

        $cart = Cart::getCurrent();
        $cart->clear();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully.',
            'cart_count' => 0,
            'cart_total' => '0.00',
        ]);
    }

    /**
     * Get cart count for header
     */
    public function count(): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['count' => 0]);
        }

        $cart = Cart::getCurrent();

        return response()->json([
            'count' => $cart->item_count,
            'total' => number_format($cart->total_amount, 2),
        ]);
    }

    /**
     * Save cart for later (wishlist functionality)
     */
    public function saveForLater(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please log in to save items.',
            ], 401);
        }

        /** @var Product $product */
        $product = Product::findOrFail($request->product_id);
        $cart = Cart::getCurrent();

        // Remove from cart
        $cart->removeProduct($product);

        // Add to wishlist
        $user = Auth::user();
        $user->wishlist()->firstOrCreate(['product_id' => $product->id]);

        return response()->json([
            'success' => true,
            'message' => 'Product saved to wishlist.',
            'cart_count' => $cart->item_count,
            'cart_total' => number_format($cart->total_amount, 2),
        ]);
    }
}
