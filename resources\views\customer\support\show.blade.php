@extends('layouts.customer')

@section('title', 'Support Ticket #' . $ticket->ticket_number)
@section('page-title', 'Support Ticket Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Back Button -->
            <div class="mb-3">
                <a href="{{ route('customer.support.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Support Tickets
                </a>
            </div>

            <!-- Ticket Details Card -->
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="fas fa-ticket-alt me-2"></i>
                                Ticket #{{ $ticket->ticket_number }}
                            </h5>
                            <p class="text-muted mb-0">{{ $ticket->subject }}</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <span class="badge bg-{{ $ticket->status_badge_color }} fs-6">
                                {{ $ticket->formatted_status }}
                            </span>
                            <span class="badge bg-{{ $ticket->priority_badge_color }} fs-6 ms-2">
                                {{ $ticket->formatted_priority }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Ticket Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Ticket Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Ticket Number:</strong></td>
                                    <td>{{ $ticket->ticket_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Subject:</strong></td>
                                    <td>{{ $ticket->subject }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>{{ $ticket->formatted_category }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $ticket->priority_badge_color }}">
                                            {{ $ticket->formatted_priority }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ $ticket->status_badge_color }}">
                                            {{ $ticket->formatted_status }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ $ticket->created_at->format('M d, Y \a\t g:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Contact Information</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>{{ $ticket->customer_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>
                                        <a href="mailto:{{ $ticket->customer_email }}">{{ $ticket->customer_email }}</a>
                                    </td>
                                </tr>
                                @if($ticket->customer_phone)
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>
                                        <a href="tel:{{ $ticket->customer_phone }}">{{ $ticket->customer_phone }}</a>
                                    </td>
                                </tr>
                                @endif
                                @if($ticket->order_number)
                                <tr>
                                    <td><strong>Order Number:</strong></td>
                                    <td>
                                        <a href="{{ route('customer.orders.show', $ticket->order_number) }}" class="text-primary">
                                            {{ $ticket->order_number }}
                                        </a>
                                    </td>
                                </tr>
                                @endif
                                @if($ticket->parcel_tracking)
                                <tr>
                                    <td><strong>Tracking Number:</strong></td>
                                    <td>
                                        <a href="{{ route('track') }}?tracking_number={{ $ticket->parcel_tracking }}" class="text-primary">
                                            {{ $ticket->parcel_tracking }}
                                        </a>
                                    </td>
                                </tr>
                                @endif
                                @if($ticket->assignedTo)
                                <tr>
                                    <td><strong>Assigned To:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $ticket->assignedTo->name }}</span>
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-4">
                        <h6 class="text-primary mb-3">Description</h6>
                        <div class="p-3 bg-light rounded">
                            <p class="mb-0">{{ $ticket->description }}</p>
                        </div>
                    </div>

                    <!-- Resolution (if resolved) -->
                    @if($ticket->resolution)
                        <div class="mb-4">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-check-circle me-2"></i>Resolution
                            </h6>
                            <div class="p-3 bg-success bg-opacity-10 border border-success rounded">
                                <p class="mb-2">{{ $ticket->resolution }}</p>
                                @if($ticket->resolved_at)
                                    <small class="text-muted">
                                        Resolved on {{ $ticket->resolved_at->format('M d, Y \a\t g:i A') }}
                                    </small>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Admin Notes (if any) -->
                    @if($ticket->admin_notes)
                        <div class="mb-4">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-sticky-note me-2"></i>Admin Notes
                            </h6>
                            <div class="p-3 bg-info bg-opacity-10 border border-info rounded">
                                <p class="mb-0">{{ $ticket->admin_notes }}</p>
                            </div>
                        </div>
                    @endif

                    <!-- Attachments (if any) -->
                    @if($ticket->attachments && count($ticket->attachments) > 0)
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-paperclip me-2"></i>Attachments
                            </h6>
                            <div class="row">
                                @foreach($ticket->attachments as $attachment)
                                    <div class="col-md-4 mb-2">
                                        <div class="card">
                                            <div class="card-body p-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file fa-2x text-muted me-3"></i>
                                                    <div>
                                                        <h6 class="mb-0">{{ $attachment['name'] ?? 'Attachment' }}</h6>
                                                        <small class="text-muted">{{ $attachment['size'] ?? 'Unknown size' }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @if($ticket->canBeEdited())
                                <a href="{{ route('customer.support.edit', $ticket) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-2"></i>Edit Ticket
                                </a>
                            @endif
                        </div>
                        
                        <div>
                            @if($ticket->canBeClosed())
                                <form action="{{ route('customer.support.close', $ticket) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-success" 
                                            onclick="return confirm('Are you sure you want to close this ticket?')">
                                        <i class="fas fa-check me-2"></i>Close Ticket
                                    </button>
                                </form>
                            @endif
                            
                            @if($ticket->canBeReopened())
                                <form action="{{ route('customer.support.reopen', $ticket) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-warning" 
                                            onclick="return confirm('Are you sure you want to reopen this ticket?')">
                                        <i class="fas fa-redo me-2"></i>Reopen Ticket
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline (Future Enhancement) -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>Ticket Timeline
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Ticket Created</h6>
                                <p class="text-muted mb-0">{{ $ticket->created_at->format('M d, Y \a\t g:i A') }}</p>
                            </div>
                        </div>
                        
                        @if($ticket->assignedTo)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Ticket Assigned</h6>
                                    <p class="text-muted mb-0">Assigned to {{ $ticket->assignedTo->name }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($ticket->resolved_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Ticket Resolved</h6>
                                    <p class="text-muted mb-0">{{ $ticket->resolved_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($ticket->closed_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-secondary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Ticket Closed</h6>
                                    <p class="text-muted mb-0">{{ $ticket->closed_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }
</style>
@endpush
