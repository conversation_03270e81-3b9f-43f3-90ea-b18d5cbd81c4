@extends('layouts.admin')

@section('title', isset($career) ? 'Edit Career Position' : 'Create Career Position')
@section('page-title', isset($career) ? 'Edit: ' . $career->title : 'Add New Career Position')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.careers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Careers
        </a>
        @if(isset($career))
            <a href="{{ route('admin.cms.careers.show', $career) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i> View Details
            </a>
        @endif
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-8">
                <form method="POST" action="{{ isset($career) ? route('admin.cms.careers.update', $career) : route('admin.cms.careers.store') }}">
                    @if(isset($career))
                        @method('PUT')
                    @endif
                    @csrf
                    
                    <!-- Basic Information -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Basic Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="title" class="form-label">Position Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror"
                                           id="title" name="title" value="{{ old('title', $career->title ?? '') }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <input type="text" class="form-control @error('department') is-invalid @enderror" 
                                           id="department" name="department" value="{{ old('department') }}">
                                    @error('department')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                           id="location" name="location" value="{{ old('location') }}" required>
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="employment_type" class="form-label">Employment Type <span class="text-danger">*</span></label>
                                    <select class="form-control @error('employment_type') is-invalid @enderror" 
                                            id="employment_type" name="employment_type" required>
                                        <option value="">Select Type</option>
                                        <option value="full-time" {{ old('employment_type') === 'full-time' ? 'selected' : '' }}>Full-time</option>
                                        <option value="part-time" {{ old('employment_type') === 'part-time' ? 'selected' : '' }}>Part-time</option>
                                        <option value="contract" {{ old('employment_type') === 'contract' ? 'selected' : '' }}>Contract</option>
                                        <option value="internship" {{ old('employment_type') === 'internship' ? 'selected' : '' }}>Internship</option>
                                        <option value="remote" {{ old('employment_type') === 'remote' ? 'selected' : '' }}>Remote</option>
                                    </select>
                                    @error('employment_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="experience_level" class="form-label">Experience Level <span class="text-danger">*</span></label>
                                    <select class="form-control @error('experience_level') is-invalid @enderror" 
                                            id="experience_level" name="experience_level" required>
                                        <option value="">Select Level</option>
                                        <option value="entry" {{ old('experience_level') === 'entry' ? 'selected' : '' }}>Entry Level</option>
                                        <option value="mid" {{ old('experience_level') === 'mid' ? 'selected' : '' }}>Mid Level</option>
                                        <option value="senior" {{ old('experience_level') === 'senior' ? 'selected' : '' }}>Senior Level</option>
                                        <option value="executive" {{ old('experience_level') === 'executive' ? 'selected' : '' }}>Executive</option>
                                    </select>
                                    @error('experience_level')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Job Description <span class="text-danger">*</span></label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="6" required>{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Information -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Detailed Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="requirements" class="form-label">Requirements</label>
                                <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                          id="requirements" name="requirements" rows="5">{{ old('requirements') }}</textarea>
                                @error('requirements')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="responsibilities" class="form-label">Key Responsibilities</label>
                                <textarea class="form-control @error('responsibilities') is-invalid @enderror" 
                                          id="responsibilities" name="responsibilities" rows="5">{{ old('responsibilities') }}</textarea>
                                @error('responsibilities')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label for="benefits" class="form-label">Benefits & Perks</label>
                                <textarea class="form-control @error('benefits') is-invalid @enderror" 
                                          id="benefits" name="benefits" rows="4">{{ old('benefits') }}</textarea>
                                @error('benefits')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Skills -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Skills</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="required_skills" class="form-label">Required Skills</label>
                                    <textarea class="form-control @error('required_skills') is-invalid @enderror" 
                                              id="required_skills" name="required_skills_text" rows="4" 
                                              placeholder="Enter each skill on a new line">{{ old('required_skills_text') }}</textarea>
                                    @error('required_skills')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Enter each skill on a new line</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="preferred_skills" class="form-label">Preferred Skills</label>
                                    <textarea class="form-control @error('preferred_skills') is-invalid @enderror" 
                                              id="preferred_skills" name="preferred_skills_text" rows="4" 
                                              placeholder="Enter each skill on a new line">{{ old('preferred_skills_text') }}</textarea>
                                    @error('preferred_skills')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Enter each skill on a new line</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Salary & Contact -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Salary & Contact Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="salary_min" class="form-label">Minimum Salary</label>
                                    <input type="number" class="form-control @error('salary_min') is-invalid @enderror" 
                                           id="salary_min" name="salary_min" value="{{ old('salary_min') }}" min="0">
                                    @error('salary_min')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="salary_max" class="form-label">Maximum Salary</label>
                                    <input type="number" class="form-control @error('salary_max') is-invalid @enderror" 
                                           id="salary_max" name="salary_max" value="{{ old('salary_max') }}" min="0">
                                    @error('salary_max')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="salary_currency" class="form-label">Currency</label>
                                    <input type="text" class="form-control @error('salary_currency') is-invalid @enderror" 
                                           id="salary_currency" name="salary_currency" value="{{ old('salary_currency', 'USD') }}" maxlength="3">
                                    @error('salary_currency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="contact_email" class="form-label">Contact Email</label>
                                    <input type="email" class="form-control @error('contact_email') is-invalid @enderror"
                                           id="contact_email" name="contact_email" value="{{ old('contact_email') }}"
                                           placeholder="{{ $siteSettings['careers_contact_email'] ?? $siteSettings['contact_email'] ?? '<EMAIL>' }}">
                                    @error('contact_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Leave empty to use site default contact email</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="contact_phone" class="form-label">Contact Phone</label>
                                    <input type="text" class="form-control @error('contact_phone') is-invalid @enderror"
                                           id="contact_phone" name="contact_phone" value="{{ old('contact_phone') }}"
                                           placeholder="{{ $siteSettings['careers_contact_phone'] ?? $siteSettings['contact_phone'] ?? '+****************' }}">
                                    @error('contact_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Leave empty to use site default contact phone</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="application_deadline" class="form-label">Application Deadline</label>
                                <input type="date" class="form-control @error('application_deadline') is-invalid @enderror" 
                                       id="application_deadline" name="application_deadline" value="{{ old('application_deadline') }}">
                                @error('application_deadline')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                        <a href="{{ route('admin.cms.careers.index') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-1"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Create Position
                        </button>
                    </div>
                </form>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Position Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">Display Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Lower numbers appear first</small>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <strong>Active Status</strong>
                                <br><small class="text-muted">Show this position on the website</small>
                            </label>
                        </div>

                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                   value="1" {{ old('is_featured') ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_featured">
                                <strong>Featured Position</strong>
                                <br><small class="text-muted">Highlight this position</small>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Tips</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i> Use clear, descriptive job titles</li>
                            <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i> Include specific requirements and qualifications</li>
                            <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i> Highlight company benefits and culture</li>
                            <li class="mb-0"><i class="fas fa-lightbulb text-warning me-2"></i> Set realistic salary ranges</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Convert skills textarea to array before submission
    document.querySelector('form').addEventListener('submit', function(e) {
        // Required skills
        const requiredSkillsText = document.querySelector('[name="required_skills_text"]').value;
        if (requiredSkillsText) {
            const requiredSkillsArray = requiredSkillsText.split('\n').filter(skill => skill.trim() !== '');
            requiredSkillsArray.forEach((skill, index) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = `required_skills[${index}]`;
                input.value = skill.trim();
                this.appendChild(input);
            });
        }

        // Preferred skills
        const preferredSkillsText = document.querySelector('[name="preferred_skills_text"]').value;
        if (preferredSkillsText) {
            const preferredSkillsArray = preferredSkillsText.split('\n').filter(skill => skill.trim() !== '');
            preferredSkillsArray.forEach((skill, index) => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = `preferred_skills[${index}]`;
                input.value = skill.trim();
                this.appendChild(input);
            });
        }
    });
</script>
@endpush
