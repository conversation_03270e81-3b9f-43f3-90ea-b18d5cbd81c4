@extends('layouts.admin')

@section('title', 'Edit Slider')
@section('page-title', 'Edit Slider: ' . $slider->title)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.sliders.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Sliders
        </a>
        <a href="{{ route('admin.cms.sliders.show', $slider) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Details
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.cms.sliders.update', $slider) }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Slider Content</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $slider->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="subtitle" class="form-label">Subtitle</label>
                            <input type="text" class="form-control @error('subtitle') is-invalid @enderror" 
                                   id="subtitle" name="subtitle" value="{{ old('subtitle', $slider->subtitle) }}">
                            @error('subtitle')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" 
                                      placeholder="Brief description for the slider...">{{ old('description', $slider->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Maximum 1000 characters</small>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Call-to-Action Button</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="button_text" class="form-label">Button Text</label>
                                <input type="text" class="form-control @error('button_text') is-invalid @enderror" 
                                       id="button_text" name="button_text" value="{{ old('button_text', $slider->button_text) }}"
                                       placeholder="e.g., Get Started, Learn More">
                                @error('button_text')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="button_url" class="form-label">Button URL</label>
                                <input type="url" class="form-control @error('button_url') is-invalid @enderror" 
                                       id="button_url" name="button_url" value="{{ old('button_url', $slider->button_url) }}"
                                       placeholder="https://example.com or /page">
                                @error('button_url')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <small class="text-muted">Leave both fields empty if you don't want a button</small>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Display Settings</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="display_order" class="form-label">Display Order</label>
                                <input type="number" class="form-control @error('display_order') is-invalid @enderror" 
                                       id="display_order" name="display_order" value="{{ old('display_order', $slider->display_order) }}" 
                                       min="0" step="1">
                                @error('display_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', $slider->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Status</strong>
                                        <br><small class="text-muted">Show this slider on the website</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                    <a href="{{ route('admin.cms.sliders.show', $slider) }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update Slider
                    </button>
                </div>
            </form>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Desktop Image</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div id="desktop-preview" class="mb-3">
                            @if($slider->image)
                                <img src="{{ Storage::url($slider->image) }}" 
                                     alt="{{ $slider->title }}" 
                                     class="img-fluid mx-auto" 
                                     style="width: 100%; height: 200px; object-fit: cover;" id="current-desktop">
                                <div class="text-muted small mt-2">Current: {{ basename($slider->image) }}</div>
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center mx-auto" 
                                     style="width: 100%; height: 200px; border: 2px dashed #ddd;" id="default-desktop">
                                    <div class="text-center">
                                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                        <div class="text-muted">Desktop Image Preview</div>
                                    </div>
                                </div>
                            @endif
                            <img id="desktop-preview-image" src="#" alt="Desktop Preview" 
                                 class="img-fluid mx-auto" 
                                 style="width: 100%; height: 200px; object-fit: cover; display: none;">
                        </div>
                        
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*" onchange="previewImage(this, 'desktop')">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted d-block mt-2">
                            Leave empty to keep current image<br>
                            Supported formats: JPG, PNG, GIF, WebP<br>
                            Maximum size: 5MB<br>
                            Recommended: 1920x800px
                        </small>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Mobile Image (Optional)</h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div id="mobile-preview" class="mb-3">
                            @if($slider->mobile_image)
                                <img src="{{ Storage::url($slider->mobile_image) }}" 
                                     alt="{{ $slider->title }} (Mobile)" 
                                     class="img-fluid mx-auto" 
                                     style="width: 200px; height: 300px; object-fit: cover;" id="current-mobile">
                                <div class="text-muted small mt-2">Current: {{ basename($slider->mobile_image) }}</div>
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center mx-auto" 
                                     style="width: 200px; height: 300px; border: 2px dashed #ddd;" id="default-mobile">
                                    <div class="text-center">
                                        <i class="fas fa-mobile-alt fa-3x text-muted mb-2"></i>
                                        <div class="text-muted small">Mobile Image Preview</div>
                                    </div>
                                </div>
                            @endif
                            <img id="mobile-preview-image" src="#" alt="Mobile Preview" 
                                 class="img-fluid mx-auto" 
                                 style="width: 200px; height: 300px; object-fit: cover; display: none;">
                        </div>
                        
                        <input type="file" class="form-control @error('mobile_image') is-invalid @enderror" 
                               id="mobile_image" name="mobile_image" accept="image/*" onchange="previewImage(this, 'mobile')">
                        @error('mobile_image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted d-block mt-2">
                            Leave empty to keep current image<br>
                            Supported formats: JPG, PNG, GIF, WebP<br>
                            Maximum size: 5MB<br>
                            Recommended: 768x1024px
                        </small>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Slider Info</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Created:</strong>
                        <div class="text-muted">{{ $slider->created_at->format('M d, Y h:i A') }}</div>
                    </div>
                    <div class="mb-2">
                        <strong>Last Updated:</strong>
                        <div class="text-muted">{{ $slider->updated_at->format('M d, Y h:i A') }}</div>
                    </div>
                    <div class="mb-2">
                        <strong>Current Status:</strong>
                        <div>
                            <span class="badge bg-{{ $slider->is_active ? 'success' : 'secondary' }}">
                                {{ $slider->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>Display Order:</strong>
                        <div class="text-muted">{{ $slider->display_order }}</div>
                    </div>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Tips</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use high-quality images for best visual impact</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Keep text concise and action-oriented</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Mobile images ensure optimal mobile experience</small>
                        </li>
                        <li>
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <small>Use display order to control slider sequence</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function previewImage(input, type) {
        const preview = document.getElementById(`${type}-preview-image`);
        const currentImage = document.getElementById(`current-${type}`);
        const defaultPreview = document.getElementById(`default-${type}`);
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
                if (currentImage) currentImage.style.display = 'none';
                if (defaultPreview) defaultPreview.style.display = 'none';
            };
            
            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
            if (currentImage) {
                currentImage.style.display = 'block';
            } else if (defaultPreview) {
                defaultPreview.style.display = 'flex';
            }
        }
    }

    // Character counter for description
    document.getElementById('description').addEventListener('input', function() {
        const maxLength = 1000;
        const currentLength = this.value.length;
        const remaining = maxLength - currentLength;
        
        // Find or create counter element
        let counter = document.getElementById('description-counter');
        if (!counter) {
            counter = document.createElement('small');
            counter.id = 'description-counter';
            counter.className = 'text-muted';
            this.parentNode.appendChild(counter);
        }
        
        counter.textContent = `${currentLength}/${maxLength} characters`;
        counter.className = remaining < 100 ? 'text-warning' : (remaining < 50 ? 'text-danger' : 'text-muted');
    });

    // Auto-fill button URL based on button text
    document.getElementById('button_text').addEventListener('input', function() {
        const buttonUrl = document.getElementById('button_url');
        if (!buttonUrl.value && this.value) {
            const text = this.value.toLowerCase();
            if (text.includes('quote') || text.includes('get started')) {
                buttonUrl.value = '/quote';
            } else if (text.includes('track')) {
                buttonUrl.value = '/track';
            } else if (text.includes('about') || text.includes('learn more')) {
                buttonUrl.value = '/about';
            } else if (text.includes('contact')) {
                buttonUrl.value = '/contact';
            } else if (text.includes('service')) {
                buttonUrl.value = '/services';
            }
        }
    });
</script>
@endpush
