<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Quote;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CustomerQuoteTest extends TestCase
{
    use WithFaker;

    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed essential data
        $this->seed([
            \Database\Seeders\SiteSettingSeeder::class,
        ]);

        // Create a customer user
        $this->customer = User::factory()->create([
            'role' => 'customer',
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'company_name' => 'Test Company',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function customer_can_view_quote_creation_form()
    {
        $response = $this->actingAs($this->customer)
                         ->get(route('customer.quotes.create'));

        $response->assertStatus(200);
        $response->assertViewIs('customer.quotes.create');
        $response->assertViewHas(['serviceTypes', 'priorities']);
    }

    /** @test */
    public function customer_can_submit_shipping_quote_request()
    {
        $quoteData = [
            'quote_type' => 'shipping',
            'service_type' => 'domestic_shipping',
            'priority' => 'standard',
            'description' => 'Test shipping quote request',
            'origin_address' => '123 Origin St',
            'origin_city' => 'Origin City',
            'origin_state' => 'Origin State',
            'origin_postal_code' => '12345',
            'origin_country' => 'USA',
            'destination_address' => '456 Destination Ave',
            'destination_city' => 'Destination City',
            'destination_state' => 'Destination State',
            'destination_postal_code' => '67890',
            'destination_country' => 'USA',
            'package_count' => 2,
            'total_weight' => 10.5,
            'weight_unit' => 'kg',
            'delivery_speed' => 'standard',
            'package_type' => 'Box',
            'package_description' => 'Test packages',
            'declared_value' => 100.00,
            'fragile' => true,
            'insurance_required' => true,
        ];

        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.store'), $quoteData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('quotes', [
            'user_id' => $this->customer->id,
            'quote_type' => 'shipping',
            'service_type' => 'domestic_shipping',
            'customer_email' => $this->customer->email,
            'status' => 'pending'
        ]);
    }

    /** @test */
    public function customer_can_submit_product_quote_request()
    {
        // Create test products
        $category = Category::factory()->create(['name' => 'Test Category']);
        $product1 = Product::factory()->create([
            'category_id' => $category->id,
            'name' => 'Test Product 1',
            'price' => 25.99,
            'is_active' => true
        ]);
        $product2 = Product::factory()->create([
            'category_id' => $category->id,
            'name' => 'Test Product 2',
            'price' => 15.50,
            'is_active' => true
        ]);

        $products = [
            [
                'product_id' => $product1->id,
                'quantity' => 2,
                'price_at_time' => $product1->price
            ],
            [
                'product_id' => $product2->id,
                'quantity' => 1,
                'price_at_time' => $product2->price
            ]
        ];

        $quoteData = [
            'quote_type' => 'product',
            'priority' => 'standard',
            'description' => 'Test product quote request',
            'products' => json_encode($products),
            'products_total' => 67.48, // (25.99 * 2) + (15.50 * 1)
            'product_requirements' => 'Need bulk pricing'
        ];

        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.store'), $quoteData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('quotes', [
            'user_id' => $this->customer->id,
            'quote_type' => 'product',
            'service_type' => 'product_inquiry',
            'customer_email' => $this->customer->email,
            'status' => 'pending',
            'products_total' => 67.48
        ]);

        $quote = Quote::where('user_id', $this->customer->id)
                     ->where('quote_type', 'product')
                     ->first();

        $this->assertNotNull($quote->products);
        $this->assertCount(2, $quote->products);
        $this->assertEquals($product1->name, $quote->products[0]['product_name']);
    }

    /** @test */
    public function shipping_quote_validation_fails_with_missing_required_fields()
    {
        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.store'), [
                             'quote_type' => 'shipping'
                             // Missing required fields
                         ]);

        $response->assertSessionHasErrors([
            'service_type',
            'priority',
            'origin_address',
            'origin_city',
            'origin_country',
            'destination_address',
            'destination_city',
            'destination_country',
            'package_count',
            'weight_unit',
            'delivery_speed'
        ]);
    }

    /** @test */
    public function product_quote_validation_fails_with_missing_products()
    {
        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.store'), [
                             'quote_type' => 'product',
                             'priority' => 'standard'
                             // Missing products
                         ]);

        $response->assertSessionHasErrors(['products', 'products_total']);
    }

    /** @test */
    public function ajax_quote_submission_returns_json_response()
    {
        $quoteData = [
            'quote_type' => 'shipping',
            'service_type' => 'domestic_shipping',
            'priority' => 'standard',
            'description' => 'Test AJAX quote request',
            'origin_address' => '123 Origin St',
            'origin_city' => 'Origin City',
            'origin_country' => 'USA',
            'destination_address' => '456 Destination Ave',
            'destination_city' => 'Destination City',
            'destination_country' => 'USA',
            'package_count' => 1,
            'weight_unit' => 'kg',
            'delivery_speed' => 'standard'
        ];

        $response = $this->actingAs($this->customer)
                         ->postJson(route('customer.quotes.store'), $quoteData);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);
        $response->assertJsonStructure([
            'success',
            'message',
            'redirect',
            'quote_number'
        ]);
    }

    /** @test */
    public function ajax_quote_submission_returns_validation_errors()
    {
        $response = $this->actingAs($this->customer)
                         ->postJson(route('customer.quotes.store'), [
                             'quote_type' => 'shipping'
                             // Missing required fields
                         ]);

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false
        ]);
        $response->assertJsonStructure([
            'success',
            'errors'
        ]);
    }

    /** @test */
    public function customer_can_lookup_their_quotes()
    {
        // Create a quote for the customer
        $quote = Quote::factory()->create([
            'user_id' => $this->customer->id,
            'customer_email' => $this->customer->email,
            'quote_number' => 'QTE20240115001'
        ]);

        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.lookup.search'), [
                             'quote_number' => $quote->quote_number,
                             'email' => $this->customer->email
                         ]);

        $response->assertRedirect(route('customer.quotes.show', $quote));
    }

    /** @test */
    public function customer_cannot_lookup_other_customers_quotes()
    {
        $otherCustomer = User::factory()->create(['role' => 'customer']);
        
        $quote = Quote::factory()->create([
            'user_id' => $otherCustomer->id,
            'customer_email' => $otherCustomer->email,
            'quote_number' => 'QTE20240115002'
        ]);

        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.lookup.search'), [
                             'quote_number' => $quote->quote_number,
                             'email' => $otherCustomer->email
                         ]);

        $response->assertSessionHasErrors(['quote_number']);
    }

    /** @test */
    public function quote_lookup_fails_with_invalid_data()
    {
        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.lookup.search'), [
                             'quote_number' => 'INVALID123',
                             'email' => '<EMAIL>'
                         ]);

        $response->assertSessionHasErrors(['quote_number']);
    }

    /** @test */
    public function customer_can_view_their_quote_details()
    {
        $quote = Quote::factory()->create([
            'user_id' => $this->customer->id,
            'customer_email' => $this->customer->email
        ]);

        $response = $this->actingAs($this->customer)
                         ->get(route('customer.quotes.show', $quote));

        $response->assertStatus(200);
        $response->assertViewIs('customer.quotes.show');
        $response->assertViewHas('quote', $quote);
    }

    /** @test */
    public function customer_cannot_view_other_customers_quotes()
    {
        $otherCustomer = User::factory()->create(['role' => 'customer']);

        $quote = Quote::factory()->create([
            'user_id' => $otherCustomer->id,
            'customer_email' => $otherCustomer->email
        ]);

        $response = $this->actingAs($this->customer)
                         ->get(route('customer.quotes.show', $quote));

        $response->assertStatus(404);
    }

    /** @test */
    public function checkbox_fields_are_properly_validated_and_processed()
    {
        // Test with checkboxes checked (value = 1)
        $quoteDataChecked = [
            'quote_type' => 'shipping',
            'service_type' => 'domestic_shipping',
            'priority' => 'standard',
            'description' => 'Test shipping quote with checkboxes checked',
            'origin_address' => '123 Origin St',
            'origin_city' => 'Origin City',
            'origin_country' => 'USA',
            'destination_address' => '456 Destination Ave',
            'destination_city' => 'Destination City',
            'destination_country' => 'USA',
            'package_count' => 1,
            'weight_unit' => 'kg',
            'delivery_speed' => 'standard',
            'fragile' => '1',
            'hazardous' => '1',
            'insurance_required' => '1',
            'signature_required' => '1',
        ];

        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.store'), $quoteDataChecked);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $quote = Quote::where('user_id', $this->customer->id)
                     ->where('description', 'Test shipping quote with checkboxes checked')
                     ->first();

        $this->assertNotNull($quote);
        $this->assertTrue($quote->fragile);
        $this->assertTrue($quote->hazardous);
        $this->assertTrue($quote->insurance_required);
        $this->assertTrue($quote->signature_required);

        // Test with checkboxes unchecked (value = 0)
        $quoteDataUnchecked = [
            'quote_type' => 'shipping',
            'service_type' => 'domestic_shipping',
            'priority' => 'standard',
            'description' => 'Test shipping quote with checkboxes unchecked',
            'origin_address' => '123 Origin St',
            'origin_city' => 'Origin City',
            'origin_country' => 'USA',
            'destination_address' => '456 Destination Ave',
            'destination_city' => 'Destination City',
            'destination_country' => 'USA',
            'package_count' => 1,
            'weight_unit' => 'kg',
            'delivery_speed' => 'standard',
            'fragile' => '0',
            'hazardous' => '0',
            'insurance_required' => '0',
            'signature_required' => '0',
        ];

        $response = $this->actingAs($this->customer)
                         ->post(route('customer.quotes.store'), $quoteDataUnchecked);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $quote = Quote::where('user_id', $this->customer->id)
                     ->where('description', 'Test shipping quote with checkboxes unchecked')
                     ->first();

        $this->assertNotNull($quote);
        $this->assertFalse($quote->fragile);
        $this->assertFalse($quote->hazardous);
        $this->assertFalse($quote->insurance_required);
        $this->assertFalse($quote->signature_required);
    }
}
