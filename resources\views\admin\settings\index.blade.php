@extends('layouts.admin')

@section('title', 'Site Settings')
@section('page-title', 'Site Settings Management')

@section('page-actions')
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addSettingModal">
            <i class="fas fa-plus me-1"></i> Add Setting
        </button>
        <button type="button" class="btn btn-outline-warning" onclick="resetSettings()">
            <i class="fas fa-undo me-1"></i> Reset All
        </button>
        <button type="button" class="btn btn-outline-info" onclick="exportSettings()">
            <i class="fas fa-download me-1"></i> Export
        </button>
    </div>
@endsection

@section('content')
    <form method="POST" action="{{ route('admin.cms.settings.update') }}" enctype="multipart/form-data" id="settingsForm">
        @csrf

        <!-- Tab Navigation -->
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                    @php
                        $tabIcons = [
                            'general' => 'cog',
                            'branding' => 'palette',
                            'contact' => 'phone',
                            'social' => 'share-alt',
                            'seo' => 'search',
                            'seo_localization' => 'globe',
                            'email' => 'envelope',
                            'system' => 'server',
                            'trust' => 'shield-alt',
                            'analytics' => 'chart-line',
                            'payment' => 'credit-card',
                            'shipping' => 'truck',
                            'notifications' => 'bell',
                            'security' => 'lock',
                            'api' => 'code',
                            'maintenance' => 'tools'
                        ];
                        $isFirst = true;
                    @endphp

                    @foreach($groupedSettings as $groupName => $settings)
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {{ $isFirst ? 'active' : '' }}"
                                    id="{{ $groupName }}-tab"
                                    data-bs-toggle="tab"
                                    data-bs-target="#{{ $groupName }}-pane"
                                    type="button"
                                    role="tab"
                                    aria-controls="{{ $groupName }}-pane"
                                    aria-selected="{{ $isFirst ? 'true' : 'false' }}">
                                <i class="fas fa-{{ $tabIcons[$groupName] ?? 'folder' }} me-2"></i>
                                {{ ucfirst(str_replace('_', ' ', $groupName)) }}
                            </button>
                        </li>
                        @php $isFirst = false; @endphp
                    @endforeach
                </ul>
            </div>

            <!-- Tab Content -->
            <div class="card-body">
                <div class="tab-content" id="settingsTabContent">
                    @php $isFirstPane = true; @endphp
                    @foreach($groupedSettings as $groupName => $settings)
                        <div class="tab-pane fade {{ $isFirstPane ? 'show active' : '' }}"
                             id="{{ $groupName }}-pane"
                             role="tabpanel"
                             aria-labelledby="{{ $groupName }}-tab">

                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h5 class="mb-1">
                                        <i class="fas fa-{{ $tabIcons[$groupName] ?? 'folder' }} me-2 text-primary"></i>
                                        {{ ucfirst(str_replace('_', ' ', $groupName)) }} Settings
                                    </h5>
                                    <p class="text-muted mb-0">
                                        @switch($groupName)
                                            @case('general')
                                                Configure basic site information and general settings
                                                @break
                                            @case('branding')
                                                Manage your site's visual identity and branding elements
                                                @break
                                            @case('contact')
                                                Set up contact information and communication details
                                                @break
                                            @case('social')
                                                Configure social media links and sharing options
                                                @break
                                            @case('seo')
                                                Optimize your site for search engines
                                                @break
                                            @case('seo_localization')
                                                Configure international SEO and localization settings
                                                @break
                                            @case('email')
                                                Set up email configuration and SMTP settings
                                                @break
                                            @case('system')
                                                Configure system-level settings and preferences
                                                @break
                                            @case('trust')
                                                Manage trust badges and security certifications
                                                @break
                                            @default
                                                Manage {{ strtolower(str_replace('_', ' ', $groupName)) }} related settings
                                        @endswitch
                                    </p>
                                </div>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetGroup('{{ $groupName }}')">
                                    <i class="fas fa-undo me-1"></i> Reset Group
                                </button>
                            </div>

                            <div class="row">
                                @foreach($settings as $setting)
                                    @if($groupName === 'seo_localization')
                                        @include('admin.settings.partials.seo-localization-fields', ['setting' => $setting])
                                    @else
                                        <div class="col-md-6 mb-3">
                                            <label for="setting_{{ $setting->key_name }}" class="form-label">
                                                {{ $setting->label }}
                                                @if($setting->description)
                                                    <i class="fas fa-info-circle text-muted ms-1" title="{{ $setting->description }}" data-bs-toggle="tooltip"></i>
                                                @endif
                                            </label>

                                            @if($setting->type === 'text')
                                                <textarea class="form-control @error('settings.' . $setting->key_name) is-invalid @enderror"
                                                          id="setting_{{ $setting->key_name }}"
                                                          name="settings[{{ $setting->key_name }}]"
                                                          rows="3">{{ old('settings.' . $setting->key_name, $setting->value) }}</textarea>
                                            @elseif($setting->type === 'boolean')
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox"
                                                           id="setting_{{ $setting->key_name }}"
                                                           name="settings[{{ $setting->key_name }}]"
                                                           value="1"
                                                           {{ old('settings.' . $setting->key_name, $setting->value) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="setting_{{ $setting->key_name }}">
                                                        {{ $setting->value ? 'Enabled' : 'Disabled' }}
                                                    </label>
                                                </div>
                                            @elseif($setting->type === 'image')
                                                <!-- Hidden input to ensure image settings are included in form submission -->
                                                <input type="hidden" name="settings[{{ $setting->key_name }}]" value="{{ $setting->value }}">

                                                <div class="mb-2">
                                                    @if($setting->value)
                                                        <img src="{{ Storage::url($setting->value) }}"
                                                             alt="{{ $setting->label }}"
                                                             class="img-thumbnail mb-2"
                                                             style="max-height: 100px;">
                                                        <div class="text-muted small">Current: {{ basename($setting->value) }}</div>
                                                    @else
                                                        <div class="text-muted">No image uploaded</div>
                                                    @endif
                                                </div>
                                                <input type="file"
                                                       class="form-control @error('files.' . $setting->key_name) is-invalid @enderror"
                                                       id="setting_{{ $setting->key_name }}"
                                                       name="files[{{ $setting->key_name }}]"
                                                       accept="image/*"
                                                       onchange="previewImage(this, 'preview_{{ $setting->key_name }}'); logFileSelection(this, '{{ $setting->key_name }}');">
                                                <small class="text-muted">Supported formats: JPG, PNG, GIF, WebP (Max: 5MB) - Images will be automatically converted to WebP, favicons to ICO</small>
                                                @if($errors->has('files.' . $setting->key_name))
                                                    <div class="invalid-feedback">{{ $errors->first('files.' . $setting->key_name) }}</div>
                                                @endif
                                                <!-- Preview container for new image -->
                                                <div id="preview_{{ $setting->key_name }}" class="mt-2" style="display: none;">
                                                    <small class="text-muted">New image preview:</small><br>
                                                    <img class="img-thumbnail" style="max-height: 100px;">
                                                </div>
                                            @elseif($setting->type === 'json')
                                                <textarea class="form-control @error('settings.' . $setting->key_name) is-invalid @enderror"
                                                          id="setting_{{ $setting->key_name }}"
                                                          name="settings[{{ $setting->key_name }}]"
                                                          rows="5"
                                                          placeholder='{"key": "value"}'>{{ old('settings.' . $setting->key_name, $setting->value) }}</textarea>
                                                <small class="text-muted">Enter valid JSON format</small>
                                            @else
                                                <input type="{{ $setting->type === 'integer' ? 'number' : 'text' }}"
                                                       class="form-control @error('settings.' . $setting->key_name) is-invalid @enderror"
                                                       id="setting_{{ $setting->key_name }}"
                                                       name="settings[{{ $setting->key_name }}]"
                                                       value="{{ old('settings.' . $setting->key_name, $setting->value) }}"
                                                       {{ $setting->type === 'integer' ? 'step=1' : '' }}>
                                            @endif

                                            @error('settings.' . $setting->key_name)
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror

                                            <div class="d-flex justify-content-between align-items-center mt-1">
                                                <small class="text-muted">Key: {{ $setting->key_name }}</small>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteSetting('{{ $setting->id }}', '{{ $setting->key_name }}')"
                                                        title="Delete Setting">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                        @php $isFirstPane = false; @endphp
                    @endforeach
                </div>
            </div>
        </div>

        @if($groupedSettings->isEmpty())
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-cog fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No Settings Found</h5>
                    <p class="text-muted">Get started by adding your first site setting.</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSettingModal">
                        <i class="fas fa-plus me-1"></i> Add First Setting
                    </button>
                </div>
            </div>
        @else
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i> Save All Settings
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-lg" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i> Reset Form
                        </button>
                    </div>
                </div>
            </div>
        @endif
    </form>

    <!-- Add Setting Modal -->
    <div class="modal fade" id="addSettingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Setting</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="{{ route('admin.cms.settings.store') }}">
                    @csrf
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="key_name" class="form-label">Key Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="key_name" name="key_name" required 
                                       placeholder="e.g., site_name">
                                <small class="text-muted">Use lowercase with underscores</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="label" class="form-label">Display Label <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="label" name="label" required 
                                       placeholder="e.g., Site Name">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">Select Type</option>
                                    <option value="string">Text (String)</option>
                                    <option value="text">Textarea</option>
                                    <option value="boolean">Boolean (On/Off)</option>
                                    <option value="integer">Number</option>
                                    <option value="image">Image Upload</option>
                                    <option value="json">JSON Data</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="group_name" class="form-label">Group <span class="text-danger">*</span></label>
                                <select class="form-select" id="group_name" name="group_name" required>
                                    <option value="">Select Group</option>
                                    <option value="general">General</option>
                                    <option value="branding">Branding</option>
                                    <option value="contact">Contact</option>
                                    <option value="social">Social Media</option>
                                    <option value="seo">SEO</option>
                                    <option value="seo_localization">SEO Localization</option>
                                    <option value="email">Email</option>
                                    <option value="system">System</option>
                                    <option value="trust">Trust & Security</option>
                                    <option value="analytics">Analytics</option>
                                    <option value="payment">Payment</option>
                                    <option value="shipping">Shipping</option>
                                    <option value="notifications">Notifications</option>
                                    <option value="security">Security</option>
                                    <option value="api">API</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="2" 
                                      placeholder="Brief description of this setting"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="value" class="form-label">Default Value</label>
                            <input type="text" class="form-control" id="value" name="value" 
                                   placeholder="Enter default value">
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_public" name="is_public" value="1">
                            <label class="form-check-label" for="is_public">
                                Public Setting (accessible in frontend)
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Setting
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the setting <strong id="deleteSettingName"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Setting</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Initialize tooltips
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    function deleteSetting(settingId, settingName) {
        document.getElementById('deleteSettingName').textContent = settingName;
        document.getElementById('deleteForm').action = `/admin/cms/settings/${settingId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function resetSettings() {
        if (confirm('Are you sure you want to reset ALL settings to their default values? This will clear all custom configurations.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/cms/settings/reset';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            document.body.appendChild(form);
            form.submit();
        }
    }

    function resetGroup(groupName) {
        if (confirm(`Are you sure you want to reset all settings in the "${groupName}" group?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/cms/settings/reset';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            const groupInput = document.createElement('input');
            groupInput.type = 'hidden';
            groupInput.name = 'group';
            groupInput.value = groupName;
            form.appendChild(groupInput);

            document.body.appendChild(form);
            form.submit();
        }
    }

    function resetForm() {
        if (confirm('Are you sure you want to reset the form to its original values?')) {
            document.getElementById('settingsForm').reset();
        }
    }

    function exportSettings() {
        // Create a simple export of current settings
        const settings = {};
        const inputs = document.querySelectorAll('#settingsForm input, #settingsForm textarea, #settingsForm select');
        
        inputs.forEach(input => {
            if (input.name.startsWith('settings[')) {
                const key = input.name.match(/settings\[(.+)\]/)[1];
                if (input.type === 'checkbox') {
                    settings[key] = input.checked;
                } else {
                    settings[key] = input.value;
                }
            }
        });

        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'site_settings_' + new Date().toISOString().split('T')[0] + '.json';
        link.click();
        URL.revokeObjectURL(url);
    }

    // Auto-generate key name from label
    document.getElementById('label').addEventListener('input', function() {
        const keyInput = document.getElementById('key_name');
        if (!keyInput.value) {
            keyInput.value = this.value.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '_');
        }
    });

    // Image preview function
    function previewImage(input, previewId) {
        const previewContainer = document.getElementById(previewId);
        const previewImg = previewContainer.querySelector('img');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                previewImg.src = e.target.result;
                previewContainer.style.display = 'block';
            };

            reader.readAsDataURL(input.files[0]);
        } else {
            previewContainer.style.display = 'none';
        }
    }

    // Log file selection for debugging
    function logFileSelection(input, settingKey) {
        if (input.files && input.files[0]) {
            console.log('File selected for ' + settingKey + ':', {
                name: input.files[0].name,
                size: input.files[0].size,
                type: input.files[0].type,
                lastModified: input.files[0].lastModified
            });
        } else {
            console.log('No file selected for ' + settingKey);
        }
    }

    // Add form submission debugging
    document.getElementById('settingsForm').addEventListener('submit', function(e) {
        console.log('Form submission started');

        // Log all file inputs
        const fileInputs = this.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            if (input.files && input.files[0]) {
                console.log('File to upload:', input.name, input.files[0].name);
            }
        });

        // Log form data
        const formData = new FormData(this);
        console.log('Form data entries:');
        for (let [key, value] of formData.entries()) {
            if (value instanceof File) {
                console.log(key + ': File - ' + value.name + ' (' + value.size + ' bytes)');
            } else {
                console.log(key + ': ' + value);
            }
        }
    });
</script>
@endpush
